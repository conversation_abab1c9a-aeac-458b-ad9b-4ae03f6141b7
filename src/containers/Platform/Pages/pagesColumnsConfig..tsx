import { Typography } from '@aidsupply/components'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import ReactTexty from '../../../lib/react-texty'

export const pagesColumnsConfig = (
  lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    getNameByMode({
      t,
      dataKey: `translations.${lng}`,
      option: 'state',
      key: 'title',
      tableMode: tableMode || 'table',
      lng
    }),
    {
      key: 'linkToPage',
      dataKey: 'link',
      sortable: true,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'organization',
      sortable: true,
      dataKey: 'organization_id',
      labelKey: 'organization',
      optionsKeys: ['organizations'],
      noTranslation: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: ({ rowData, container }: any) => {
        const orgs = container?.props?.cellProps?.system?.organizations || {}
        const org = orgs[rowData.organization_id] || Object.values(orgs).find((o: any) => o.id === rowData.organization_id)
        const orgName = org?.name || 'N/A'
        return (
          <Typography as={ReactTexty}>{orgName}</Typography>
        )
      },
      headerRenderer: headerRenderer(t, tableMode),
    }
  ]
}
