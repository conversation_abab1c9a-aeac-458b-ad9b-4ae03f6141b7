import { useTranslation } from 'react-i18next'
import { Link } from 'react-router'
import clsx from 'clsx'
import { useTheme } from 'styled-components'
import { Icon, UiLink, Typography } from '@aidsupply/components'
import { sortObjectToUrlString } from '../../utils/table'
import { StyledMenuItem } from './styled'

interface IMenuItem {
  iconName?: string
  iconProps: Record<string, string>
  text: string
  route?: string
  isActive?: boolean
  isActiveParent?: boolean
  className?: string
  to?: string
  onClick?: () => void
  isExtended: boolean
  isMobile: boolean
  sortObject?: Record<string, 'asc' | 'desc'>
}

const MenuItem = ({
  iconName,
  iconProps = {},
  text,
  route,
  isActive,
  isActiveParent,
  className,
  to,
  onClick,
  isExtended,
  isMobile,
  sortObject
}: IMenuItem) => {
  const { t } = useTranslation('menu')
  const theme = useTheme()
  const routeWithSortString = sortObject
    ? `${route || to}?sort=${sortObjectToUrlString(sortObject)}`
    : route || to

  const iconFill = isActive || isActiveParent ? theme.color.general.light : theme.color.general.dark

  return (
    <UiLink
      className={clsx(className, 'menuItem')}
      to={routeWithSortString}
      onClick={onClick}
      Link={Link}
      withHoverForTypographyOnly={isExtended}
    >
      <StyledMenuItem
        className={clsx(isExtended ? 'extendedMenu' : 'condensedMenu', isActive && 'active')}
      >
        <Icon
          name={iconName}
          className={clsx(iconName, 'menuItemIcon')}
          fill={iconFill}
          strokeWidth={1.4}
          stroke={iconFill}
          {...iconProps}
        />
        <Typography
          type={isMobile ? 'button2' : 'button1'}
          text={t(text)}
          className={clsx(isExtended ? 'extendedItem' : 'condensedItem')}
        />
      </StyledMenuItem>
    </UiLink>
  )
}

export default MenuItem
