import { useEffect } from 'react';

const useClickOutside = (
  ref: { current: HTMLElement | null },
  callback: () => void,
  ignoredSelector = '.ignore-click-outside'
) => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      const clickedInside = ref.current?.contains(target);
      const clickedIgnored = target.closest(ignoredSelector);

      if (!clickedInside && !clickedIgnored) {
        callback();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [callback, ref, ignoredSelector]);
};

export default useClickOutside
