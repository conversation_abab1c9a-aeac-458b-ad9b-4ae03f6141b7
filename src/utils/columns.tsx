import { Icon, Tag, Tooltip, Typography } from '@aidsupply/components'
import { STATES_ICONS } from '../components/config/table'
import { UserRole } from '../redux-saga/reducers/user'
import { USER_ROLES } from '../components/NavMenu/config'
import { headerRenderer } from './table'
import { TableModeType } from '../redux-saga/reducers/data'
import theme from '../theme'
import ReactTexty from '../lib/react-texty'
import { StyledContentWrapper } from './styled'

export const getStateColumnProps = (t: (key: string) => string, tableMode: 'table' | 'cards' | 'list') => {
  return {
    key: 'state',
    dataKey: 'state',
    title: 'State',
    width: 35,
    flexShrink: 0,
    resizable: false,
    sortable: true,
    cellRenderer: ({ cellData }: { cellData: string | { id: string }}) => {
      const id = typeof cellData === 'object' ? cellData.id : cellData
      return STATES_ICONS[id] ? (
        <Tooltip
          left={tableMode === 'table' ? '8px' : undefined}
          right={tableMode === 'table' ? undefined : '8px'}
          text={t(`${cellData}`)}
          arrowPosition={tableMode === 'table' ? 'left' : 'right'}
        >
          <Icon
            className="stateIcon"
            onClick={() => {}}
            name={STATES_ICONS[id]}
            width={14}
            height={14}
            padding="10px"
          />
        </Tooltip>
      ) : (
        ''
      )
    },
    headerRenderer: () =>
      tableMode !== 'list' ? <Icon title={t('state')} name="status" wrapperWidth="100%" /> : '',
  }
}

export const getRole = (roleKey: string) => {
  const roleObject = USER_ROLES?.[roleKey]
  const label = roleObject?.label?.['en']
  
  if (!label) {
    return roleKey?.charAt(0).toUpperCase() + roleKey?.slice(1)
  }

  return label
    .split(' ')
    .map((item: string) => item.charAt(0).toUpperCase())
    .join('')
}

export const getName = (t: (key: string) => string, tableMode: 'table' | 'cards' | 'list') => ({
  key: 'name',
  dataKey: 'name',
  title: 'name',
  sortable: true,
  width: 0,
  flexGrow: 1,
  headerRenderer: headerRenderer(t, tableMode),
})

export const getOrganization = (t: (key: string) => string, sortable: boolean, tableMode?: TableModeType, withRoles?: boolean) => {
  const organizationConfig = {
    key: 'organization',
    dataKey: 'organization_name',
    width: 0,
    flexGrow: 2,
    sortable,
    noTranslation: true,
    headerRenderer: headerRenderer(t, tableMode),
  }

  if (tableMode === 'table') {
    return {
      ...organizationConfig,
      cellRenderer: ({
        rowData,
      }: {
        rowData: {name?: string; full_name?: string; status?: string | { id: string }, role: UserRole , organization_name: string, id: number};
      }) => {
        return (
        <StyledContentWrapper>
          {withRoles && 
            <Tooltip
              textColor={theme.color.general.light}
              text={USER_ROLES?.[rowData.role]?.label?.['en']}
              left="5px"
              arrowPosition="left"
              padding="15px 15px"
              position="bottom"
            >
              <Tag key={rowData.id} text={getRole(rowData.role)} backgroundColor={theme.color.primary.main}/>{' '}
            </Tooltip>
          }
          <Typography as={ReactTexty}
            type="button1"
            fontWeight="400"
          >
            {rowData.organization_name}
            {!withRoles && rowData.organization_name}
          </Typography> 
        </StyledContentWrapper>
        )
      }
    }
  } else {
    return {
      ...organizationConfig
    }
  }
}
