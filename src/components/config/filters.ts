import { ALL_PLATFORMS_LANGUAGES } from '@aidsupply/components'
import { LANGUAGES_FULL_NAME, TLanguages } from '../../locales'
import { PROVIDERS } from '.'

interface FilterConfig {
  apiKey: string
  label: {
    uk: string,
    en: string
  }
  type: string
  key?: string
  options?: { id: string; label: {uk: string, en: string} | string}[]
  systemDataKeyMapping?: Record<string, string>
}

export const FILTERS_CONFIG: Record<string, FilterConfig> = {
  brand: {
    key: 'brands',
    apiKey: 'brand_id',
    label: {
      uk: 'Бренд',
      en: 'Brand',
    },
    type: 'checkboxList',
  },
  carrier: {
    key: 'carriers',
    apiKey: 'general.carrier.id',
    label: {
      uk: 'Перевізник',
      en: 'Carrier',
    },
    type: 'checkboxList',
  },
  category: {
    key: 'categories',
    apiKey: 'category_id',
    label: {
      uk: 'Категорія',
      en: 'Category',
    },
    type: 'checkboxList',
  },
  client: {
    key: 'organizations',
    api<PERSON>ey: 'general.client.id',
    label: {
      uk: 'Клієнт',
      en: 'Client',
    },
    type: 'checkboxList',
  },
  organization: {
    key: 'organizations',
    apiKey: 'organization_id',
    label: {
      uk: 'Організація',
      en: 'Organization',
    },
    type: 'checkboxList',
  },
  country: {
    key: 'countries',
    apiKey: 'country_id',
    label: {
      uk: 'Країна',
      en: 'Country',
    },
    type: 'checkboxList',
  },
  inquirer_oid: {
    key: 'organizations',
    apiKey: 'inquirer_oid',
    label: { uk: 'Запитувач', en: 'Inquirer' },
    type: 'checkboxList',
  },
  language: {
    type: 'checkboxList',
    key: '',
    apiKey: 'general.language',
    options: ALL_PLATFORMS_LANGUAGES.map((lng) => ({ id: lng, label: LANGUAGES_FULL_NAME[lng as TLanguages] })),
    label: {
      uk: 'Мова',
      en: 'Language',
    },
  },
  logist_oid: {
    key: 'carriers',
    apiKey: 'logist_oid',
    label: {
      uk: 'Перевізник',
      en: 'Carrier',
    },
    type: 'checkboxList',
  },
  magnitude: {
    key: 'magnitudes',
    type: 'checkboxList',
    apiKey: 'magnitude',
    label: {
      en: 'Magnitude',
      uk: 'Величина',
    },
  },
  merchant: {
    key: 'organizations',
    type: 'checkboxList',
    apiKey: 'general.merchant.id',
    label: {
      uk: 'Продавець',
      en: 'Merchant',
    },
  },
  platform: {
    key: 'platforms',
    apiKey: 'general.platform.id',
    label: {
      uk: 'Платформа',
      en: 'Platform',
    },
    type: 'checkboxList',
  },
  platforms: {
    key: 'platforms',
    apiKey: 'general.platforms',
    label: {
      uk: 'Платформа',
      en: 'Platform',
    },
    type: 'checkboxList',
  },
  price: {
    apiKey: 'price',
    label: {
      uk: 'Ціна',
      en: 'Price',
    },
    type: 'checkboxList',
  },
  provider: {
    key: '',
    options: PROVIDERS,
    type: 'checkboxList',
    apiKey: 'provider',
    label: {
      uk: 'Провайдер',
      en: 'Provider',
    },
  },
  recipient: {
    key: 'organizations',
    apiKey: 'recipient_id',
    label: { uk: 'Отримувач', en: 'Recipient' },
    type: 'checkboxList',
  },
  role: {
    type: 'checkboxList',
    key: 'userRoles',
    apiKey: 'role',
    label: {
      uk: 'Роль',
      en: 'Role',
    },
  },
  roles: {
    type: 'checkboxList',
    key: 'organizationRoles',
    apiKey: 'roles',
    label: {
      uk: 'Роль',
      en: 'Role',
    },
  },
  state: {
    key: 'states',
    apiKey: 'state',
    label: {
      uk: 'Стан',
      en: 'State',
    },
    type: 'checkboxList',
  },
  status: {
    key: '',
    systemDataKeyMapping: {
      inquiries: 'inquiryStatuses',
      invoices: 'invoiceStatuses',
      shipments: 'shipmentStatuses',
      users: 'orgOrUsersStatuses',
      organizations: 'orgOrUsersStatuses',
      campaigns: 'campaignStatuses',
      orders: 'orderStatuses',
      inventories: 'inventoryStatuses',
    },
    apiKey: 'status',
    label: {
      uk: 'Статус',
      en: 'Status',
    },
    type: 'checkboxList',
  },
  supplier: {
    key: 'organizations',
    type: 'checkboxList',
    apiKey: 'supplier_id',
    label: {
      uk: 'Постачальник',
      en: 'Supplier',
    },
  },
  warehouse: {
    key: 'warehouses',
    type: 'checkboxList',
    apiKey: 'warehouse_id',
    label: {
      uk: 'Склад',
      en: 'Warehouse',
    },
  },
  type: {
    key: '',
    systemDataKeyMapping: {
      organizations: 'organizationTypes',
      warehouses: 'warehouseTypes',
      pages: 'pageTypes',
      contracts: 'contractTypes',
      inquiries: 'organizationTypes',
    },
    type: 'checkboxList',
    apiKey: 'type',
    label: {
      uk: 'Тип',
      en: 'Type',
    },
  },
  attributeType: {
    key: 'attributeTypes',
    apiKey: 'attribute_type',
    label: {
      uk: 'Тип атрибуту',
      en: 'Attribute type',
    },
    type: 'checkboxList',
  },
  organizationType: {
    key: 'organizationTypes',
    apiKey: 'recipient_organization_type',
    label: {
      uk: 'Тип організації',
      en: 'Organization type',
    },
    type: 'checkboxList',
  },
  region: {
    key: 'country_subdivisions',
    apiKey: 'recipient_region',
    label: {
      uk: 'Регіон',
      en: 'Region',
    },
    type: 'checkboxList',
  },
  topic: {
    key: 'topics',
    apiKey: 'topic_id',
    label: {
      uk: 'Тема',
      en: 'Topic',
    },
    type: 'checkboxList',
  },
  visibility: {
    key: 'visibilityTypes',
    apiKey: 'visibility',
    label: {
      uk: 'Видимість',
      en: 'Visibility',
    },
    type: 'checkboxList',
  },
}

export const FILTERS_KEY_MAPPINGS: Record<string, string> = {
  'general.client.id': 'client',
  'general.customer.id': 'customer',
  'general.merchant.id': 'merchant',
  'general.platforms': 'platforms',
  'general.platform.id': 'platform',
  'general.language': 'language',
  'general.stock.id': 'stock',
  'inquirer_oid.id': 'inquirer_oid',
  'logist_oid.id': 'logist_oid',
  attribute_type: 'attributeType',
  brand_id: 'brand',
  category_id: 'category',
  country_id: 'country',
  organization_id: 'organization',
  recipient_id: 'recipient',
  recipient_organization_type: 'organizationType',
  recipient_region: 'region',
  topic_id: 'topic',
  visibility: 'visibility',
  warehouse_id: 'warehouse',
  supplier_id: 'supplier',
}
