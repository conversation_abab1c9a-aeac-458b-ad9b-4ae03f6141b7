import { useTheme } from 'styled-components'
import { Icon, Tooltip } from '@aidsupply/components'

interface IIconWithTooltip {
  text: string
  iconName: string
  iconColor?: string
  arrowPosition?: 'left' | 'right'
}

const IconWithTooltip = (props: IIconWithTooltip) => {
  const theme = useTheme()

  const { text, iconName, iconColor, arrowPosition } = props

  return(
    <Tooltip
      textColor={theme.color.general.light}
      text={text}
      right="5px"
      arrowPosition={arrowPosition || 'right'}
      padding="15px 15px"
      position="bottom"
    >
      <Icon name={iconName} stroke={iconColor} width="16px" height="16px" />
    </Tooltip>
  )
}

export default IconWithTooltip
