import styled from 'styled-components'

export const StyledUserProfile = styled.div`
  max-width: 880px;
  margin: 30px auto;
  width: 70%;

  .rowsForm {
    padding: 20px 0 5px 0;

    form {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      gap: 20px;

      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px)  {
        flex-direction: column;
        gap: 0;
      }

      .formItem {
        width: 100%;
      }
    }
  }

  .submitFormButton {
    height: 40px;
    margin: 0 0 20px 0;

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px)  {
      width: 100%;
    }
  }

  .h4 {
    text-transform: uppercase;
    letter-spacing: 2px;
    margin: 40px 0 0 0;
    padding: 0 0 20px 0;
    border-bottom: 1px solid ${({ theme }) => theme.color.general.gray1};
  }

  .logoutButton {
    margin: 20px 0;

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px)  {
      margin: 20px 0 60px;
    }
  }
`
