import { NavigateFunction, Location } from 'react-router'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { ICredentials } from '../../containers/SignUp/FormSignUp'
import { Status } from '../../commonTypes'

export type UserRole = 'system' | 'administrator' | 'organization_owner' | 'organization_admin' | 'organization_member' | 'recipient' | 'client'

export interface ICurrentUser extends Record<string, unknown>{
  email?: string
  email_verified?: boolean
  full_name?: string
  id?: number
  organization_id?: number | null
  organization_name?: string | null
  password?: string | null
  profile_image_id?: number | null
  profile_pic?: string | null
  role?: UserRole
  status?: Status
  supervised_organization_ids?: (number | null)[];
}

export interface IUser {
  details: ICurrentUser
  inProgress: boolean,
  error: boolean,
  isAuthenticated: boolean | null,
  isAppInitialized: boolean,
  token: string,
  email: string,
  password: string,
}

export const userSlice = createSlice({
  name: 'user',
  initialState:  {
    details: {},
    inProgress: false,
    error: false,
    isAuthenticated: null,
    isAppInitialized: false,
    token: '',
    email: '',
    password: '',
  },
  reducers: {
    initializeApp(state: IUser, _action: PayloadAction<{ navigate: NavigateFunction, location: Location }>) {
      state.isAppInitialized = true
    },
    signIn(state: IUser, action: PayloadAction<{ email: string; password: string }>) {
      state.inProgress = true
      state.email = action.payload.email as string
    },
    signUp(state: IUser, _action: PayloadAction<Omit<ICredentials, 'repeat_password'> & { navigate: NavigateFunction }>) {
      state.inProgress = true
      state.error = false
    },
    signInSuccess(state: IUser, action: PayloadAction<Record<string, string>>) {
      state.isAuthenticated = true
      state.inProgress = false
      state.error = false
      state.token = action.payload.token
    },
    signUpSuccess(state: IUser, action: PayloadAction<Record<string, unknown>>) {
      state.inProgress = false
      state.error = false
      state.email = action.payload.email as string
    },
    signInError(state: IUser) {
      state.isAuthenticated = false
      state.inProgress = false
      state.error = true
    },
    signUpError(state: IUser) {
      state.inProgress = false;
      state.error = true;
    },
    getCurrentUser(state: IUser, _action: PayloadAction<{ navigate: NavigateFunction, location: Location }>) {
      state.inProgress = true
    },
    getCurrentUserSuccess(state: IUser, action: PayloadAction<ICurrentUser>) {
      state.details = { ...action.payload }
      state.email = action.payload.email as string
      state.inProgress = false
    },
    getCurrentUserError(state: IUser) {
      state.inProgress = false
    },
    signOut(state: IUser) {
      state.details = {}
      state.isAuthenticated = null
      state.isAppInitialized = false
      state.inProgress = false
      state.error = false
      state.token = ''
      state.email = ''
    },
    userResetPassword(state: IUser, _action: PayloadAction<{
      password_old: string, password_new: string, navigate: NavigateFunction
    }>) {
      state.inProgress = true
    },
    userResetPasswordSuccess(state: IUser) {
      state.inProgress = false
    },
    userResetPasswordError(state: IUser) {
      state.inProgress = false
    },
    userUpdatedSuccess(state: IUser, action: PayloadAction<ICurrentUser>) {
      state.details.status = action.payload.status
    },
    userUpdateName(state: IUser, action: PayloadAction<ICurrentUser>) {
      state.details.full_name = action.payload.full_name
    },
    userRequestResetPassword(state: IUser, _action: PayloadAction<{
      email: string, navigate: NavigateFunction
    }>) {
      state.inProgress = true
      state.error = false
    },
    userRequestResetPasswordSuccess(state: IUser) {
      state.inProgress = false
    },
    userRequestResetPasswordError(state: IUser) {
      state.inProgress = false
      state.error = true
    }
  }
});

export const {
  initializeApp,
  signIn,
  signUp,
  signInSuccess,
  signUpSuccess,
  signInError,
  signUpError,
  getCurrentUser,
  getCurrentUserSuccess,
  getCurrentUserError,
  signOut,
  userResetPassword,
  userResetPasswordSuccess,
  userResetPasswordError,
  userUpdatedSuccess,
  userUpdateName,
  userRequestResetPassword,
  userRequestResetPasswordSuccess,
  userRequestResetPasswordError
} = userSlice.actions;
export default userSlice.reducer;
