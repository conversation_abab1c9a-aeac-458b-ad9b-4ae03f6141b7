import { getDate, getSelectCellValue } from '../../../components/config/columns'
import AddressTableCell from '../../../components/Table/components/AddressTableCell'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { TLanguages } from '../../../locales'
import { getNameByMode, headerRenderer } from '../../../utils/table'

export const organizationsColumnsConfig = (
  lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    getNameByMode({
      t,
      dataKey: 'name',
      option: 'status',
      tableMode: tableMode || 'table',
      lng
    }),
    {
      key: 'type',
      title: t('Type'),
      dataKey: 'type',
      optionsKeys: ['organizationTypes'],
      sortable: true,
      width: 0,
      flexGrow: 1,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: getSelectCellValue,
    },
    {
      key: 'roles',
      dataKey: 'roles',
      optionsKeys: ['organizationRoles'],
      isMulti: true,
      labelKey: 'label',
      sortable: false,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: getSelectCellValue,
    },
    {
      key: 'address',
      labelKey: `translations.${lng}`,
      dataKey: 'address',
      width: 0,
      flexGrow: 1.5,
      sortable: true,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({ rowData }: { rowData: Record<string, unknown> }) => (
        <AddressTableCell rowData={rowData} />
      )
    },
    getDate(t, tableMode)
  ];
}
