import { useTranslation } from 'react-i18next'
import { Typography } from '@aidsupply/components'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty/ReactTexty'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StateType } from '../../../components/Form/interfaces'
import { Status } from '../../../commonTypes'
import { StyledStateWrapper } from '../styled'

interface IPagesTableCard {
  id: number
  status: Status
  name: string
  state: StateType
  organization_id: number
}

const PagesTableCard = ({ data, className }: {
  data: IPagesTableCard
  className?: string
}) => {
  const { t } = useTranslation('table')
  const { id, state, organization_id, name } = data

  return (
    <TableCardStyled key={id} direction='column' gap={1} className={className}>
      <StyledStateWrapper>
        <Typography as={ReactTexty} type="h4">
          {name}
        </Typography>
        {state && <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />}
      </StyledStateWrapper>
        <Typography as={ReactTexty} type="button2" fontWeight="400">
          {organization_id || ''}
        </Typography>
    </TableCardStyled>
  )
}

export default PagesTableCard
