import { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { TFunction } from 'i18next'

import { StyledSummaryCell, StyledTableSummary } from '../styled'
import { selectFormValueByKey, selectFormValuesWhole } from '../../../redux-saga/selectors.js'
import { getTableSummary, ISummaryItem } from '../../../utils/tableSummary.js'
import { IStore } from '../../../configureStore.ts'

const TableSummary = ({
  headerHeight,
  isReadOnly,
  optionsData,
  t,
  type,
  visibleData,
}: {
  headerHeight?: number
  isReadOnly?: boolean
  optionsData?: Record<string, unknown>
  t: TFunction
  type: string
  visibleData: unknown[]
}) => {
  const pageType = type.split('.')[0]
  const blockKey = type.split('.')[1]
  const additionalFormValues = useSelector((state: IStore) => selectFormValuesWhole(state, pageType))

  const visibleDataLength = visibleData?.length
  const editableTableValues = useSelector((state: IStore) => selectFormValueByKey(state, pageType, blockKey))

  const visibleDataWithValuesChanged = isReadOnly ? visibleData : editableTableValues?.slice(0, visibleDataLength)
  const summaryData =
    visibleDataWithValuesChanged &&
    getTableSummary(type, t, visibleDataWithValuesChanged, {
      state: additionalFormValues?.state,
      currency:
        additionalFormValues?.currency_id?.symbol ||
        // @ts-ignore
        optionsData?.currencies?.[additionalFormValues?.currency_id]?.symbol,
    })

  useEffect(() => {
    if (summaryData) {
      document.querySelector('.tableContent')?.classList.add('withSummary')
    }
  }, [summaryData])

  if (!summaryData) {
    return null
  }

  return (
    <>
      <StyledTableSummary headerHeight={headerHeight} className="summary">
        {summaryData.title && <div className="summaryTitle">{summaryData.title}</div>}
        {summaryData.data.map((item: ISummaryItem, i: number) => (
          <StyledSummaryCell
            className="summaryCell"
            key={i}
            width={item.width}
            flexGrow={item.flexGrow}
            padding={item.padding}
          >
            {item.label && <div className="summaryLabel">{item.label}:</div>}
            <div className="summaryValue">{item.value}</div>
          </StyledSummaryCell>
        ))}
      </StyledTableSummary>
    </>
  )
}

export default TableSummary
