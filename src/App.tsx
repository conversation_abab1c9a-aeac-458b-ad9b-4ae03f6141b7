import { Suspense, useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import Cookies from 'universal-cookie'
import Routing from './Routing'
import { useMappedState } from './hooks'
import { selectUserDetails } from './redux-saga/selectors'
import { getNotifications } from './redux-saga/reducers/common'
import { ICurrentUser } from './redux-saga/reducers/user'
import { DEFAULT_LANGUAGE, SUPPORTED_LNGS } from './locales'

function App() {
  const dispatch = useDispatch()
  const { i18n } = useTranslation()
  const cookies = new Cookies()

  const details = useMappedState(selectUserDetails) as ICurrentUser

  useEffect(() => {
    const lngCookies = cookies.get('lng')
    if (lngCookies !== undefined) {
      i18n.changeLanguage(SUPPORTED_LNGS.includes(lngCookies) ? lngCookies : DEFAULT_LANGUAGE)
    }
  }, [i18n])

  // TODO: remove or change logic for TEST notification 
  useEffect(() => {
    if (details.role === 'system') {
      dispatch(getNotifications({
        message: 'Notifications TEST',
        organization_id: details.organization_id as number,
        user_id: details.id as number
      }))
    }
  }, [details.role, details.id, details.organization_id])

  return (
    <Suspense fallback={'...loading'}>
      <Routing />
    </Suspense>
  )
}

export default App
