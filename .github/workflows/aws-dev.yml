env:
  # [IMAGEKIT]
  VITE_IMAGEKIT_ID: "k5txkj3a0"
  # [FRONT-END]
  VITE_API_HOST: "https://api-dev.aidsupply.org"
  # [Build Path]
  BUILD_PATH: "build/"
  # [AWS]
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_REGION: "eu-central-1"
  # [CloudFront]
  CLOUDFRONT_DISTRIBUTION_ID: "E28WN1UVXFN2OG"
  # [S3]
  S3_BUCKET_NAME: "crm-dev.goodzyk.com"

name: "[DEV] Deploy"
on:
  push:
    branches: [ "master" ]
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  # Job 1: Deploy master branch to root
  deploy-master:
    if: github.ref == 'refs/heads/master'
    name: Deploy Master
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install dependencies
        run: npm install

      - name: Build
        run: npm run build

      - name: Display structure
        run: ls -R
        working-directory: ${{ env.BUILD_PATH }}

      - name: Deploy to S3 /crm
        run: |
          aws s3 sync ${{ env.BUILD_PATH }} s3://${{ env.S3_BUCKET_NAME }}/crm/ --delete

      - name: Invalidate CloudFront cache for master
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/crm/*"

      - name: Output QA URL
        run: |
          echo "✅ Preview URL: https://crm.dev.goodzyk.com"

  # Job 2: Deploy PR branches to subfolder
  deploy-pr:
    if: github.event.pull_request.head.repo.full_name == github.repository
    name: Deploy PR Branch
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
  
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
  
      - name: Install dependencies
        run: npm install
  
      - name: Build
        run: tsc --noEmit false && npm run build || true
  
      - name: Set lowercase branch name
        id: branch
        run: |
          BRANCH_NAME=$(echo "${{ github.event.pull_request.head.ref }}" | tr '[:upper:]' '[:lower:]')
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
  
      - name: Deploy to S3 /<branch>/
        run: |
          aws s3 sync ${{ env.BUILD_PATH }} s3://${{ env.S3_BUCKET_NAME }}/${{ env.BRANCH_NAME }}/ --delete
  
      - name: Invalidate CloudFront cache for branch
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/${{ env.BRANCH_NAME }}/*"

      - name: Output QA URL
        run: |
          echo "✅ Preview URL: https://${{ env.BRANCH_NAME }}.dev.goodzyk.com"
