import { Link } from 'react-router'
import styled, { useTheme } from 'styled-components'
import { Icon, TextPanel, Typography, UiLink } from '@aidsupply/components'

const StyledTextPanel = styled(TextPanel)`
  padding: 20px;
  margin-bottom: 30px;
  .icon {
    margin-right: 15px;
    flex-shrink: 0;
  }

  .link {
    margin-top: 5px;
  }
`

interface IInfoPanel {
  date?: string
  text?: string
  children?: React.ReactElement
  linkText?: string
  linkTo?: string
}

const InfoPanel = ({ children, date, linkText, linkTo, text }: IInfoPanel) => {
  const theme = useTheme()
  return (
    <StyledTextPanel bordered>
      <Icon
        name="info"
        wrapperHeight={30}
        wrapperWidth={30}
        borderRadius="50%"
        fill={theme.color.general.dark}
        stroke={theme.color.general.dark}
        height={20}
        width={20}
      />
      {children || (
        <div>
          <Typography type="body1">{text}</Typography>
          <br />
          {date && (
            <Typography type="body2" className="date">
              {date}
            </Typography>
          )}
          {linkText && (
            <UiLink Link={Link} to={linkTo} themeColor="status.new">
              {linkText}
            </UiLink>
          )}
        </div>
      )}
    </StyledTextPanel>
  )
}

export default InfoPanel
