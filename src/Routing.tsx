import { BrowserRouter, Route, Routes } from 'react-router'
import Cookies, { CookieGetOptions } from 'universal-cookie'
import Login from './containers/Login/Login'
import MainWrapper from './containers/Layout/MainWrapper'
import PrivateRoute from './PrivateRoute'
import SignUp from './containers/SignUp/SignUp'
import PasswordRecovery from './containers/PasswordRecovery/PasswordRecovery'
import StatusInfoPage from './containers/StatusInfoPage/StatusInfoPage'
import UserProfile from './containers/UserProfile'
import IntroTopBarWrapper from './containers/IntroScreen/IntroTopBarWrapper'
import { PublicOnlyRoute } from './PublicOnlyRoute'
import Users from './containers/Admin/Users/<USER>'
import Organizations from './containers/Admin/Organizations/Organizations'
import UsersRightPanel from './containers/Admin/Users/<USER>'
import Brands from './containers/System/Brands/Brands'
import OrganizationsRightPanel from './containers/Admin/Organizations/OrganizationsRightPanel'
import BrandsRightPanel from './containers/System/Brands/BrandsRightPanel'
import Categories from './containers/System/Categories/Categories'
import CategoriesRightPanel from './containers/System/Categories/CategoriesRightPanel'
import Items from './containers/System/Items/Items'
import ItemsRightPanel from './containers/System/Items/ItemsRightPanel'
import Units from './containers/System/Units/Units'
import UnitsRightPanel from './containers/System/Units/UnitsRightPanel'
import Attributes from './containers/System/Attributes/Attributes'
import AttributesRightPanel from './containers/System/Attributes/AttributesRightPanel'
import Warehouses from './containers/Inventory/Warehouses/Warehouses'
import WarehousesRightPanel from './containers/Inventory/Warehouses/WarehousesRightPanel'
import Orders from './containers/Documents/Orders/Orders'
import PasswordRecoverySent from './containers/PasswordRecoverySent/PasswordRecoverySent'
import OrdersRightPanel from './containers/Documents/Orders/OrdersRightPanel'
import FAQs from './containers/Platform/FAQs/FAQs'
import FAQsRightPanel from './containers/Platform/FAQs/FAQsRightPanel'
import Reports from './containers/Platform/Reports/Reports'
import ReportsRightPanel from './containers/Platform/Reports/ReportsRightPanel'
import AddItemRightPanel from './components/RightPanel/AddItemRightPanel'
import CommentsRightPanel from './components/RightPanelForms/CommentsRightPanel'
import Invoices from './containers/Documents/Invoices/Invoices'
import InvoicesRightPanel from './containers/Documents/Invoices/InvoicesRightPanel'
import Shipments from './containers/Documents/Shipments/Shipments'
import { ShipmentsRightPanel } from './containers/Documents/Shipments/ShipmentsRightPanel'
import OrganizationProfile from './containers/Admin/OrganizationProfile/OrganizationProfile'
import Pages from './containers/Platform/Pages/Pages'
import PagesRightPanel from './containers/Platform/Pages/PagesRightPanel'
import Posts from './containers/Platform/Posts/Posts'
import PostsRightPanel from './containers/Platform/Posts/PostsRightPanel'
import Inquiries from './containers/Documents/Inquiries/Inquiries'
import InquiriesRightPanel from './containers/Documents/Inquiries/InquiriesRightPanel'
import Campaigns from './containers/Platform/Campaigns/Campaigns'
import CampaignsRightPanel from './containers/Platform/Campaigns/CampaignsRightPanel'
import Inventories from './containers/Inventory/Inventories/Inventories'
import { InventoriesRightPanel } from './containers/Inventory/Inventories/InventoriesRightPanel'
import NotFound from './containers/NotFound/NotFound'
import Dashboard from './containers/Dashboard/Dashboard'

const Routing = () => {
  const cookies = new Cookies()
  const isUserAuthenticated = !!cookies.get('tokenVal', { path: '/' } as CookieGetOptions)

  return (
    <BrowserRouter>
      <Routes>
        <Route 
          path="/signin" 
          element={
            <PublicOnlyRoute isUserAuthenticated={isUserAuthenticated}>
              <Login />
            </PublicOnlyRoute>
          } 
        />
        <Route 
          path="/signup" 
          element={
            <PublicOnlyRoute isUserAuthenticated={isUserAuthenticated}>
              <SignUp />
            </PublicOnlyRoute>
          } 
        />
        <Route 
          path="/password-recovery" 
          element={
            <PublicOnlyRoute isUserAuthenticated={isUserAuthenticated}>
              <PasswordRecovery />
            </PublicOnlyRoute>
          } 
        />
        <Route 
          path="/password-request-sent" 
          element={
            <PublicOnlyRoute isUserAuthenticated={isUserAuthenticated}>
              <PasswordRecoverySent />
            </PublicOnlyRoute>
          } 
        />
        <Route
          path="/"
          element={
            <PrivateRoute>
              <IntroTopBarWrapper>
                <MainWrapper />
              </IntroTopBarWrapper>
            </PrivateRoute>
          }
        >
          <Route path="/pending" element={<StatusInfoPage />}/>
          <Route path="/inactive" element={<StatusInfoPage />}/>
          <Route path="/" element={<Dashboard />}/>
          <Route path="/profile" element={<UserProfile />}/>

          <Route path="/platform">
            <Route path="posts" element={<Posts />}>
              <Route path=":rightPanelId" element={<PostsRightPanel />} />
            </Route>
            <Route path="faqs" element={<FAQs />}>
              <Route path=":rightPanelId" element={<FAQsRightPanel />} />
            </Route>
            <Route path="campaigns" element={<Campaigns />}>
              <Route path=":rightPanelId" element={<CampaignsRightPanel />} />
            </Route>
            <Route path="reports" element={<Reports />}>
              <Route path=":rightPanelId" element={<ReportsRightPanel />} />
            </Route>
            <Route path="pages" element={<Pages />}>
              <Route path=":rightPanelId" element={<PagesRightPanel />} />
            </Route>
          </Route>

          <Route path="/documents">
            <Route path="inquiries" element={<Inquiries />}>
              <Route path=":rightPanelId" element={<InquiriesRightPanel />} />
              <Route path=":rightPanelId/add" element={<AddItemRightPanel />} />
              <Route path=":rightPanelId/comments" element={<CommentsRightPanel />} />
            </Route>
            <Route path="orders" element={<Orders />}>
              <Route path=":rightPanelId" element={<OrdersRightPanel />} />
              <Route path=":rightPanelId/add" element={<AddItemRightPanel />} />
              <Route path=":rightPanelId/comments" element={<CommentsRightPanel />} />
            </Route>
            <Route path="shipments" element={<Shipments />}>
              <Route path=":rightPanelId" element={<ShipmentsRightPanel />} />
              <Route path=":rightPanelId/add" element={<AddItemRightPanel />} />
              <Route path=":rightPanelId/comments" element={<CommentsRightPanel />} />
            </Route>
            <Route path="invoices" element={<Invoices />}>
              <Route path=":rightPanelId" element={<InvoicesRightPanel />} />
              <Route path=":rightPanelId/add" element={<AddItemRightPanel />} />
              <Route path=":rightPanelId/comments" element={<CommentsRightPanel />} />
            </Route>
          </Route>

          <Route path="/admin">
            <Route path="users" element={<Users />}>
              <Route path=":rightPanelId" element={<UsersRightPanel />} />
            </Route>
            <Route path="organizations" element={<Organizations />}>
              <Route path=':rightPanelId' element={<OrganizationsRightPanel />} />
            </Route>
            <Route path="organization-profile" element={<OrganizationProfile />}>
          </Route>
          </Route>

          <Route path="/inventory">
            <Route path="warehouses" element={<Warehouses />}>
              <Route path=":rightPanelId" element={<WarehousesRightPanel />} />
            </Route>
            <Route path="inventories" element={<Inventories />}>
              <Route path=":rightPanelId" element={<InventoriesRightPanel />} />
              <Route path=":rightPanelId/add" element={<AddItemRightPanel />} />
              <Route path=":rightPanelId/comments" element={<CommentsRightPanel />} />
            </Route>
          </Route>

          <Route path="/system">
            <Route path="brands" element={<Brands />}>
              <Route path=":rightPanelId" element={<BrandsRightPanel />} />
            </Route>
            <Route path="categories" element={<Categories />}>
              <Route path=":rightPanelId" element={<CategoriesRightPanel />} />
            </Route>
            <Route path="items" element={<Items />}>
              <Route path=":rightPanelId" element={<ItemsRightPanel />} />
            </Route>
            <Route path="units" element={<Units />}>
              <Route path=":rightPanelId" element={<UnitsRightPanel />} />
            </Route>
            <Route path="attributes" element={<Attributes />}>
              <Route path=":rightPanelId" element={<AttributesRightPanel />} />
            </Route>
          </Route>
        </Route>
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  )
}

export default Routing
