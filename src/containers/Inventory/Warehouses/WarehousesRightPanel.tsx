import { useNavigate, useParams } from 'react-router'
import { useDispatch } from 'react-redux'
import { object } from 'dot-object'
import { isObject } from '@aidsupply/components'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { warehousesRightPanelConfig } from './warehousesRightPanelConfig'
import { useMappedState } from '../../../hooks'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { ITranslations } from '../../../locales'
import { ICountry, ICurrency, IRegion } from '../../../commonInterfaces'

export interface IWarehousesRightPanel {
  id?: number
  name?: string
  country_id?: ICountry
  currency_id?: ICurrency
  organization_id?: {
    id: number
    [key: string]: unknown
  }
  type?: {id: string, label: ITranslations}[]
  region_id?: IRegion
  city?: string
}

const WarehousesRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { rightPanelId: editedItemId } = useParams()

  const warehouseInitial = useMappedState(selectSidebarInitialData)

  const isReadOnly = false

  const onSubmit = (formValuesChanged: IWarehousesRightPanel) => {
    const requestBody: IWarehousesRightPanel = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key as keyof IWarehousesRightPanel]) &&
      // @ts-ignore
      requestBody[key as keyof IWarehousesRightPanel]?.id) {
        requestBody[key as keyof IWarehousesRightPanel] = requestBody[key as
          // @ts-ignore
          keyof IWarehousesRightPanel]?.id
      }
    })

    if (formValuesChanged.type) {
      // @ts-ignore
      requestBody.type = formValuesChanged.type.map((type) => (editedItemId === 'clone' ? type : type.id))
    }

    const dataToSend = {
      id: warehouseInitial?.id,
      requestBody,
      type: 'warehouses',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} formData={warehousesRightPanelConfig}
  onSubmit={onSubmit}/>
}

export default WarehousesRightPanel
