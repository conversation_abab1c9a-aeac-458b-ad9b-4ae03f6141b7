import { useContext } from 'react'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router'
import { useTheme } from 'styled-components'
import { Userpic, HeaderMenuIcon, ScreenContext } from '@aidsupply/components'
import { selectIsNavMenuExtended, selectUserDetails, selectUserStatus } from '../../redux-saga/selectors'
import { toggleNavMenuExtended } from '../../redux-saga/reducers/common'
import NotificationButton from '../../components/Notification/NotificationButton'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import Logo from '../../components/Logo'
import { IMAGEKIT_URL } from '../../constants'
import { useMappedState } from '../../hooks'
import { StyledNotificationWrapper, StyledTopBar, StyledUserPic } from './styled'
import { Status } from '../../commonTypes'

const TopBar = () => {
  const dispatch = useDispatch()
  const theme = useTheme()
  const navigate = useNavigate()
  const { width, xl } = useContext(ScreenContext)
  const isTablet = width <= xl
  {/* TODO: uncomment search after it's implemented */}
  // const [isFullWidthSearch, setIsFullWidthSearch] = useState<boolean>(false)

  const user = useMappedState(selectUserDetails) as ICurrentUser
  const status = useMappedState(selectUserStatus) as Status
  const isNavMenuExtended = useMappedState(selectIsNavMenuExtended)

  const getDirection = () => {
    const directions: Partial<Record<Status, string>> = {
      active: '../',
      pending: '../pending',
      inactive: '../inactive'
    };
  
    return directions[status];
  };

  const handleUserProfile = () => {
    navigate('/profile');
  }

  const onIconMenuClick = () => {
    dispatch(toggleNavMenuExtended(!isNavMenuExtended))
  }

  return(
    <StyledTopBar>
      <Logo theme={theme} variant="small" to={getDirection()} padding="0" isExtended withText />
      {/* TODO: uncomment search after it's implemented */}
      {/* <TopBarSearch handleFullWidthSearch={setIsFullWidthSearch} isFullWidthSearch={isFullWidthSearch}/> */}
      <StyledNotificationWrapper>
        <NotificationButton />
        <StyledUserPic onClick={handleUserProfile}>
          <Userpic
            imagekitUrl={IMAGEKIT_URL}
            height='32px'
            width='32px'
            src={user.profile_pic}
            theme={theme}
            fullName={user.full_name || '?'}
            borderRadius='6px'
          />
        </StyledUserPic>
        {status === 'active' && isTablet && 
          <HeaderMenuIcon
            wrapperColor={theme.color.general.light}
            className="menuIcon ignore-click-outside"
            opened={isNavMenuExtended}
            onClick={onIconMenuClick}
            fill={theme.color.general.dark}
            width={20}
            height={20}
          />
        }
      </StyledNotificationWrapper>
    </StyledTopBar>
  )
}

export default TopBar
