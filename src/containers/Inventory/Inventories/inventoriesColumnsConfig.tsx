import { Typography } from '@aidsupply/components'
import { getDate, getSelectCellValue, getWarehouse } from '../../../components/config/columns'
import { TLanguages } from '../../../locales'
import { ISystem, TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { IOrganization } from '../../../commonInterfaces'
import ReactTexty from '../../../lib/react-texty'

export const inventoriesColumnsConfig = (lng: TLanguages, t: (key: string) => string, tableMode?: TableModeType) => {
  return [
    getNameByMode({
      t,
      dataKey: 'number',
      option: 'state',
      tableMode: tableMode || 'table',
      lng,
      key: 'inventoryNumber',
    }),
    getWarehouse(t, tableMode || 'table'),
    {
      key: 'user',
      optionsKeys: ['users'],
      dataKey: 'user_id',
      labelKey: 'full_name',
      sortable: true,
      noTranslation: true,
      flexGrow: 1,
      width: 0,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: getSelectCellValue,
    },
    {
      key: 'organization',
      sortable: true,
      dataKey: 'organization_id',
      labelKey: 'organization',
      optionsKeys: ['organizations'],
      noTranslation: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: ({ rowData, container }: {
        rowData: { organization_id: number },
        container: {
          props: {
            cellProps: {
              system: ISystem
            }
          }
        } }) => {
        const orgs = container?.props?.cellProps?.system?.organizations || {}
        const org = orgs[rowData.organization_id] || Object.values(orgs).find((org: IOrganization) => org.id === rowData.organization_id)
        const orgName = org?.name || 'N/A'
        return (
          <Typography as={ReactTexty}>{orgName}</Typography>
        )
      },
      headerRenderer: headerRenderer(t, tableMode),
    },
    { ...getDate(t, tableMode), key: 'created' },
    { ...getDate(t, tableMode), key: 'lastUpdated', dataKey: 'updated_at' },
  ]
}
