import styled from 'styled-components'
import { getTokens } from '@aidsupply/components'

export const StyledDatePicker = styled.div`
  position: relative;
  border: none;

  & > div {
    width: 265px;

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
      width: 225px;
    }
  }

  .react-datepicker {
    color: ${({ theme }) => theme.color.general.dark};
  }

  .react-datepicker__input-container {
    input {
      ${({ theme }) => getTokens('typography-h3-black-large', theme)};
        border: none !important;
        box-shadow: none !important;

      &:hover {
        cursor: pointer !important;
      }

      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
        font-size: 14px !important;
      }
    }

      input:not(.hasRightIcon) {
        padding: 12px 12px;
        text-align: center;
      }

      .icon.rightIcon {
        bottom: 2px;
        display: none;
      }
    }

    .react-datepicker-popper {
        z-index: 3;
    }

    .react-datepicker__day--selected,
    .react-datepicker__day--in-selecting-range,
    .react-datepicker__day--in-range,
    .react-datepicker__month-text--selected,
    .react-datepicker__month-text--in-selecting-range,
    .react-datepicker__month-text--in-range,
    .react-datepicker__year-text--selected,
    .react-datepicker__year-text--in-selecting-range,
    .react-datepicker__year-text--in-range {
      background-color: ${({ theme }) => theme.color.primary.main};
    }

    .react-datepicker__day--keyboard-selected:not(
      .react-datepicker__day--today,
      .react-datepicker__day--in-selecting-range,
      .react-datepicker__day--range-start,
      .react-datepicker__day--in-range
    ) {
      color: ${({ theme }) => theme.color.general.dark};
      background-color: transparent;
    }

    .inputContainer {
      margin: 0;

      .datePickerSelect {
        border-color: transparent;
      }
    }

    .myContainer .react-datepicker__navigation {
      top: 46px;
    }

    &.isClearable {
      .react-datepicker__close-icon {
        background-color: transparent;
        padding: 0 6px;
      }

      .react-datepicker__close-icon::after {
        background-color: transparent;
        color: ${({ theme }) => theme.color.general.dark};
        font-size: 16px;
      }

      input:not(.hasLeftIcon) {
        padding: 12px 23px 12px 15px;
      }
    }

    @media only screen and (max-width: 850px) {
      .react-datepicker__input-container input {
        font-size: 14px;
      }
    }
`
