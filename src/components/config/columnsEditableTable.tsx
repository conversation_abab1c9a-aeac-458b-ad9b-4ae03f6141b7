import { getAvailableTranslation, Label } from '@aidsupply/components'
import { DEFAULT_LANGUAGE, TLanguages } from '../../locales'
import { headerRenderer } from '../../utils/table'
import { getStatusColumnProps } from './columns'
import ReactTexty from '../../lib/react-texty'
import { UNITS_PIECES_ID } from '../../constants'
import { ISystem } from '../../redux-saga/reducers/data'

export const getItemTitle = (t: (key: string) => string, lng: TLanguages) => {
  return {
    key: 'item',
    dataKey: 'item_id',
    width: 0,
    flexGrow: 2,
    cellRenderer: ({ cellData, container, rowData }: { cellData: string; container: any; rowData: any }) => {
      const {
        props: {
          cellProps: { system },
        },
      } = container

      const title =
        getAvailableTranslation(system.items?.[cellData]?.translations, DEFAULT_LANGUAGE, lng) ||
        rowData.item_name ||
        ''
      const model = system.items?.[cellData]?.sku

      const brandObject = rowData.brand_id && container.props.cellProps.system.brands?.[rowData.brand_id]
      const brandName = brandObject?.name || ''

      return (
        <ReactTexty tooltipClassName="invisible">
          <ReactTexty>{title ? `${title} (${model})` : model}</ReactTexty>
          <Label type="smaller" text={rowData.brand_name || brandName} />
        </ReactTexty>
      )
    },
    headerRenderer: headerRenderer(t, 'table'),
  }
}

export const getEditableTableColumns = (type: string, lng: TLanguages, t: (key: string) => string) => {
  const EDITABLE_TABLE_COLUMNS_CONFIG = {
    'organizations.supervised_organizations': [
      {
        key: 'index',
        cellRenderer: ({ rowIndex }: { rowIndex: number }) => rowIndex + 1,
      },
      { ...getStatusColumnProps(t, { width: 13, height: 13 }, 'table'), width: 30 },
      {
        key: 'name',
        dataKey: 'name',
        isEditable: false,
        width: 0,
        flexGrow: 1,
      },
    ],
    'orders.order_items': [
      {
        key: '#',
        cellRenderer: ({ rowIndex }: { rowIndex: number }) => rowIndex + 1,
        headerRenderer: headerRenderer(t, 'table'),
      },
      getItemTitle(t, lng),
      {
        key: 'quantity',
        dataKey: 'quantity',
        isEditable: true,
        type: 'number',
        hideButtons: true,
        width: 80,
        headerRenderer: headerRenderer(t, 'table'),
      },
      {
        key: 'price',
        dataKey: 'price',
        isEditable: true,
        type: 'number',
        hideButtons: true,
        width: 85,
        headerRenderer: headerRenderer(t, 'table'),
      },
    ],
    'shipments.shipment_items': [
      {
        key: 'index',
        cellRenderer: ({ rowIndex }: { rowIndex: number }) => rowIndex + 1,
      },
      getItemTitle(t, lng),
      {
        key: 'quantity',
        dataKey: 'quantity',
        isEditable: true,
        type: 'number',
        hideButtons: true,
        width: 80,
        headerRenderer: headerRenderer(t, 'table'),
      },
    ],
    'invoices.invoice_items': [
      {
        key: 'index',
        cellRenderer: ({ rowIndex }: { rowIndex: number }) => rowIndex + 1,
      },
      getItemTitle(t, lng),
      {
        key: 'quantity',
        dataKey: 'quantity',
        isEditable: true,
        type: 'number',
        hideButtons: true,
        width: 80,
        headerRenderer: headerRenderer(t, 'table'),
      },
    ],
    'inquiries.inquiry_items': [
      {
        key: 'index',
        cellRenderer: ({ rowIndex }: { rowIndex: number }) => rowIndex + 1,
      },
      {
        key: 'category',
        dataKey: 'category_id',
        width: 0,
        flexGrow: 2,
        cellRenderer: ({ cellData, container }: { cellData: number, container: {props: {cellProps: {system: ISystem, lng: TLanguages}}}}) => {
          const {
            props: {
              cellProps: { system, lng },
            },
          } = container
          const category = system.categories?.[cellData]
          const unitId = category?.unit_id
          // @ts-ignore
          const unit = system.units?.[unitId] || system.units?.[UNITS_PIECES_ID]

          return (
            <ReactTexty>
              <div style={{ display: 'flex' }}>
                <ReactTexty>
                  {getAvailableTranslation(category?.translations, DEFAULT_LANGUAGE, lng)}
                  {system.units &&
                    unit &&
                    `, ${getAvailableTranslation(unit.translations, DEFAULT_LANGUAGE, lng) || unit.code}`}
                </ReactTexty>
              </div>{' '}
            </ReactTexty>
          )
        },
        headerRenderer: headerRenderer(t, 'table'),
      },
      {
        key: 'quantity',
        dataKey: 'qty_new',
        isEditable: true,
        type: 'number',
        width: 100,
        hideButtons: true,
        headerRenderer: headerRenderer(t, 'table'),
      },
    ],
    'inventories.inventory_items': [
      {
        key: 'index',
        cellRenderer: ({ rowIndex }: { rowIndex: number }) => rowIndex + 1,
      },
      getItemTitle(t, lng),
      {
        key: 'quantity',
        dataKey: 'quantity',
        isEditable: true,
        type: 'number',
        hideButtons: true,
        fullWidth: true,
        width: 80,
        headerRenderer: headerRenderer(t, 'table'),
      },
      {
        key: 'price',
        dataKey: 'price',
        isEditable: true,
        type: 'number',
        hideButtons: true,
        fullWidth: true,
        width: 85,
        headerRenderer: headerRenderer(t, 'table'),
      },
    ],
  }

  // @ts-ignore
  return EDITABLE_TABLE_COLUMNS_CONFIG[type]
}
