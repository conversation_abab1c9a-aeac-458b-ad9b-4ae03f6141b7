import { Typography } from '@aidsupply/components'
import ReactTexty from '../../../lib/react-texty'
import { TLanguages } from '../../../locales'
import { ISystem, TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { getDate, getSelectCellValue } from '../../../components/config/columns'
import { IOrganization } from '../../../commonInterfaces'

export const postsColumnsConfig = (
  lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    getNameByMode({
      t,
      dataKey: 'translations.en',
      option: 'state',
      key: 'title',
      tableMode: tableMode || 'table',
      lng
    }),
    {
      key: 'topic',
      dataKey: 'topic_id',
      optionsKeys: ['topics'],
      labelKey: 'translations',
      sortable: true,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: getSelectCellValue,
    },
    {
      key: 'visibility',
      dataKey: 'visibility',
      optionsKeys: ['visibilityTypes'],
      sortable: true,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: getSelectCellValue,
    },
    {
      key: 'organization',
      sortable: true,
      dataKey: 'organization_id',
      labelKey: 'organization',
      optionsKeys: ['organizations'],
      noTranslation: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: ({ rowData, container }: {
        rowData: { organization_id: number },
        container: {
          props: {
            cellProps: {
              system: ISystem
            }
          }
        } }) => {
        const orgs = container?.props?.cellProps?.system?.organizations || {}
        const org = orgs[rowData.organization_id] || Object.values(orgs).find((org: IOrganization) => org.id === rowData.organization_id)
        const orgName = org?.name || 'N/A'
        return (
          <Typography as={ReactTexty}>{orgName}</Typography>
        )
      },
      headerRenderer: headerRenderer(t, tableMode),
    },
    { ...getDate(t, tableMode), key: 'lastUpdated', dataKey: 'updated_at' },
  ]
}
