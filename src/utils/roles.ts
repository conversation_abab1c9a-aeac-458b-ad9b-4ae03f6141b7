import { USER_ROLES } from '../components/NavMenu/config'
import { UserRole } from '../redux-saga/reducers/user'

export const getUserAccessLevel = (role: UserRole) => {
  const maxLevel = Object.keys(USER_ROLES).length - 1

  return USER_ROLES[role]?.level ?? maxLevel
}

export const isAccessAllowed = (accessLevel: number | string[], userRole: UserRole) => {
  const userAccessLevel = getUserAccessLevel(userRole)

  return typeof accessLevel === 'number'
    ? accessLevel >= userAccessLevel
    : accessLevel.some((accessRole) => userRole === accessRole)
}
