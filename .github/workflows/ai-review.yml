name: AI Code Review

on:
  pull_request:
    types: [ opened, synchronize ]

jobs:
  review:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install OpenAI SDK
        run: pip install openai

      - name: Get PR Diff
        id: diff
        run: |
          gh pr diff ${{ github.event.pull_request.number }} > pr_diff.txt
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: AI Review with OpenAI
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          python3 <<EOF
          import os
          from openai import OpenAI
          
          client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
          
          # Read diff from the file
          with open("pr_diff.txt", "r") as f:
              diff = f.read()
          
          if not diff or len(diff.strip()) < 50:
              print("Diff is too small, skipping.")
              exit(0)
          
          prompt = f"Review this code diff and provide feedback:\\n\\n{diff[:5000]}"
          response = client.chat.completions.create(
              model="gpt-4",
              messages=[{"role": "user", "content": prompt}]
          )
          
          review = response.choices[0].message.content
          
          with open("review.txt", "w") as f:
              f.write(review)
          EOF

      - name: Comment on PR
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          path: review.txt