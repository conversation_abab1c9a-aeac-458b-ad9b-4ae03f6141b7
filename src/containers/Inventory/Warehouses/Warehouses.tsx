import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import WarehousesTableCard from './WarehousesTableCard'

const Warehouses = () => {
  return (
    <GridLayout rightPanelComponent={<Outlet context={{}} />}>
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={WarehousesTableCard as unknown as React.JSX.Element}
        tableCardHeight={143}
        iconName='warehouse'
      />
    </GridLayout>
  )
}

export default Warehouses
