import { useTranslation } from 'react-i18next'
import { FlexRow, isObjectEmpty, Tabs, Typography } from '@aidsupply/components'
import { useMappedState } from '../../hooks'
import clsx from 'clsx'
import { useTheme } from 'styled-components'
import InfoPanel from '../InfoPanel'
import UploadFiles from '../UploadFiles'
import { StyledThumbnails } from '../UploadFiles/styled'
import Thumbnail, { IFileDrafted } from '../UploadFiles/Thumbnail'
import { selectDataTypeObject } from '../../redux-saga/selectors'
import { ISubmenu } from '../NavMenu/config'
import { UpdateInput } from '../Form/interfaces'
import { StyledTabsWrapper } from './styled'

interface IFilesForm {
  changedPhotos?: Record<string, IFileDrafted[]>
  changedFilesStates?: Record<string, 'drafted' | 'posted' | 'deleted'>
  id?: number
  isReadOnly?: boolean
  itemCharacteristics?: Record<string, unknown>
  updateInput?: UpdateInput
  primaryPhotoId?: number
  primaryLogoId?: number
  primaryBannerId?: number
  filesType?: 'photos' | 'files'
  withTabsHidden?: boolean
  values?: Record<string, {
    active: IFileDrafted[]
    disabled?: IFileDrafted[]
  }>
}

const FilesForm = ({
  changedFilesStates,
  id,
  isReadOnly,
  itemCharacteristics,
  values = { general: {
    active: []
  }},
  updateInput,
  primaryPhotoId,
  primaryLogoId,
  primaryBannerId,
  filesType,
  withTabsHidden,
}: IFilesForm) => {
  const theme = useTheme()
  const { t } = useTranslation('forms')
  const typeData = useMappedState(selectDataTypeObject) as ISubmenu
  const entityType = typeData?.key

  // We don't allow adding pictures until collection object is written to the DB (when creating new one)
  if (!id) {
    return <InfoPanel text={t('fillGeneralAndSaveBeforeUploadingPhotos')} />
  }

  const filesGroups = !isObjectEmpty(values) ? Object.keys(values) : ['general']

  const filteredFileGroups = filesGroups.filter((group) => {
    if (entityType === 'items') {
      const splitPhotoType = group.split('_')
      const attributeSlug = splitPhotoType[0]
      const enumOptionSlug = splitPhotoType[1]

      if (
        !itemCharacteristics ||
        (group !== 'general' &&
          (!itemCharacteristics[attributeSlug] ||
            // @ts-ignore
            itemCharacteristics[attributeSlug].findIndex(
              // @ts-ignore
              (enumOption) => enumOption.slug === enumOptionSlug
            ) === -1))
      ) {
        return false
      }
    }
    return true
  })

  return (
    <StyledTabsWrapper className={clsx('photos', withTabsHidden && 'withTabsHidden')}>
      <Tabs
        className="tabs"
        withAddAction
        tabsTitles={filteredFileGroups.map((fileGroup, idx) => {
          const text = t(fileGroup)
          return (
            <FlexRow id={fileGroup} gap="6px" key={idx}>
              <Typography text={text} style={{
                fontWeight: 600,
                letterSpacing: 0,
              }} />
            </FlexRow>
          )
        })}
        tabsContents={filteredFileGroups.map((fileGroup, i) => {
          const isOnlyOneFileAllowed = entityType === 'reports'
          const isMultipleUploadDisabled = isOnlyOneFileAllowed && !!values?.general?.active.length

          const primaryPhoto =
            filesType === 'photos' &&
            (entityType === 'organizations' || entityType === 'users'
              ? (fileGroup === 'logos' && primaryLogoId) || (fileGroup === 'banners' && primaryBannerId)
              : primaryPhotoId) as number
          return (
            <div
              aria-labelledby={`scrollable-auto-tab-${i}`}
              id={`scrollable-auto-tabpanel-${i}`}
              key={`${fileGroup}-${i}`}
              role="tabpanel"
            >
              {!isReadOnly && (
                <UploadFiles
                  entityType={entityType}
                  entityId={id}
                  fileGroup={fileGroup}
                  filesType={filesType}
                  isMultipleUploadDisabled={isMultipleUploadDisabled}
                  maxFiles={isOnlyOneFileAllowed ? 1 : undefined}
                  />
                )
              }
              {/* @ts-ignore */}
              {!!values?.[fileGroup]?.length && (
                <StyledThumbnails>
                   {/* @ts-ignore */}
                  {values[fileGroup].map((photo, index: number) => (
                    <Thumbnail
                      values={values}
                      updateInput={updateInput}
                      key={`${photo.url}-${index}`}
                      saved
                      src={photo.url}
                      filesType={filesType}
                    />
                  ))}
                </StyledThumbnails>
              )}

              {values?.[fileGroup]?.active && (
                <StyledThumbnails className="filesContainer">
                  {values[fileGroup].active.map((file) => (
                    <Thumbnail
                      changedFilesStates={changedFilesStates}
                      entityType={entityType}
                      isReadOnly={isReadOnly}
                      file={file}
                      src={file.url}
                      values={values}
                      updateInput={updateInput}
                      key={file.id}
                      saved
                      fileGroup={fileGroup}
                      fileSize={file.meta.file_size}
                      fileName={file.meta.file_name_original}
                      isPrimary={primaryPhoto === file.id}
                      t={t}
                      isActive
                      filesType={filesType}
                    />
                  ))}
                </StyledThumbnails>
              )}

              {!isReadOnly && (
                <>
                  {values?.[fileGroup]?.disabled && (
                    <StyledThumbnails className="filesContainer">
                      {values[fileGroup].disabled.map((file) => (
                        <Thumbnail
                          changedFilesStates={changedFilesStates}
                          file={file}
                          src={file.url}
                          isDeleted
                          values={values}
                          updateInput={updateInput}
                          key={file.id}
                          saved
                          fileGroup={fileGroup}
                          fileSize={file.meta.file_size}
                          fileName={file.meta.file_name_original}
                          filesType={filesType}
                          isMultipleUploadDisabled={isMultipleUploadDisabled}
                        />
                      ))}
                    </StyledThumbnails>
                  )}
                </>
              )}
            </div>
          )
        })}
        typographyType="body1"
        backgroundColor={theme.color.general.gray2}
      />
    </StyledTabsWrapper>
  )
}

export default FilesForm
