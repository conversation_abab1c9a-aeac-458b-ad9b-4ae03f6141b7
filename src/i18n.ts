import i18n, { InitOptions } from 'i18next'
import { initReactI18next } from 'react-i18next'

import { DEFAULT_LANGUAGE, resources, SUPPORTED_LNGS, TLanguages } from './locales'

i18n.use(initReactI18next).init({
  resources,
  supportedLngs: SUPPORTED_LNGS,
  load: 'languageOnly',
  fallbackLng: 'en' as TLanguages,
  defaultLng: DEFAULT_LANGUAGE as TLanguages,
  lng: DEFAULT_LANGUAGE as TLanguages,
  languages: Object.keys(resources),
  keySeparator: false,
  interpolation: {
    escapeValue: false,
  },
} as InitOptions)

export default i18n
