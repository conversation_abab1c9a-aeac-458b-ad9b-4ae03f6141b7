import { useContext } from 'react'
import { DefaultTheme, useTheme } from 'styled-components'
import { useTranslation } from 'react-i18next'
import { Icon, ScreenContext } from '@aidsupply/components'
import { useDispatch } from 'react-redux'
import { toggleLeftPanelExtended, toggleRightPanelExtendedWider } from '../../redux-saga/reducers/common'
import RightPanel from '../../components/RightPanel'
import { selectIsRightPanelExtendedWider } from '../../redux-saga/selectors'
import { NAV_MENU_WIDTH } from '../../constants'
import { IGridLayout } from './GridLayout'
import CentralPanel from './CentralPanel'
import { useMappedState } from '../../hooks'
import { StyledGrid, StyledLeftDrawer } from './styled'

const GridLayoutDesktop = ({
  children,
  rightPanelComponent,
  isRightPanelExtended,
  handleToggleRightPanel,
  leftPanelComponent,
  isLeftPanelExtended,
  isIconRightPanelWide,
  className,
}: IGridLayout) => {
  const theme = useTheme()
  const dispatch = useDispatch()
  const { t } = useTranslation(['table'])
  const { currentBreakpoint } = useContext(ScreenContext)
  const isRightPanelExtendedWider = useMappedState(selectIsRightPanelExtendedWider) as boolean

  const getWidth = (panelColsCount: number) => {
    const navMenuWidth = NAV_MENU_WIDTH.desktop.condensed

    return `calc(((100vw - ${navMenuWidth}) / ${theme.grid[currentBreakpoint as keyof DefaultTheme['grid']]?.columns?.count}) * ${panelColsCount} - 20px)`
  }

  const getRightPanelWidth = (currentBreakpoint: keyof DefaultTheme['grid']) => {
    const panelWidth =
      isRightPanelExtended && isRightPanelExtendedWider
        ? theme.grid[currentBreakpoint]?.rightPanelColsWide
        : theme.grid[currentBreakpoint]?.rightPanelCols

    return `calc(((100vw) / ${theme.grid[currentBreakpoint]?.columns?.count}) * ${panelWidth} - 20px)`
  }

  return (
    <StyledGrid className={className}>
      {leftPanelComponent && (
        <StyledLeftDrawer
          width={getWidth(theme.grid[currentBreakpoint as keyof DefaultTheme['grid']]?.leftPanelCols)}
          openedValue={isLeftPanelExtended}
          side="left"
          id="filters"
          title={t('table:filters')}
          closeIconPadding="6px"
          closeIconProps={{
            width: 18,
            height: 18,
            wrapperWidth: 32,
            wrapperHeight: 32,
            fill: theme.color.general.gray4,
            strokeWidth: 1.4,
            onClick: () => dispatch(toggleLeftPanelExtended(false))
          }}
        >
          {leftPanelComponent}
        </StyledLeftDrawer>
      )}

      <CentralPanel
        rightPanelWidth={getRightPanelWidth(currentBreakpoint)}
        isRightPanelExtendedWider={isRightPanelExtendedWider}
        currentBreakpoint={currentBreakpoint}
      >
        {rightPanelComponent && <Icon
          width={16}
          height={16}
          wrapperWidth={20}
          wrapperHeight={20}
          fill={theme.color.general.light}
          strokeWidth={1.4}
          name="arrowLeft"
          className={`iconLeft ${isRightPanelExtended ? 'open' : 'close'}`}
          style={
            isRightPanelExtendedWider ? {
            rotate: '180deg',
            borderRadius: '0 6px 6px 0'
          } : {
            rotate: '0deg',
            borderRadius: '6px 0 0 6px'
          }}
          onClick={() => {
            dispatch(toggleRightPanelExtendedWider(!isRightPanelExtendedWider))
          }}
        />}
        {children}
      </CentralPanel>

      {rightPanelComponent && (
        <RightPanel
          width={getRightPanelWidth(currentBreakpoint)}
          openedValue={isRightPanelExtended}
          toggleDrawer={handleToggleRightPanel}
          isIconRightPanelWide={isIconRightPanelWide}
          isRightPanelExtendedWider={isRightPanelExtendedWider}
        >
          {rightPanelComponent}
        </RightPanel>
      )}
    </StyledGrid>
  )
}

export default GridLayoutDesktop
