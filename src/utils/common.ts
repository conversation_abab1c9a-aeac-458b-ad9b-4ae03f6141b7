import { NavigateFunction, Location } from 'react-router'
import { getAvailableTranslation } from '@aidsupply/components'
import dot from 'dot-object'
import { ENTITIES, IEntities, IUserRole } from '../components/NavMenu/config'
import { DEFAULT_LANGUAGE, LANGUAGES_FULL_NAME, TLanguages } from '../locales'
import { BasicValue, IFieldsProps, TranslateFn } from '../components/Form/interfaces'
import { IFileDrafted } from '../components/UploadFiles/Thumbnail'
import { ICurrency } from '../commonInterfaces'
import { Status } from '../commonTypes'

export const getEntityByRoute = (pathname: string) => {
  const getEntity = (configObject: IEntities[]) => {
    const array = Array.isArray(configObject) ? configObject : Object.values(configObject).flat() as IEntities[]

    const parentEntity = array.find(
      (entity: IEntities) => entity.route !== '/' && pathname.includes(entity.key.replace('_', '-'))
    )

    return parentEntity?.items
      ? parentEntity.items.find((entity) => pathname.includes(entity.route))
      : parentEntity
  }

  return pathname === '/' ? ENTITIES[0] : getEntity(ENTITIES)
}

export const getNavigate = (
  navigate: NavigateFunction,
  status: Status,
  location?: Location
) => {
  const paths = (location?.pathname || '') + (location?.search || '')
  const currentPath = paths || '/';

  const redirects: Partial<Record<typeof status, string>> = {
    active: currentPath === '/pending' || currentPath === '/inactive' ? '/' : currentPath,
    pending: currentPath === '/profile' ? '/profile' : '/pending',
    inactive: currentPath === '/profile' ? '/profile' : '/inactive',
  };

  navigate(redirects[status] || '/');
};

export const capitalizeFirstLetter = (string: string) => {
  return string.charAt(0).toUpperCase() + string?.slice(1)
}

export const getSelectLabel = (
  system: Record<string, IUserRole | unknown>,
  value: string | {id: string, slug: string },
  lng: TLanguages,
  noTranslation: boolean,
  labelKey: string = '',
  valueKey: string
) => {
  const valueFound = (valueKey && system[valueKey]) ||
  (typeof value === 'object' && (system[(value as { id: string }).id] ||
  system[(value as { slug: string }).slug])) ||
  system[value as string] || value;

  if (typeof valueFound === 'string') {
    return valueFound;
  }

  const label =
    typeof labelKey === 'string'
      ? dot.pick(labelKey || 'label', valueFound)
      // @ts-ignore
      : labelKey.map((key) => dot.pick(key, valueFound)).filter((val) => val)[0];

  if (noTranslation || !lng) {
    return label;
  }

  return getAvailableTranslation(label, DEFAULT_LANGUAGE, lng);
}

export const transformStringSelectValue = (value: Record<string, unknown>, labelKey: string, lng: TLanguages) => {
  if (typeof value === 'object') {
    return value
  }

  return {
    id: value,
    [labelKey || 'label']: value[lng] || value,
  }
}

export const getSelectValue = (
  value: BasicValue,
  options: Record<string, unknown>[],
  field: IFieldsProps
) => {
  if (!value) return ''

  if (!options?.length) return value

  const labelKey = field.labelKey ?? 'label'
  const valueKey = field.valueKey ?? 'id'

  const getValueKey = (val: unknown): string => {
    if (typeof val === 'object' && val !== null && valueKey in val) {
      return (val as Record<string, unknown>)[valueKey] as string
    }
    return val as string
  }

  const findOption = (val: unknown) => {
    const key = getValueKey(val)
    return options.find(option => {
      if (typeof option[valueKey] === 'number') {
        return option[valueKey] === Number(key)
      }
      return option[valueKey] === key
    })
  }

  if (Array.isArray(value)) {
    return value.map(findOption).filter(Boolean) as Record<string, unknown>[]
  }

  if (typeof value === 'string' || typeof value === 'number') {
    return findOption(value) as Record<string, unknown> | undefined
  }

  const hasLabel = typeof labelKey === 'string'
    ? dot.pick(labelKey, value)
    : (labelKey as string[]).some((key: string) => dot.pick(key, value))

  if (!hasLabel) {
    return findOption(value)
  }

  return value
}

export const getSelectLanguageValue = (lngKey: TLanguages) => {
  const name = LANGUAGES_FULL_NAME[lngKey]

  return {
    id: lngKey,
    label: name,
  }
}

export const getSelectOptions = (field: IFieldsProps, optionsData: Record<string, unknown>, lng: TLanguages) => {
  if (!field.options && !field.optionsKeys && !field.getOptions) {
    return
  }

  const getOptionsArr = field.getOptions && field.getOptions(optionsData)
  const optionsArr =
    getOptionsArr ||
    field.options ||
    // @ts-ignore
    field.optionsKeys?.reduce((acc, curr) => {
      const rawOptions = optionsData[curr] || {}
      const options = Object.values(rawOptions)

      return [...acc, ...options]
    }, [])

    // @ts-ignore
    return optionsArr?.[0]?.id
    // @ts-ignore
    ? optionsArr as Record<string, unknown>[]
    // @ts-ignore
    : optionsArr?.map((option) =>
        field.key?.toLowerCase().includes('language')
          ? getSelectLanguageValue(option as TLanguages)
          : transformStringSelectValue(option, field.labelKey as string, lng)
      )
}

export const countryCodeToFlagEmoji = (countryCode: string) => {
  if (countryCode?.length !== 2) {
    return ''
  }
  return countryCode
    .toUpperCase()
    .split('')
    .map((letter) => String.fromCodePoint(0x1f1e6 + letter.charCodeAt(0) - 'A'.charCodeAt(0)))
    .join('')
}

export const markFileDraftedOrPosted = (
  values: {
    active: IFileDrafted[]
    disabled?: IFileDrafted[]
  },
  file: IFileDrafted,
  isDeleted?: boolean
) => {
  const id = file?.id
  const updatedValues: {
    active: IFileDrafted[]
    disabled?: IFileDrafted[]
  } = {
    active: {} as IFileDrafted[]
  }
  const updatedFilesState: Record<string, 'drafted' | 'posted'> = {}
  if (isDeleted) {
    updatedValues.active = values.active.filter((file) => file.id !== id)
    updatedValues.disabled = [...(values.disabled || []), file]
    updatedFilesState[`id-${id}`] = 'drafted'
  } else {
    updatedValues.active = [...values.active, file]
    updatedValues.disabled = values.disabled?.filter((file) => file.id !== id) || []
    updatedFilesState[`id-${id}`] = 'posted'
  }

  return { updatedValues, updatedFilesState }
}

export const getAllItemsString = (items: string[], t: TranslateFn) => {
  const allItemsString = items
    .map((item) => {
      const translatedItem = t(item)
      const capitalizedItem = translatedItem.charAt(0).toUpperCase() + translatedItem.slice(1)
      return capitalizedItem
    })
    .join(', ')
  return allItemsString
}

export const getFirstChart = (item: string, t: TranslateFn) => {
  const firstChart = t(`${item}`)
  return firstChart.charAt(0).toUpperCase()
}

export const getCurrencyObjectById = (currencyId: number, currencies: Record<number, ICurrency>) => {
  return currencies && currencies[currencyId]
}
