import { Typography } from '@aidsupply/components'
import { ICurrency } from '../../../commonInterfaces'
import { getDate } from '../../../components/config/columns'
import ReactTexty from '../../../lib/react-texty'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getCurrencyObjectById } from '../../../utils/common'
import { getNameByMode, headerRenderer } from '../../../utils/table'

export const campaignsColumnsConfig = (
  _lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    getNameByMode({
      t,
      dataKey: 'translations.en',
      option: null,
      tableMode: tableMode || 'table',
      key: 'title',
    }),
    { ...getDate(t, tableMode, undefined, 'deadline', 'deadline_at') },
    {
      key: 'goal',
      sortable: true,
      dataKey: 'sum_goal',
      width: 0,
      flexGrow: 1,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({
        rowData,
        cellData,
        container: {
          props: { cellProps },
        },
      }: {
        rowData: { currency_id: number }
        cellData: string
        container: {
          props: { cellProps: { system: { currencies: ICurrency[] } } }
        }
      }) => {
        const currency = getCurrencyObjectById(rowData.currency_id, cellProps.system?.currencies)
        return (
          <ReactTexty>
            {currency?.symbol} {cellData}
          </ReactTexty>
        )
      },
    },
    {
      key: 'currentSum',
      sortable: true,
      dataKey: 'sum_current',
      width: 0,
      flexGrow: 1,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({
        rowData,
        cellData,
        container: {
          props: { cellProps },
        },
      }: {
        rowData: { currency_id: number }
        cellData: string
        container: {
          props: { cellProps: { system: { currencies: ICurrency[] } } }
        }
      }) => {
        const currency = getCurrencyObjectById(rowData.currency_id, cellProps.system?.currencies)
        return (
          <ReactTexty>
            {currency?.symbol} {cellData}
          </ReactTexty>
        )
      },
    },
    {
      key: 'organization',
      dataKey: 'organization_id',
      labelKey: 'name',
      optionsKeys: ['organizations'],
      noTranslation: true,
      sortable: true,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({ rowData, container }: any) => {
        const orgs = container?.props?.cellProps?.system?.organizations || {}
        const org = orgs[rowData.organization_id] || Object.values(orgs).find((o: any) => o.id === rowData.organization_id)
        const orgName = org?.name || 'N/A'
        return (
          <Typography as={ReactTexty}>{orgName}</Typography>
        )
      },
    },
    { ...getDate(t, tableMode), key: 'created' },
  ]
}
