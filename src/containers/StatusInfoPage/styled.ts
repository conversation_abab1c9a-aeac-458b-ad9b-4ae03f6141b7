import styled from 'styled-components'
import { TOP_BAR_BORDER_BOTTOM, TOP_BAR_HEIGHT, TOP_BAR_HEIGHT_XL } from '../../constants'

export const StyledStatusInfoPage = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: calc(100vh - ${TOP_BAR_HEIGHT}px - ${TOP_BAR_BORDER_BOTTOM}px);
  max-width: 570px;
  margin: 0 auto;
  text-align: center;

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
    height: calc(100vh - ${TOP_BAR_HEIGHT_XL}px - ${TOP_BAR_BORDER_BOTTOM}px);
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px) {
    margin: 0 20px;
  }

  .linkToAdmin {
    cursor: pointer;
    font-size: 16px;
    font-weight: 700;
    text-decoration: none;
    color: ${({ theme }) => theme.color.primary.main};
  }
`
