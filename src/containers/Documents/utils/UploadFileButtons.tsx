import { Link as RouterLink } from 'react-router'
import { Button, FlexRow, Icon, Typography, UiLink } from '@aidsupply/components'
import { useTheme } from 'styled-components'
import { useTranslation } from 'react-i18next'

interface IUploadFileButtonsProps<T extends { is_being_processed?: boolean }> {
  formValues: T
  uploadType: string
  setUploadType: React.Dispatch<React.SetStateAction<'file' | 'table'>>
  setIsUploadFileActive?: (value: boolean) => void
  isUploadFileActive?: boolean
  isManually?: boolean
};

const UploadFileButtons = <T extends { is_being_processed?: boolean }>({    
  formValues,
  setUploadType,
  setIsUploadFileActive,
  isUploadFileActive,
  uploadType,
  isManually = false
}: IUploadFileButtonsProps<T>) => {
  const theme = useTheme()
  const { t } = useTranslation(['table'])
  return (
    <FlexRow gap="10px" margin="20px 0">
      {!isManually && (
      <Button
        size="small"
        padding="10px 0px"
        variant="primary"
        backgroundColor={formValues.is_being_processed ? theme.color.general.gray3 : theme.color.primary.main}
        fullWidth
        onClick={() => {
          setUploadType('file')
          setIsUploadFileActive?.(!isUploadFileActive)
        }}
        // TODO: remove formValues?.inquirer_id after implementing the inquiry_items upload from file
        // @ts-ignore
        disabled={formValues.is_being_processed || formValues?.inquirer_id || formValues?.invoice_items || formValues?.inventory_items}
        type="button"
        >
        <Icon
          name="upload"
          fill={theme.color.general.light}
          height={20}
          width={20}
          wrapperHeight={20}
          wrapperWidth={20}
          margin=" 0 3px 0 0"
        />
        <Typography
          text={t('addFromFile')}
          color={theme.color.general.light}
          textTransform="none"
          fontWeight={600}
        />
        <Icon
          name="chevronDown"
          fill={theme.color.general.light}
          height={8}
          width={8}
          margin="4px 0 0 5px"
          style={{ transform: isUploadFileActive ? 'rotate(0deg)' : 'rotate(180deg)' }}
        />
      </Button>
      )}
      <UiLink Link={RouterLink} to="./add" style={{ width: '100%', display: 'block' }}>
      <Button
        size="small"
        padding="10px 0px"
        variant="bordered"
        fullWidth
        onClick={() => {
          setUploadType('table')
          setIsUploadFileActive?.(false)
        }}
        disabled={formValues.is_being_processed}
        type="button"
      >
      <Icon
        name="plus2"
        fill={uploadType === 'table' ? 'white' : 'black'}
        height={20}
        width={20}
        wrapperHeight={20}
        wrapperWidth={20}
        stroke={formValues.is_being_processed ? theme.color.general.gray3 : theme.color.general.dark}
        color={formValues.is_being_processed ? theme.color.general.gray3 : theme.color.general.dark}
      />
      <Typography
        text={t('addManually')}
        textTransform="none"
        fontWeight={600}
        color={formValues.is_being_processed ? theme.color.general.gray3 : theme.color.general.dark}
        cursor={formValues.is_being_processed ? 'not-allowed' : 'pointer'}
      />
      </Button>
      </UiLink>
    </FlexRow>
  )
}

export default UploadFileButtons
