import { Icon, Tooltip } from '@aidsupply/components'
import { getDate, getRecipient } from '../../../components/config/columns'
import InquiryNumberWithStatusBar from '../../../components/InquiryNumberWithStatusBar/InquiryNumberWithStatusBar'
import { TLanguages } from '../../../locales'
import { ISystem, TableModeType } from '../../../redux-saga/reducers/data'
import { headerRenderer } from '../../../utils/table'
import { getOption, IRowData } from '../../../components/config/columns'
import { NameOptionType } from '../../../commonTypes'
import { STATES_ICONS, STATUSES_ICONS } from '../../../components/config/table'
import AddressTableCell from '../../../components/Table/components/AddressTableCell'
import { StyledInquiryNumberColumn } from './styled'

export const inquiriesColumnsConfig = (
  lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    {
      key: 'inquiryNumber',
      sortable: true,
      dataKey: 'number',
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({ cellData, rowData, column }: { cellData: string, rowData: IRowData, column: { key: string } }) => {
        const { open_demands, closed_demands, in_progress_demands } = rowData as { open_demands: number, closed_demands: number, in_progress_demands: number }
        const totalDemands = open_demands + closed_demands + in_progress_demands
        const rowOption = rowData[getOption(rowData) as NameOptionType]
        const key = typeof rowOption === 'object' ? rowOption?.id : rowOption;
        const iconName = getOption(rowData) === 'status' ? STATUSES_ICONS[key as string] : STATES_ICONS[key as string]

        const getPercentageObject = (key: string, totalDemands: number) => {
          return {
            percentage: totalDemands === 0 ? 0 : ((rowData as Record<string, number>)[key] / totalDemands) * 100,
            tooltipTitle: t(key),
          }
        }
        const percentages = [
          getPercentageObject('open_demands', totalDemands),
          getPercentageObject('closed_demands', totalDemands),
          getPercentageObject('in_progress_demands', totalDemands),
          { percentage: 100, tooltipTitle: null },
        ]

        return <StyledInquiryNumberColumn>
          {iconName ? (
            <Tooltip
              left={tableMode === 'table' ? '8px' : undefined}
              right={tableMode === 'table' ? undefined : '8px'}
              text={t(`statuses:${key}`)}
              arrowPosition={tableMode === 'table' ? 'left' : 'right'}
            >
              <Icon
                key={column.key}
                name={iconName}
                width={16}
                height={16}
              />
            </Tooltip>
          ) : ''}
          <InquiryNumberWithStatusBar cellData={cellData} percentages={percentages} />
        </StyledInquiryNumberColumn>
      },
    },
    { ...getDate(t, tableMode), key: 'date' },
    {
      key: 'type',
      sortable: true,
      dataKey: 'recipient_organization_type',
      cellRenderer: ({ cellData, container }: {
        cellData: string,
        container: Record<string, Record<string, ISystem>>
      }) => {
        const {
          props: {
            // @ts-ignore
            cellProps: { system },
          },
        } = container
        const organizationSystemType = cellData && system.organizationTypes[cellData]
        return organizationSystemType?.label?.[lng]
      },
      width: 0,
      flexGrow: 1,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'address',
      labelKey: `translations.${lng}`,
      dataKey: 'recipient_city',
      width: 0,
      flexGrow: 1.5,
      sortable: true,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({
        cellData,
        rowData,
        container: {
          props: {
            cellProps: {
              // @ts-ignore
              system: { organizations },
            },
          },
        },
      }: {
        cellData: string,
        rowData: IRowData,
        container: Record<string, Record<string, ISystem>>
      }) => {
        const country_id = organizations[rowData.recipient_id]?.country_id
        const region_id = organizations[rowData.recipient_id]?.region_id

        const address = {
          city: cellData,
          country_id: country_id,
          region_id: region_id,
        }

        return <AddressTableCell rowData={address} />
      },
    },
    { ...getRecipient(t, tableMode || 'table'), flexGrow: 1 },
  ]
}
