import { getAvailableTranslation } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { useMappedState } from '../../../hooks'
import ReactTexty from '../../../lib/react-texty'
import { DEFAULT_LANGUAGE } from '../../../locales'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'
import { countryCodeToFlagEmoji } from '../../../utils/common'
import { ISystem } from '../../../redux-saga/reducers/data'

const AddressTableCell = ({ rowData, onlyFlag }: { rowData: Record<string, unknown>, onlyFlag?: boolean }) => {
  const {
    i18n: { language: lng },
  } = useTranslation()

  const system = useMappedState(selectAllSystemCollections) as ISystem

  const { city, country_id, region_id } = rowData || {}

  const country = getAvailableTranslation(
    system?.countries?.[country_id as unknown as string]?.translations || '',
    DEFAULT_LANGUAGE,
    lng
  )
  const region = getAvailableTranslation(
    system?.country_subdivisions?.[region_id as unknown as string]?.translations || '',
    DEFAULT_LANGUAGE,
    lng
  )

  const countryCode = system?.countries?.[country_id as unknown as string]?.alpha2code
  const countryFlag = countryCode && countryCodeToFlagEmoji(countryCode)

  const address = [country, region, city].filter(Boolean).join(', ')

  if (onlyFlag) {
    return <ReactTexty>
      {countryFlag}
    </ReactTexty>
  }

  return (
    <ReactTexty>
      {countryFlag}&nbsp;&nbsp;{address}
    </ReactTexty>
  )
}

export default AddressTableCell
