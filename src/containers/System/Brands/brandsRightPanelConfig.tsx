import { slugify } from 'transliteration'
import { COUNTRY } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'

export const brandsRightPanelConfig = (): IFormConfig => {
  return {
    validationRules: {
      country_id: ['required'],
      name: [
        'required',
        {
          type: 'keyInListAbsent',
          customRuleName: 'nameAlreadyExists',
          withSuccess: true,
          dataForValidation: {
            listKey: 'brands',
            searchKey: 'slug',
            transformValue: (value: string) => slugify(value),
          },
        },
      ],
    },
    withTabs: ['description'],
    fields: {
      noTitle: [{
        key: 'state',
        label: 'state',
        optionsKeys: ['states'],
        component: 'dropdown',
      }],
      general: [
        {
          key: 'name',
          label: 'name',
          disabledWhenNotEmpty: true,
          required: true,
        },
        {
          key: 'name_en',
          label: 'nameInEnglish',
        },
        COUNTRY,
        {
          key: 'email',
          label: 'email',
          placeholder: '<EMAIL>',
        },
        {
          key: 'website',
          label: 'website',
          placeholder: 'https://domain.com',
        },
      ],
      translations: [
        {
          key: 'description',
          label: 'description',
          type: 'richText',
        },
      ],
      photos: [],
    },
  }
}
