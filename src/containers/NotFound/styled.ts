import styled from 'styled-components'

export const StyledNotFound = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
`

export const StyledErrorWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  justify-content: center;
  padding: 20px;
  align-items: center;

  button {
    margin-bottom: 20px;
  }

  span {
    margin: 0 auto;
  }
`
