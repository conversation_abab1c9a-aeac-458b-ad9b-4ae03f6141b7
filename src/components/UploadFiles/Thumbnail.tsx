import clsx from 'clsx'
import { useTheme } from 'styled-components'
import { FlexRow, IMAGEKIT_PARAMS_CONFIG, Icon, Image, Typography } from '@aidsupply/components'
import ReactTexty from '../../lib/react-texty'
import { IMAGEKIT_URL } from '../../constants'
import { formatSizeUnits } from '../../utils/formatSizeUnits'
import { markFileDraftedOrPosted } from '../../utils/common'
import { UpdateInput } from '../Form/interfaces'
import { StyledThumbnail } from './styled'
import { useTranslation } from 'react-i18next'

export interface IFileDrafted {
  id: number
  url: string
  meta: {
    file_size: number
    file_name_original: string
  } 
}

interface IThumbnail {
  fileSize?: number
  changedFilesStates?: Record<string, 'drafted' | 'posted' | 'deleted'>
  disableImagekit?: boolean
  entityType?: string
  error?: boolean
  file?: IFileDrafted
  fnDelete?: (src: string) => void
  isDeleted?: boolean
  isReadOnly?: boolean
  loading?: boolean
  fileGroup?: string
  values?: Record<string, {
    active: IFileDrafted[]
    disabled?: IFileDrafted[]
  }>
  saved?: boolean
  src: string
  updateInput?: UpdateInput
  fileName?: string
  isPrimary?: boolean
  t?: (key: string) => string
  isActive?: boolean
  filesType?: 'photos' | 'files'
  isMultipleUploadDisabled?: boolean
}

const Thumbnail = ({
  changedFilesStates = {},
  disableImagekit,
  entityType,
  error,
  file,
  fnDelete = () => null,
  isDeleted,
  isReadOnly,
  fileGroup,
  values,
  saved,
  src,
  updateInput,
  fileSize,
  fileName,
  isPrimary,
  isActive,
  filesType = 'photos',
  isMultipleUploadDisabled,
}: IThumbnail) => {
  const theme = useTheme()
  const { t } = useTranslation('forms')
  
  if (!src) {
    return null
  }

  const changeFileState = (fileGroup?: string, isDeleted?: boolean) => {
    if (fileGroup && file && values) {
      const { updatedValues, updatedFilesState } = markFileDraftedOrPosted(values?.[fileGroup], file, isDeleted)

      const newFiles = {
        ...values,
        [fileGroup]: updatedValues,
      }

      updateInput?.({ target: { name: filesType, value: newFiles } })
      updateInput?.({
        target: { name: 'filesNewStates', value: { ...changedFilesStates, ...updatedFilesState } },
      })
    }
  }

  const setPrimaryPhoto = (fileId?: number) => {
    if (fileId) {
      const primaryPhotoKey =
        entityType === 'organizations' || entityType === 'users'
          ? (fileGroup === 'logos' && 'logo_photo_id') || (fileGroup === 'banners' && 'banner_photo_id')
          : 'photo_id'
      updateInput?.({ target: { name: primaryPhotoKey as string, value: fileId } })
    }
  }

  const handleDownload = () => {
    if (!file?.url) return
  
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.meta?.file_name_original || 'file.xlsx'
  
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  const transformedSize = fileSize && formatSizeUnits(fileSize)
  const fileType = fileName?.split('.')[1]
  const iconName =
    fileType === 'xls' || fileType === 'xlsx'
      ? 'xlsFileType'
      : fileType === 'pdf'
      ? 'pdfFileType'
      : fileType === 'doc' || fileType === 'docx'
      ? 'docFileType'
      : fileType === 'txt'
      ? 'txtFileType'
      : 'defaultFileType'

  return (
    <>
      <StyledThumbnail
        className={clsx('thumbnail', error && 'error', saved && 'saved', isDeleted && 'isDeleted')}
      >
        {filesType === 'photos' ? (
          <Image
            alt="thumbnail"
            imagekitParams={IMAGEKIT_PARAMS_CONFIG.crm.thumbnail}
            imagekitUrl={IMAGEKIT_URL}
            src={src}
            disableImagekit={disableImagekit}
            maxWidth={40}
            height={40}
          />
        ) : (
          <Icon name={iconName} width={30} height={30} />
        )}
        <div style={{ flexGrow: 1, overflow: 'hidden', display: 'block' }}>
          <Typography text={fileName} type="caption2" as={ReactTexty} />
          <FlexRow>
            <Typography
              text={`${fileType} • ${transformedSize}`}
              type="caption3"
              color={theme.color.general.gray3}
            />
            {isActive && filesType === 'photos' && (
              <Icon
                onClick={() => setPrimaryPhoto(file?.id)}
                fill={isPrimary ? theme.color.general.gray2 : ''}
                name="star"
                width={16}
                height={16}
                wrapperHeight={20}
                wrapperWidth={20}
              />
            )}
            {isPrimary && <Typography text={t('primaryPhoto')} type="button2" />}
          </FlexRow>
        </div>
        {
          <Icon
            name="download"
            className="download"
            onClick={handleDownload}
            size="small"
          />
        }
        {!isReadOnly && (
          <Icon
            className="deleteIcon"
            onClick={() => (saved ? changeFileState(fileGroup, !isDeleted) : fnDelete(src))}
            fill={theme.color.general.gray6}
            name={isDeleted ? 'reload' : 'trashBin'}
            width={16}
            height={16}
            wrapperHeight={20}
            wrapperWidth={20}
            disabled={isPrimary || (isDeleted && isMultipleUploadDisabled)}
          />
        )}
      </StyledThumbnail>
    </>
  )
}

export default Thumbnail
