import { TakeableChannel } from 'redux-saga'
import { NavigateFunction, Location } from 'react-router'
import Cookies, { CookieGetOptions } from 'universal-cookie'
import { put, select, takeLatest } from 'redux-saga/effects'
import { buildRequest, fetchData, showError } from '../../api'
import {
  getCurrentUser,
  getCurrentUserError,
  getCurrentUserSuccess,
  ICurrentUser,
  signIn,
  signInError,
  signInSuccess,
  signOut,
  signUp,
  signUpError,
  signUpSuccess,
  userRequestResetPassword,
  userRequestResetPasswordError,
  userRequestResetPasswordSuccess,
  userResetPassword,
  userResetPasswordError,
  userResetPasswordSuccess,
  userUpdateName,
} from '../reducers/user'
import { popupAlertShow } from '../reducers/common'
import { selectUserSystemParams } from '../selectors'
import { getNavigate } from '../../utils/common'
import { Status } from '../../commonTypes'

export interface IResponse {
  [key: string]: unknown
  detail?: string
}

export interface IResponseUserSignIn {
  access_token: string
  expires_in: number
  refresh_token: string
  scope: string
  token_type: string
  detail?: string
}

const cookies = new Cookies()

function* doUserSignUp({
  payload,
}: {
  payload: {
    full_name: string
    organization_name: string
    email: string
    password: string
    navigate: NavigateFunction
  }
}) {
  const { full_name, organization_name, email, password, navigate } = payload

  try {
    const request: Request = yield buildRequest({
      apiMethod: 'POST',
      type: 'signUp',
      requestBody: { full_name, organization_name, email, password },
    })
    const response: IResponse = yield fetchData(request)

    yield showError(response)

    yield put(signUpSuccess({ email: response.email }))
    navigate('/pending')

    yield put(
      popupAlertShow({
        contentKey: 'signUpSuccess',
        type: 'success',
        timeout: 10000,
      })
    )
  } catch (error) {
    yield put(signUpError())
    console.error(error)
  }
}

function* doGetCurrentUser({
  payload,
}: {
  payload: {
    navigate: NavigateFunction
    location?: Location
  }
}) {
  const { navigate, location } = payload
  try {
    const request: Request = yield buildRequest({ apiMethod: 'GET', type: 'currentUser' })
    const response: IResponse = yield fetchData(request)

    yield showError(response)

    yield put(getCurrentUserSuccess(response as ICurrentUser))

    if (response?.email_verified) {
      getNavigate(navigate, response.status as Status, location)
    } else if ('email_verified' in response && !response.email_verified) {
      navigate('/confirm-email')
    }

    return response?.email_verified
  } catch (error) {
    console.error(error)
    yield put(getCurrentUserError())
    const { isAuthenticated, inProgress } = yield select(selectUserSystemParams)

    if (!isAuthenticated && !inProgress) {
      navigate('/signin')
    }
  }
}

function* doUserSignIn({
  payload,
}: {
  payload: {
    email: string
    password: string
    navigate: NavigateFunction
  }
}) {
  const { email, password } = payload
  try {
    const request: Request = yield buildRequest({
      apiMethod: 'POST',
      type: 'signIn',
      requestBody: { email, password },
    })

    const response: IResponseUserSignIn = yield fetchData(request)

    yield showError(response)

    if (response.access_token) {
      yield put(signInSuccess({ token: response.access_token }))
    }
    if (response.expires_in) {
      cookies.set('tokenExp', Date.now() + response.expires_in * 1000, { path: '/' })
    }
    if (response.refresh_token) {
      cookies.set('tokenVal', response.refresh_token, { path: '/', httpOnly: false })
    }

    yield doGetCurrentUser({ payload })
  } catch (error) {
    yield put(signInError())
    console.error(error)
  }
}

export function* doRefreshToken() {
  try {
    const refreshTokenFromCookies = cookies.get('tokenVal', { path: '/' } as CookieGetOptions)

    if (!refreshTokenFromCookies) {
      return
    }

    const request: Request = yield buildRequest({
      apiMethod: 'POST',
      type: 'refreshToken',
      requestBody: { refresh_token: refreshTokenFromCookies },
    })

    const response: IResponseUserSignIn = yield fetchData(request)

    if (!response?.access_token && !response?.refresh_token) {
      cookies.remove('tokenVal', { path: '/' })
      cookies.remove('tokenExp', { path: '/' })
    }

    if (response.access_token) {
      yield put(signInSuccess({ token: response.access_token }))
    }
    if (response.expires_in) {
      cookies.set('tokenExp', Date.now() + response.expires_in * 1000, { path: '/' })
    }

    if (response.refresh_token) {
      cookies.set('tokenVal', response.refresh_token, { path: '/', httpOnly: false })
    }

    return response.access_token
  } catch (error) {
    console.error(error)
  }
}

function doUserLogout() {
  if (cookies) {
    cookies.remove('tokenVal', { path: '/' })
    cookies.remove('tokenExp', { path: '/' })
    window.location.reload()
  }
}

function* doUserResetPassword({
  payload,
}: {
  payload: {
    password_new: string
    password_old: string
    navigate: NavigateFunction
  }
}) {
  const { password_old, password_new, navigate } = payload
  try {
    const request: Request = yield buildRequest({
      apiMethod: 'POST',
      type: 'resetPassword',
      requestBody: { password_new, password_old },
    })
    const response: IResponse = yield fetchData(request)
    yield showError(response)
    yield put(userResetPasswordSuccess())
    navigate('/signin')
  } catch (error) {
    yield put(userResetPasswordError())
    console.error(error)
  }
}

function* doUserRequestResetPassword({ payload }: {
  payload: {
    email: string
    navigate: NavigateFunction
  }
}) {
  const { email, navigate} = payload
  try {
    const request: Request = yield buildRequest({
      apiMethod: 'POST',
      type: 'requestResetPassword',
      requestBody: { email },
    })
    
    const response: IResponse = yield fetchData(request)

    yield showError(response)

    if (navigate) {
      navigate('/password-request-sent')
    } else {
      yield put(
        popupAlertShow({
          contentKey: 'passRecoverSentText',
          contentParams: { email },
          type: 'success',
          withCloseButton: true,
          iconName: 'mailSend',
        })
      )
    }
    yield put(userRequestResetPasswordSuccess())
  } catch (error) {
    console.error(error)
    yield put(userRequestResetPasswordError())
  }
}

function* doUpdateUserName({
  payload,
}: {
  payload: {
    id: number
    type: string
    requestBody: {
      full_name: string
    }
  }
}) {
  const { id, type, requestBody } = payload

  try {
    const request: Request = yield buildRequest({
      apiMethod: 'PATCH',
      type,
      requestBody,
      apiUrlParam: id,
    })
    const response: IResponse = yield fetchData(request)
    yield showError(response)

    yield put(
      popupAlertShow({
        contentKey: 'updatedUserName',
        type: 'success',
        withCloseButton: true,
        iconName: 'checkRounded',
      })
    )
  } catch (error) {
    console.error(error)
    yield put(popupAlertShow({ contentKey: 'errorInSavingData', type: 'error' }))
  }
}

export default function* userSaga() {
  yield takeLatest(signIn.type as unknown as TakeableChannel<unknown>, doUserSignIn)
  yield takeLatest(signUp.type as unknown as TakeableChannel<unknown>, doUserSignUp)
  yield takeLatest(getCurrentUser.type as unknown as TakeableChannel<unknown>, doGetCurrentUser)
  yield takeLatest(userResetPassword.type as unknown as TakeableChannel<unknown>, doUserResetPassword)
  yield takeLatest(signOut.type as unknown as TakeableChannel<unknown>, doUserLogout)
  yield takeLatest(userUpdateName.type as unknown as TakeableChannel<unknown>, doUpdateUserName)
  yield takeLatest(userRequestResetPassword.type as unknown as TakeableChannel<unknown>, doUserRequestResetPassword)
}
