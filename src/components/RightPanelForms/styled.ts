import { Dropdown } from '@aidsupply/components'
import styled from 'styled-components'

export const StyledTabsWrapper = styled.div`
    &.photos {
      margin-top: -5px;
      padding-bottom: 20px;
    }

    &.withTabsHidden {
      .tabs > div:first-child {
        display: none;
      }
    }
`

export const StyledActions = styled(Dropdown)`
  .menuButton {
    width: 100px;
    justify-content: center;
    padding: 9px 10px 9px 14px;
  }

	.iconChevron {
    transform: rotate(270deg);
  }

  .opened {
    right: 0;
    left: auto;
    margin-top: 10px;
    width: 162px;
    z-index: 3;
    padding: 0;
    align-items: start;

    > div:first-child {
      background-color: rgba(116, 116, 128, 0.08);
      width: 100%;
      justify-content: start;
      padding: 6px 12px;
      cursor: default;
    }

    & > * {
      border: none;
      padding: 6px 12px;
      position: relative;
      justify-content: start;
      width: 100%;

      &:hover {
        background-color: ${({ theme }) => theme.color.general.gray1};
      }
    }
  }
`
