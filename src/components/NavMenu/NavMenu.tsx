import { Fragment, useRef } from 'react'
import { useDispatch } from 'react-redux'
import clsx from 'clsx'
import { selectAllSystemCollections, selectDataTypeObject, selectIsNavMenuExtended, selectUserDetails, selectUserRole } from '../../redux-saga/selectors'
import { toggleNavMenuExtended } from '../../redux-saga/reducers/common'
import useClickOutside from '../../hooks/useClickOutside'
import { useMappedState } from '../../hooks'
import { isAccessAllowed } from '../../utils/roles'
import { ICurrentUser, UserRole } from '../../redux-saga/reducers/user'
import MenuGroup from './MenuGroup'
import { DOCUMENTS, ENTITIES, IEntities, INVENTORY, ISubmenu } from './config'
import { StyledLine, StyledNavMenu } from './styled'

interface INavMenu {
  isDesktop: boolean
  isTablet: boolean
  isMobile: boolean
  onClick?: () => void
}

const NavMenu = ({ isDesktop, isTablet, isMobile, onClick }: INavMenu) => {
  const dispatch = useDispatch()
  const navMenuRef = useRef(null)
  const isNavMenuExtended = useMappedState(selectIsNavMenuExtended) as boolean
  const role = useMappedState(selectUserRole) as UserRole
  const urlPageTypeObj = useMappedState(selectDataTypeObject) as ISubmenu
  const { organization_id } = useMappedState(selectUserDetails) as ICurrentUser
  const system = useMappedState(selectAllSystemCollections)

  useClickOutside(navMenuRef, () => dispatch(toggleNavMenuExtended(false)));

  const isActiveParent = (key: string) => {
    return urlPageTypeObj ? urlPageTypeObj?.route?.startsWith(`/${key}`) : key === 'dashboard'
  }

  let allowedEntities = ENTITIES.filter(
    (entity) => isAccessAllowed(entity.accessLevel, role) && !entity?.hideFromNavMenu
  )

  const isMerchant = system?.organizations?.[organization_id as number]?.roles?.includes('merchant')

  if (isMerchant) {
    const hasInventory = allowedEntities.includes(INVENTORY)
  
    if (!hasInventory) {
      const index = allowedEntities.indexOf(DOCUMENTS)
  
      if (index !== -1) {
        allowedEntities = [
          ...allowedEntities.slice(0, index + 1),
          INVENTORY,
          ...allowedEntities.slice(index + 1),
        ];
      } else {
        allowedEntities = [INVENTORY, ...allowedEntities];
      }
    }
  }

  return (
    <StyledNavMenu
      ref={navMenuRef}
      className={clsx(
        isNavMenuExtended ? 'extended' : 'condensed',
        isTablet && 'tabletView',
        isMobile && 'mobileView'
      )}
      onMouseOver={isDesktop ? () => dispatch(toggleNavMenuExtended(true)) : undefined}
      onMouseOut={isDesktop ? () => dispatch(toggleNavMenuExtended(false)) : undefined}
    >
      <div className="topWrapper">
        {allowedEntities.length ? <StyledLine className={isNavMenuExtended ? 'extended' : 'condensed'}/> : null}
        {allowedEntities.map((entityObj, entityIndex) => {
          const parentEntity = (entityObj.items) && entityObj 
          
          return (
            <Fragment key={entityIndex}>
              <MenuGroup
                parentEntity={parentEntity as IEntities}
                entitiesArr={entityObj.items as ISubmenu[] || [entityObj as ISubmenu]}
                isExtended={isNavMenuExtended}
                isMobile={isMobile}
                onClick={onClick}
                role={role}
                isActiveParent={isActiveParent(parentEntity?.key as string)}
                withExpansionPanel={allowedEntities.length > 5}
              />
              {entityIndex !== allowedEntities.length - 1 && <StyledLine />}
            </Fragment>
          )
        })}
      </div>
    </StyledNavMenu>
  )
}

export default NavMenu
