import { getPriceWithCurrency } from '@aidsupply/components' 
import { getDate, getRecipient, getSelectCellValue, getSupplier, getWarehouse } from '../../../components/config/columns'
import ReactTexty from '../../../lib/react-texty'
import { TLanguages } from '../../../locales'
import { ISystem, TableModeType } from '../../../redux-saga/reducers/data'
import { getCurrencyObjectById } from '../../../utils/common'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { ICurrency, IOrder } from '../../../commonInterfaces'
import { ICurrentUser } from '../../../redux-saga/reducers/user'

export const ordersColumnsConfig = (
  _lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    getNameByMode({
      t,
      dataKey: 'orderNumber',
      option: null,
      tableMode: tableMode || 'table',
    }),
    getDate(t, tableMode),
    {
      key: 'inquiryNumber',
      sortable: true,
      dataKey: 'inquiry_id',
      labelKey: 'number',
      optionsKeys: ['inquiries'],
      noTranslation: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: getSelectCellValue,
      headerRenderer: headerRenderer(t, tableMode),
    },
    getRecipient(t, tableMode || 'table'),
    getSupplier(t, tableMode || 'table'),
    getWarehouse(t, tableMode || 'table'),
    {
      key: 'sum',
      sortable: true,
      dataKey: 'total',
      width: 0,
      flexGrow: 0.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({
        rowData,
        cellData,
        container: {
          props: { cellProps },
        },
      }: {
        rowData: IOrder
        cellData: number
        container: {
          props: {
            cellProps: {
              is_read_only: boolean,
              lng: TLanguages
              system: ISystem
              user: ICurrentUser
            }
          }
        }
      }) => {
        const currency = cellProps?.system?.currencies && getCurrencyObjectById(rowData.currency_id as number, cellProps?.system?.currencies) as ICurrency

        return (
          <ReactTexty>{currency ? getPriceWithCurrency(cellData || 0, currency) : cellData}</ReactTexty>
        )
      },
    },
  ]
}
