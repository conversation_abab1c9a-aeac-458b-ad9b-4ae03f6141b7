import { useContext, useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { Navigate, Outlet, useLocation, useNavigate } from 'react-router'
import { isObjectEmpty, Loader, ScreenContext, usePrevious } from '@aidsupply/components'
import { useMappedState } from '../../hooks'
import { selectAllSystemCollections, selectIsNavMenuExtended, selectIsNotificationsOpened, selectUserStatus } from '../../redux-saga/selectors'
import { useWebsockets } from '../../hooks/useWebsockets'
import NotificationsDashboard from '../../components/Notification/NotificationsDashboard'
import NavMenu from '../../components/NavMenu'
import { getEntityByRoute, getNavigate } from '../../utils/common'
import { popupAlertHideAll, toggleLeftPanelExtended, toggleRightPanelExtendedWider } from '../../redux-saga/reducers/common'
import { setDataType } from '../../redux-saga/reducers/data'
import { ISubmenu } from '../../components/NavMenu/config'
import { filterBackToInitialState } from '../../redux-saga/reducers/filters'
import { StyledContainer, StyledMainWrapper } from './styled'

const MainWrapper = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()

  const { width, xl, sm } = useContext(ScreenContext)
  const isTablet =  width && width <= xl
  const isDesktop =  width && width > xl
  const isMobile = width && width < sm

  const status = useMappedState(selectUserStatus)
  const isNotificationsOpened = useMappedState(selectIsNotificationsOpened)
  const isNavMenuExtended = useMappedState(selectIsNavMenuExtended)

  const { pathname } = location
  const typeData = getEntityByRoute(pathname)
  const prevType = usePrevious(typeData?.key)
  const system = useMappedState(selectAllSystemCollections)

  useWebsockets()

  useEffect(() => {
    if (typeData?.key !== prevType) {
      dispatch(popupAlertHideAll())
      dispatch(setDataType({data: typeData as ISubmenu}))

      // TODO: uncomment when needed
      // if (['products', 'inquiry_items'].includes(typeData.key)) {
      //   dispatch(cleanBasket())
      // }
      dispatch(filterBackToInitialState())
      dispatch(toggleRightPanelExtendedWider(false))
      dispatch(toggleLeftPanelExtended(false))
    }
  }, [pathname, prevType, typeData])

  useEffect(() => {
    if (status) {
      getNavigate(navigate, status, location)
    }
  }, [status])

  // TODO: rid of hardcode
  const isForbidden = false;

  if (isForbidden) {
    return <Navigate replace to="/404" />
  }

  return (
    <StyledMainWrapper className={isNotificationsOpened ? 'extended' : ''}>
      {isObjectEmpty(system) ? (
        <div className="loaderWrapper">
          <Loader size="60px" top={`calc(50% - 30px)`} left={`calc(50% - 30px)`} />
        </div>
      ) : (
        <>
          {status === 'active' && <NavMenu isDesktop={isDesktop} isTablet={isTablet} isMobile={isMobile}/>}
          <StyledContainer className={isNavMenuExtended ? 'extended' : ''}>
            <Outlet/>
          </StyledContainer>
        </>
      )}
      <NotificationsDashboard />
    </StyledMainWrapper>
  )
}

export default MainWrapper
