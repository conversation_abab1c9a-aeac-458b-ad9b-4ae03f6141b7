import styled from 'styled-components'
import { Menu } from '@aidsupply/components'
import { HEADER_HEIGHT, TOP_BAR_BORDER_BOTTOM, TOP_BAR_HEIGHT_XL } from '../../constants'

export const StyledNotification = styled.div`
  width: 32px;
  height: 32px;
  border: ${({ theme }) => `1px solid ${theme.color.general.gray4}`};
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center; 
  position: relative;

  &:hover {
    cursor: pointer;
  }

  .iconNotificationOn {
    position: absolute;
    top: -4px;
    right: -6px;

    &:before {
      content: '';
      position: absolute;
      display: block;
      width: 12px;
      height: 12px;
      top: -2px;
      right: 2px;
      border-radius: 50%;
      border: ${({ theme }) => `2px solid ${theme.color.general.light}`};
    }
  }
`

export const StyledNotificationItem = styled.div`
  margin: 10px 0;
  border-bottom: 1px solid ${({ theme: { color } }) => color.general.gray1};
  display: flex;
  justify-content: space-between;
  padding: 15px;

  &:nth-child(2) {
    margin-top: 0;
  }

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  &.new {
    border-bottom: none;
    border-radius: ${({ theme }) => theme.size.border.radius.main};
    background-color: ${({ theme: { color } }) => color.general.gray1};
  }

  a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .icon {
    margin-right: 10px;
    flex: 0 0 auto;
    margin-left: 0;

  .content {
    flex-grow: 1;

    .firstRow {
      padding-bottom: 2px;
    }
  }

  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.info {
      padding-bottom: 5px;
    }

    .label {
      &:first-child {
        margin-right: 10px;
      }
    }
  }

  .time {
    margin-left: 10px;
    white-space: nowrap;
  }
`

export const StyledNotificationsMenu = styled(Menu)`
  .menuList {
    max-height: 65vh;
    overflow: auto;
    padding: 0 20px 20px;
  }

  .menuItem {
    display: block;
    padding: 0;
  }

  .navbarNotificationsLabel {
    text-align: center;
  }

  .navbarNotificationsBadge {
    margin-top: 6px;

    span {
      padding: 2px 6px;
      font-size: ${({ theme }) => theme.font.size.button1.value};
      line-height: ${({ theme }) => theme.font.size.button1.lineHeight};
      font-weight: ${({ theme }) => theme.font.weight.black};
      height: auto;
      width: max-content;
      text-transform: uppercase;
      border-radius: ${({ theme }) => theme.size.border.radius.smaller};
    }
  }

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints.md}px) {
    .menuList {
      transform: translate(-10px, -70%);
    }
  }
`

export const StyledNotificationsHeader = styled.div`
  background-color: ${({ theme }) => theme.color.general.light};
  padding-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  position: sticky;
  top: 0;
  z-index: 10;

  .navbarNotifications & {
    padding-bottom: 10px;
  }

  .clearAll {
    cursor: pointer;
  }

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints.sm}px) {
    .empty & {
      margin-bottom: 20vh;
    }
  }
`

export const StyledNotificationsPanel = styled.div`
    position: absolute;
    top: 0;
    right: 0;
    width: 350px;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
    height: calc(100vh - ${HEADER_HEIGHT}px - ${TOP_BAR_BORDER_BOTTOM}px);
    flex-grow: 1;
    background-color: white;
    z-index: 22;
    padding: 0 20px 20px;

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
      height: calc(100vh - ${TOP_BAR_HEIGHT_XL}px - ${TOP_BAR_BORDER_BOTTOM}px);
    }

    .panel {
      padding: 10px;
      margin-bottom: 15px;

      .icon {
        margin-right: 5px;
      }

      .date {
        color: ${({ theme }) => theme.color.general.gray3};
      }
    }

    &.notificationsExtended {
      transform: translateX(0);
      width: 350px;
      z-index: 25;
      padding: 0 20px 20px;

      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
        z-index: 10;
      }
    }

    @media only screen and (max-width: 890px) {
      flex-basis: 50%;
    }

    &.empty {
      height: 100vh;
    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px) {
      &.notificationsExtended {
        width: 100vw;
        height: 100%;
      }
    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
      margin-bottom: 30px;
    }
`

export const StyledEmptyWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
`

export const StyledEmpty = styled.div`
  padding-top: 20px;
  text-align: center;
  padding-left: 10px;
  padding-bottom: 10px;
`
