import { IFormConfig } from '../../../components/Form/interfaces'

export const reportsRightPanelConfig: IFormConfig = {
  withTabs: ['translations'],
  validationRules: {
    'translations.en': ['required'],
    period: ['required'],
    year: ['required'],
    organization_id: ['required'],
  },
  fields: {
    noTitle: [{
      key: 'state',
      label: 'state',
      optionsKeys: ['states'],
      component: 'dropdown',
    }],
    general: [
      {
        key: 'organization_id',
        label: 'organization',
        required: true,
        optionsKeys: ['organizations'],
        component: 'dropdown',
        noTranslation: true,
        labelKey: 'name',
        valueKey: 'id',
        getDisabled: () => true
      },
      {
        key: 'period',
        label: 'period',
        required: true,
        component: 'dropdown',
        optionsKeys: ['reportTypes'],
      },
      {
        key: 'year',
        label: 'year',
        required: true,
        type: 'number',
        hideButtons: true,
      },
      {
        key: 'month',
        label: 'month',
        required: false,
        component: 'dropdown',
        optionsKeys: ['months'],
      },
    ],
    translations: [
      {
        type: 'translations',
        key: 'translations',
        label: 'title',
        required: true,
      },
    ],
    files: [],
  },
}
