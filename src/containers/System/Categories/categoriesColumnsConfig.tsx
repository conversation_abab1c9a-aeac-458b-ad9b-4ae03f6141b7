import { getAvailableTranslation, TranslationsCell } from '@aidsupply/components'
import ReactTexty from '../../../lib/react-texty'
import { DEFAULT_LANGUAGE, TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { getDate } from '../../../components/config/columns'

export const categoriesColumnsConfig = (lng: TLanguages, t: (key: string) => string, tableMode?: TableModeType) => {
  return [
    getNameByMode({
      t,
      dataKey: 'name',
      option: 'state',
      tableMode: tableMode || 'table',
      lng
    }),
    {
      key: 'title',
      dataKey: `translations.${lng}`,
      sortable: true,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: (props: Record<string, Record<string, unknown>>) => (
        <TranslationsCell
          translations={props.rowData.translations}
          defaultLanguage={DEFAULT_LANGUAGE}
          currentLanguage={lng}
        />
      ),
    },
    {
      key: 'parentCategory',
      optionsKeys: ['categories'],
      dataKey: 'parent_translations',
      sortable: true,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({ cellData }: { cellData: string }) => {
        return cellData ? (
          <ReactTexty>{getAvailableTranslation(cellData, DEFAULT_LANGUAGE, lng)}</ReactTexty>
        ) : (
          ''
        )
      },
    },
    {
      ...getDate(t, tableMode),
      key: 'lastUpdated',
      dataKey: 'updated_at',
    }
  ]
}
