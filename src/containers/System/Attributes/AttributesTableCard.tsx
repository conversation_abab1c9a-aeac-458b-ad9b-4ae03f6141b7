import { Icon, Typography } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { StateType } from '../../../components/Form/interfaces'
import { ITranslations, TLanguages } from '../../../locales'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StyledContentWrapper, StyledStateWrapper } from './styled'

interface IAttributesTableCard {
  id: number
  name: string
  state: StateType
  unit_id: string
  translations: ITranslations
}

const AttributesTableCard = ({ data, className }: { data: IAttributesTableCard, className?: string}) => {
  const { t, i18n: { language: lng }  } = useTranslation('table')
  const { name, id, state, unit_id, translations } = data
  
  return (
    <TableCardStyled key={id} direction='column' gap={1} className={className}>
      <>
      <StyledStateWrapper>
        <StyledContentWrapper>
          <Icon
            name='listBulleted'
            width='20px'
            height='20px' 
          />
          <Typography as={ReactTexty} type="h4">
            {translations[lng as TLanguages] || name}
          </Typography>
        </StyledContentWrapper>
        <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />
      </StyledStateWrapper>
        <Typography as={ReactTexty} type="button2" fontWeight="400" margin="0 0 0 26px">
          {unit_id}
        </Typography>
      </>
    </TableCardStyled>
  )
}

export default AttributesTableCard
