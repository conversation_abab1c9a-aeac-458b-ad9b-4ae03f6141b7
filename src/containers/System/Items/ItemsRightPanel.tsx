import { useNavigate } from 'react-router'
import { object } from 'dot-object'
import { useDispatch } from 'react-redux'
import { slugify } from 'transliteration'
import { isObject } from '@aidsupply/components'
import merge from 'deepmerge'
import { useMappedState } from '../../../hooks'
import { selectSidebarInitialData, selectSystemCollections } from '../../../redux-saga/selectors'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { ITranslations } from '../../../locales'
import { itemsRightPanelConfig } from './itemsRightPanelConfig'

interface IItemsRightPanel {
  id?: string | number
  name?: string
  translations?: ITranslations
  description?: ITranslations
  slug?: string
  sku?: string
  category_id?: number | { id: number }
  brand_id?: number | { id: number }
  [key: string]: unknown
}

const ItemsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const itemInitial = useMappedState(selectSidebarInitialData) as IItemsRightPanel
  const { brands, categories } = useMappedState(
    selectSystemCollections(['attributes', 'brands', 'categories'])
  )

  const isReadOnly = false

  const onFormSubmit = (formValuesChanged: IItemsRightPanel) => {
    const requestBody: IItemsRightPanel = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key) => {
      const value = requestBody[key]
      if (isObject(value as string | number | Record<string, unknown>) && (value as Record<string, unknown>).id) {
        requestBody[key] = (value as Record<string, unknown>).id
      }
    })

    if (formValuesChanged.translations?.en) {
      requestBody.name = formValuesChanged.translations.en as string
    }

    if (formValuesChanged.translations) {
      requestBody.translations = merge((itemInitial?.translations as ITranslations), (requestBody.translations as ITranslations))
    }
    if (formValuesChanged.description) {
      requestBody.description = merge((itemInitial?.description as ITranslations), (requestBody.description as ITranslations))
    }

    if (formValuesChanged.sku || formValuesChanged.category_id || formValuesChanged.brand_id) {
      const category = categories[`${(formValuesChanged.category_id as {
        id: number
      })?.id || itemInitial?.category_id}`] as Record<string, unknown>
      const brand = brands[`${(formValuesChanged.brand_id as {
        id: number
      })?.id || itemInitial?.brand_id}`] as Record<string, unknown>

      requestBody.slug = `${category.slug}_${brand.slug}_${slugify(formValuesChanged?.sku as string || itemInitial?.sku as string)}`
    }

    const dataToSend = {
      id: itemInitial?.id,
      requestBody,
      type: 'items',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return (
    <EditForm
      isReadOnly={isReadOnly}
      onSubmit={onFormSubmit}
      formData={itemsRightPanelConfig()}
    />
  )
}

export default ItemsRightPanel
