import { TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { TLanguages } from '../../../locales'
import { getSelectCellValue } from '../../../components/config/columns'

export const attributesColumnsConfig = (lng: TLanguages, t: (key: string) => string, tableMode?: TableModeType) => {
  return [
    getNameByMode({
      t,
      dataKey: 'name',
      option: 'state',
      tableMode: tableMode || 'table',
      lng
    }),
    {
      key: 'type',
      dataKey: 'attribute_type',
      sortable: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: getSelectCellValue,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'enumeration',
      dataKey: 'enumeration_id',
      labelKey: `translations.${lng}`,
      valueKey: 'id',
      noTranslation: true,
      sortable: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: getSelectCellValue,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'unit',
      dataKey: 'unit_id',
      labelKey: `translations.${lng}`,
      noTranslation: true,
      sortable: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: getSelectCellValue,
      headerRenderer: headerRenderer(t, tableMode),
    },
  ]
}
