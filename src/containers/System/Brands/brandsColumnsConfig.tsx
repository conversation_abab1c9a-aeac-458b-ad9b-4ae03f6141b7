import AddressTableCell from '../../../components/Table/components/AddressTableCell'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'

export const brandsColumnsConfig = (lng: TLanguages, t: (key: string) => string, tableMode?: TableModeType) => {
  return [
    getNameByMode({
      t,
      dataKey: 'name',
      option: 'state',
      tableMode: tableMode || 'table',
    }),
    {
      key: 'country',
      dataKey: 'country_id',
      labelKey: `translations.${lng}`,
      optionsKeys: ['countries'],
      sortable: true,
      width: 0,
      flexGrow: 1,
      noTranslation: true,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({ rowData }: { rowData: Record<string, unknown> }) => {
        return <AddressTableCell rowData={rowData} />
      },
    },
    {
      key: 'website',
      dataKey: 'website',
      sortable: true,
      width: 0,
      flexGrow: 1,
      headerRenderer: headerRenderer(t, tableMode = 'table'),
    },
  ]
}
