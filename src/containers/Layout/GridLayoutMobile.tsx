import { useTheme } from 'styled-components'
import RightPanel from '../../components/RightPanel'
import CentralPanel from './CentralPanel'
import { IGridLayout } from './GridLayout'
import { StyledLeftDrawer } from './styled'

const GridLayoutMobile = ({
  children,
  rightPanelComponent,
  isRightPanelExtended,
  handleToggleRightPanel,
  leftPanelComponent,
  isLeftPanelExtended,
}: IGridLayout) => {
  const theme = useTheme()
  return(
    <>
      {leftPanelComponent && (
        <StyledLeftDrawer
          width="100vw"
          side="left"
          openedValue={isLeftPanelExtended}
          absolutePositioned
          closeIconPadding="6px"
          id="filters"
          closeIconProps={{
            width: 18,
            height: 18,
            wrapperWidth: 32,
            wrapperHeight: 32,
            fill: theme.color.general.gray4,
            strokeWidth: 1.4,
          }}
        >
          { leftPanelComponent }
        </StyledLeftDrawer>
      )}

      <CentralPanel>{children}</CentralPanel>
      {rightPanelComponent && (
        <RightPanel
          width="100vw"
          absolutePositioned
          openedValue={isRightPanelExtended}
          toggleDrawer={handleToggleRightPanel}
        >
          {rightPanelComponent}
        </RightPanel>
      )}
    </>
  )
};

export default GridLayoutMobile
