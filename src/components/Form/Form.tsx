import { useContext, useEffect, useState } from 'react'
import cloneDeep from 'lodash.clonedeep'
import { useParams } from 'react-router'
import merge from 'deepmerge'
import dot from 'dot-object'
import { useTranslation } from 'react-i18next'
import { isObjectEmpty, ScreenContext, usePrevious, validate } from '@aidsupply/components'
import { DEFAULT_VALUES_DATA } from '../../data/defaultValues'
import FormBlock from './FormBlock'
import { useChangingBlocks } from './useChangingBlocks'
import useForm from './useForm'
import { IFieldsProps, IFormProps } from './interfaces'
import { StyledForm } from './styled'
import { DEFAULT_TABS } from './FormBlockTabs'
import { useMappedState } from '../../hooks'
import { selectFormValueChangedByKey } from '../../redux-saga/selectors'
import { useDispatch } from 'react-redux'
import { resetFormValuesChanged, setFormValuesChanged, setFormValuesInitial } from '../../redux-saga/reducers/forms.ts'
import FormButtons from './FormButtons.tsx'

const Form = (props: IFormProps) => {
  const {
    id,
    buttonsAreSticky,
    children,
    className,
    fields: customFields,
    formConfig,
    formErrors: customFormErrors,
    FormWrapper,
    getFormButtons,
    initialValues: initialValuesBeforeCheck,
    initialValuesChanged: initialValuesChangedBeforeCheck,
    inProgress,
    isDraft: customIsDraft,
    isReadOnly,
    isSubmitOnBlur,
    labelKey,
    labelType,
    optionsData,
    onSubmit,
    resetForm,
    serverError,
    setFormErrors: customSetFormErrors,
    // setIsDraft: customSetIsDraft,
    submitByEnterPressed,
    type,
    updateInput: customUpdateInputValue,
    updateSelect: customUpdateSelectValue,
    validationRules: validationRulesInitial = {},
    valuesChanged: customValuesChanged,
    // setValuesChanged: customSetValuesChanged,
    withActions,
    headerType,
    expansionPanelProps,
  } = props
  const dispatch = useDispatch()
  const params = useParams()
  const { rightPanelId } = params
  const isClone = rightPanelId === 'clone'
  const { currentBreakpoint } = useContext(ScreenContext) || {}
  const { t } = useTranslation(['forms', 'validation'])
  const [touchedFields, setTouchedFields] = useState([])
  const [isSubmitPressed, setIsSubmitPressed] = useState(false)

  const changingBlocksDependency = (formConfig as Record<string, unknown> | undefined)
    ?.changingBlocksDependency as string

  const withTabs = ((formConfig as Record<string, unknown>)?.withTabs as string[]) || Object.keys(DEFAULT_TABS)
  const prevId = usePrevious(id)

  const prevInProgress = usePrevious(inProgress)

  const currentChangingBlocksDependencyValue = useMappedState(
    selectFormValueChangedByKey(type, changingBlocksDependency)
  )

  const initialValuesToCheckChangingBlock = initialValuesChangedBeforeCheck || initialValuesBeforeCheck
  const initialChangingBlocksDependencyValue =
    changingBlocksDependency &&
    initialValuesToCheckChangingBlock &&
    (dot.pick(`${changingBlocksDependency}.id`, initialValuesToCheckChangingBlock) ||
      dot.pick(changingBlocksDependency as string, initialValuesToCheckChangingBlock))

  const initialValues = merge.all([
    DEFAULT_VALUES_DATA[initialChangingBlocksDependencyValue || type] || {},
    initialValuesBeforeCheck || {},
    initialValuesChangedBeforeCheck ? dot.object(cloneDeep(initialValuesChangedBeforeCheck)) : {},
  ])

  const initialValuesChanged = isClone ? initialValuesBeforeCheck : initialValuesChangedBeforeCheck

  const {
    formErrors: errors,
    setFormErrors: setErrors,
    updateInputValue,
    updateSelectValue,
    updateCheckboxValue,
    validateField,
  } = useForm({
    initialValues,
    initialValuesChanged,
    validationRules: validationRulesInitial,
    optionsData,
    // setTouchedFields,
    type,
  })

  const formErrors = customFormErrors || errors
  const setFormErrors = customSetFormErrors || setErrors
  const updateInput = customUpdateInputValue || updateInputValue
  const updateSelect = customUpdateSelectValue || updateSelectValue
  const isNotesChanged = false //'notes' in valuesChanged && valuesChanged.notes !== ''

  useChangingBlocks({
    type,
    changingBlocksDependency,
    currentChangingBlocksDependencyValue,
    initialChangingBlocksDependencyValue,
    initialValues,
    setFormErrors,
  })

  const formReset = (event?: { preventDefault: () => void }) => {
    if (event !== undefined) {
      event.preventDefault()
    }
    setFormErrors({})
    dispatch(
      resetFormValuesChanged({
        type,
        initialValuesChanged: initialValuesChanged && dot.dot(initialValuesChanged),
      })
    )
    if (resetForm) {
      resetForm()
    }
  }

  // useEffect(() => {
  //   if ((inProgress === false && !serverError) || (prevId !== id && typeof prevId !== 'undefined')) {
  //     formReset()
  //   }
  // }, [inProgress, serverError, id, prevId])

  useEffect(() => {
    if (inProgress === false && !serverError && prevInProgress) {
      formReset()
    }
  }, [inProgress, serverError, formReset, prevInProgress])

  useEffect(() => {
    if (prevId && id !== prevId) {
      formReset()
    }
  }, [id, prevId])

  useEffect(() => {
    if (initialValues) {
      dispatch(setFormValuesInitial({ type, valuesInitial: initialValues }))
    }
  }, [initialValues])

  useEffect(() => {
    if (customValuesChanged) {
      dispatch(setFormValuesChanged({ type, valuesChanged: dot.dot(customValuesChanged) || {} }))
    }
  }, [customValuesChanged])

  useEffect(() => {
    if (initialValuesChanged) {
      dispatch(setFormValuesChanged({ type, valuesChanged: dot.dot(initialValuesChanged) }))
    }
  }, [initialValuesChanged])

  const getFields = () => {
    if (customFields) {
      return customFields
    }
    const commonFields = (formConfig as Record<string, unknown>)?.fields

    const currentDependencyKey =
      changingBlocksDependency && (currentChangingBlocksDependencyValue?.id || currentChangingBlocksDependencyValue)

    const changingBlocksFromConfig =
      currentDependencyKey && (formConfig as Record<string, string>)?.changingBlocks?.[currentDependencyKey]

    if (!changingBlocksFromConfig) {
      return commonFields
    }

    const withBlocks = !Array.isArray(commonFields)

    return withBlocks
      ? commonFields && merge(commonFields, changingBlocksFromConfig)
      : [...commonFields, ...changingBlocksFromConfig]
  }

  const fields = getFields() as IFieldsProps[]

  const getValidationRules = (formValuesChanged: Record<string, unknown>) => {
    return Object.keys(validationRulesInitial).reduce((acc, curr) => {
      let customRules
      if (
        Object.values(fields)
          .flat()
          .some((field) => {
            const isFieldPresent =
              (field.key === curr ||
                (withTabs.includes(field.key as string) && curr.startsWith(field.key as string))) &&
              (!field.getIsHidden ||
                !field.getIsHidden(
                  (initialValues || {}) as {
                    id?: string | number
                    [key: string]: unknown
                  },
                  optionsData,
                  formValuesChanged || {}
                ))

            if (isFieldPresent && field.validationRules) {
              customRules = { ...field.validationRules }
            }
            return isFieldPresent
          })
      ) {
        return { ...acc, [curr]: validationRulesInitial[curr], ...(customRules || {}) }
      }
      return acc
    }, {})
  }

  const checkErrorsAndSubmit = (e, formValuesChanged, formValues) => {
    if (!isSubmitPressed) {
      setIsSubmitPressed(true)
    }

    const validationRules = getValidationRules(formValuesChanged)

    const submitErrors = validate(validationRules)(formValues, {
      values: formValues,
      ...optionsData,
    })
    setFormErrors(submitErrors)

    if (isObjectEmpty(submitErrors)) {
      if (onSubmit) {
        onSubmit(formValuesChanged, e)
      }
    }
  }

  const formSubmitOnBlur = (e, customValuesForOnBlur) => {
    if (e?.preventDefault) {
      e.preventDefault()
    }
    const valueChangedObBlur = customValuesForOnBlur || { [e.target.name]: e.target.value }

    if ((isReadOnly && !isNotesChanged) || isObjectEmpty(valueChangedObBlur)) {
      return
    }

    checkErrorsAndSubmit(e, valueChangedObBlur, { ...initialValues, ...valueChangedObBlur })
  }

  const getFormBlock = (fields: IFieldsProps[], blockKey?: string, isSecondaryBlock?: boolean) => {
    // const customFields = fields.getFieldsByCustomRule && fields.getFieldsByCustomRule(formValues, optionsData)
    // if (customFields && !customFields.length) {
    //   return
    // }

    return (
      <FormBlock
        blockKey={blockKey as string}
        fields={customFields || fields || []}
        formErrors={formErrors}
        formSubmit={isSubmitOnBlur ? formSubmitOnBlur : undefined}
        // formSubmit={formSubmit}
        formConfig={formConfig}
        FormWrapper={FormWrapper}
        id={id}
        initialValues={merge(
          initialValues,
          initialValuesChangedBeforeCheck ? dot.dot(initialValuesChangedBeforeCheck) : {}
        )}
        isSubmitOnBlur={isSubmitOnBlur}
        isReadOnly={isReadOnly}
        isSecondaryBlock={isSecondaryBlock}
        key={blockKey}
        labelKey={labelKey}
        labelType={labelType}
        optionsData={optionsData}
        // @ts-ignore
        setTouchedFields={setTouchedFields}
        submitByEnterPressed={submitByEnterPressed}
        touchedFields={touchedFields}
        type={type}
        updateInput={updateInput}
        // @ts-ignore
        updateSelect={updateSelect}
        updateCheckbox={updateCheckboxValue}
        validateField={validateField}
        headerType={headerType}
        expansionPanelProps={expansionPanelProps}
        validationRules={validationRulesInitial}
      />
    )
  }

  // const isShowStickyButtons =
  //   (!isReadOnly && withActions && isDraft && (Object.keys(valuesChanged).length > 0 || isCurrentFilesInDraft)) || isNotesChanged

  // VR: form renders multiple times
  return (
    // <StyledForm className={clsx(className, withActions && isDraft && 'buttonsAreShown')}>
    <StyledForm className={className}>
      <form>
        {fields &&
          (Array.isArray(fields)
            ? getFormBlock(fields)
            : Object.keys(fields).map((blockKey) =>
                // @ts-ignore
                getFormBlock(fields[blockKey], blockKey)
              ))}
        {children}
        <FormButtons
          buttonsAreSticky={buttonsAreSticky}
          checkErrorsAndSubmit={checkErrorsAndSubmit}
          customIsDraft={customIsDraft}
          // customSetIsDraft={customSetIsDraft}
          currentBreakpoint={currentBreakpoint}
          formReset={formReset}
          getFormButtons={getFormButtons}
          isClone={isClone}
          inProgress={inProgress}
          isReadOnly={isReadOnly}
          t={t}
          type={type}
          withActions={withActions}
        />
        {/*{isShowStickyButtons && (*/}
        {/*  <StyledFormButtons currentBreakpoint={currentBreakpoint} className={buttonsAreSticky ? 'sticky' : ''}>*/}
        {/*    <div className={buttonsAreSticky ? 'stickyButtonsWrapper' : ''}>*/}
        {/*      <Button variant="bordered" onClick={formReset} disabled={inProgress}>*/}
        {/*        {t('Discard')}*/}
        {/*      </Button>*/}
        {/*      <Button fullWidth type="submit" variant="primary" disabled={inProgress}>*/}
        {/*        {t('Save')}*/}
        {/*      </Button>*/}
        {/*    </div>*/}
        {/*  </StyledFormButtons>*/}
        {/*)}*/}
        {/*{!withActions && getFormButtons && getFormButtons(isDraft)}*/}
      </form>
    </StyledForm>
  )
}

export default Form
