import { MENU_ICONS_BY_TYPES } from '../config'

export type RoleLabel = {
  en: string
  uk: string
}

export interface IUserRole {
  id: string
  level: number
  label: RoleLabel
  entities?: string[]
  entitiesSharedWithUpperRoles?: string[]
}

export interface ISubmenu {
  key: string
  icon: string
  route: string
  label: string
  labelSingle: string
  parentType: string
  accessLevel: number | string[]
  iconName?: string
  iconProps?: Record<string, string>
}

export interface IEntities {
  key: string
  label?: string
  labelSingle?: string
  route: string
  iconName?: string
  items?: ISubmenu[]
  accessLevel: number | string[]
  hideFromNavMenu?: boolean
  iconProps?: Record<string, string>
}

export const USER_ROLES: Record<string, IUserRole>= {
  system: {
    level: 0,
    entities: ['system/.*'],
    label: { en: 'System administrator', uk: 'Системний адміністратор' },
    id: 'system',
  },
  administrator: {
    level: 1,
    entitiesSharedWithUpperRoles: ['system/.*'],
    label: { en: 'Administrator', uk: 'Адміністратор' },
    id: 'administrator',
  },
  organization_owner: {
    level: 2,
    entitiesSharedWithUpperRoles: ['admin/.*'],
    label: { en: 'Organization owner', uk: 'Власник організації' },
    id: 'organization_owner',
  },
  organization_admin: {
    level: 3,
    entitiesSharedWithUpperRoles: ['admin/.*'],
    label: { en: 'Organization administrator', uk: 'Адміністратор організації' },
    id: 'organization_admin',
  },
  organization_member: {
    id: 'organization_member',
    label: { en: 'Organization member', uk: 'Учасник організації' },
    level: 4,
    entitiesSharedWithUpperRoles: ['products', 'inquiry_items', 'inventory/.*', 'documents/.*', 'platform/.*'],
  },
}

export const DASHBOARD_CARDS = [
  {
    key: 'brands',
    label: 'brands',
    src: '/system/brands',
    accessLevel: 1,
  },
  {
    key: 'items',
    label: 'items',
    iconName: 'deliveryBox',
    src: '/system/items',
    accessLevel: 1,
  },
  {
    key: 'categories',
    label: 'categories',
    src: '/system/categories',
    accessLevel: 1,
  },
  {
    key: 'organizations',
    label: 'organizations',
    src: '/admin/organizations',
    accessLevel: 1,
  },
  {
    key: 'users',
    label: 'users',
    src: '/admin/users',
    accessLevel: 1,
  },
  {
    key: 'orders',
    label: 'orders',
    src: '/documents/orders',
    accessLevel: 3,
  },
  {
    key: 'shipments',
    label: 'shipments',
    iconName: 'deliveryTruck2',
    src: '/documents/shipments',
    accessLevel: 3,
  },
  {
    key: 'shipment_weight_kg',
    label: 'weight',
    iconName: 'weightKg',
    accessLevel: 3,
  },
  {
    key: 'saved_co2_kg',
    label: 'saved_co2_kg',
    iconName: 'weightKg',
    accessLevel: 3,
  },
  {
    key: 'saved_water_kg',
    label: 'saved_water_kg',
    iconName: 'weightKg',
    accessLevel: 3,
  },
  {
    key: 'food_portions',
    label: 'food_portions',
    iconName: 'weightKg',
    accessLevel: 3,
  },
  // {
  //   key: 'inquiries',
  //   label: 'inquiries',
  //   src: '/documents/inquiries',
  //   accessLevel: 3,
  // },
  // {
  //   key: 'invoices',
  //   label: 'invoices',
  //   src: '/documents/invoices',
  //   accessLevel: 3,
  // },
  // {
  //   key: 'warehouses',
  //   label: 'warehouses',
  //   src: '/inventory/warehouses',
  //   accessLevel: 1,
  // },
]

export const SUBMENU: Record<string, ISubmenu[]> = {
  documents: [
    {
      key: 'inquiries',
      label: 'inquiries',
      labelSingle: 'inquiry',
      route: '/documents/inquiries',
      icon: MENU_ICONS_BY_TYPES.inquiries,
      parentType: 'documents',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'orders',
      label: 'orders',
      labelSingle: 'order',
      route: '/documents/orders',
      icon: MENU_ICONS_BY_TYPES.orders,
      parentType: 'documents',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'invoices',
      label: 'invoices',
      labelSingle: 'invoice',
      route: '/documents/invoices',
      icon: MENU_ICONS_BY_TYPES.invoices,
      parentType: 'documents',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'shipments',
      label: 'shipments',
      labelSingle: 'shipment',
      route: '/documents/shipments',
      icon: MENU_ICONS_BY_TYPES.shipments,
      parentType: 'documents',
      accessLevel: USER_ROLES['organization_member'].level,
    },
  ],
  platform: [
    {
      key: 'posts',
      label: 'blogPosts',
      labelSingle: 'blogPost',
      route: '/platform/posts',
      icon: MENU_ICONS_BY_TYPES.posts,
      parentType: 'platform',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'faqs',
      label: 'faqs',
      labelSingle: 'faq',
      route: '/platform/faqs',
      icon: MENU_ICONS_BY_TYPES.faqs,
      parentType: 'platform',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'campaigns',
      label: 'campaigns',
      labelSingle: 'campaign',
      route: '/platform/campaigns',
      icon: MENU_ICONS_BY_TYPES.campaigns,
      parentType: 'platform',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'reports',
      label: 'reports',
      labelSingle: 'report',
      route: '/platform/reports',
      icon: MENU_ICONS_BY_TYPES.reports,
      parentType: 'platform',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'pages',
      label: 'pages',
      labelSingle: 'page',
      route: '/platform/pages',
      icon: MENU_ICONS_BY_TYPES.pages,
      parentType: 'platform',
      accessLevel: USER_ROLES['organization_owner'].level,
    },
  ],
  admin: [
    {
      key: 'users',
      icon: MENU_ICONS_BY_TYPES.users,
      route: '/admin/users',
      label: 'users',
      labelSingle: 'user',
      parentType: 'admin',
      accessLevel: USER_ROLES['organization_owner'].level,
    },
    {
      key: 'organizations',
      label: 'organizations',
      labelSingle: 'organization',
      route: '/admin/organizations',
      icon: MENU_ICONS_BY_TYPES.organizations,
      parentType: 'admin',
      accessLevel: USER_ROLES['administrator'].level,
    },
    {
      key: 'organization-profile',
      label: 'myOrganization',
      labelSingle: 'myOrganization',
      route: '/admin/organization-profile',
      icon: MENU_ICONS_BY_TYPES['my-organization'],
      parentType: 'admin',
      accessLevel: ['organization_owner'],
    },
  ],
  inventory: [
    {
      key: 'warehouses',
      label: 'warehouses',
      labelSingle: 'warehouse',
      route: '/inventory/warehouses',
      icon: MENU_ICONS_BY_TYPES.warehouses,
      parentType: 'inventory',
      accessLevel: USER_ROLES['organization_member'].level,
    },
    {
      key: 'inventories',
      label: 'inventories',
      labelSingle: 'inventory',
      route: '/inventory/inventories',
      icon: MENU_ICONS_BY_TYPES.inventories,
      parentType: 'inventory',
      accessLevel: USER_ROLES['organization_member'].level,
    },
  ],
  system: [
    {
      key: 'brands',
      label: 'brands',
      labelSingle: 'brand',
      route: '/system/brands',
      icon: MENU_ICONS_BY_TYPES.brands,
      parentType: 'system',
      accessLevel: USER_ROLES['administrator'].level,
    },
    {
      key: 'items',
      label: 'items',
      labelSingle: 'item',
      route: '/system/items',
      icon: MENU_ICONS_BY_TYPES.items,
      parentType: 'system',
      accessLevel: USER_ROLES['administrator'].level,
    },
    {
      key: 'categories',
      icon: MENU_ICONS_BY_TYPES.categories,
      route: '/system/categories',
      label: 'categories',
      labelSingle: 'category',
      parentType: 'system',
      accessLevel: USER_ROLES['administrator'].level,
    },
    {
      key: 'attributes',
      icon: MENU_ICONS_BY_TYPES.attributes,
      route: '/system/attributes',
      label: 'attributes',
      labelSingle: 'attribute',
      parentType: 'system',
      accessLevel: USER_ROLES['administrator'].level,
    },
    {
      key: 'units',
      icon: MENU_ICONS_BY_TYPES.units,
      route: '/system/units',
      label: 'units',
      labelSingle: 'unit',
      parentType: 'system',
      accessLevel: USER_ROLES['administrator'].level,
    },
  ],
}

export const INVENTORY: IEntities = 
  {
    key: 'inventory',
    label: 'inventory',
    labelSingle: 'inventory',
    route: '/inventory/warehouses',
    iconName: 'squares',
    items: SUBMENU.inventory,
    accessLevel: USER_ROLES['administrator'].level,
  }

export const DOCUMENTS: IEntities = 
  {
    key: 'documents',
    label: 'documents',
    labelSingle: 'document',
    route: '/documents/inquiries',
    iconName: 'file',
    items: SUBMENU.documents,
    accessLevel: USER_ROLES['organization_member'].level,
  }

export const ENTITIES: IEntities[] = [
  {
    key: 'dashboard',
    route: '/',
    hideFromNavMenu: true,
    accessLevel: USER_ROLES['organization_member'].level,
  },
  DOCUMENTS,
  INVENTORY,
  {
    key: 'platform',
    label: 'platform',
    labelSingle: 'platform',
    route: '/platform/faqs',
    iconName: 'platforms',
    items: SUBMENU.platform,
    accessLevel: USER_ROLES['organization_member'].level,
  },
  {
    key: 'system',
    label: 'system',
    labelSingle: 'system',
    route: '/system/brands',
    iconName: 'settings',
    items: SUBMENU.system,
    accessLevel: USER_ROLES['administrator'].level,
  },
  {
    key: 'admin',
    label: 'admin',
    labelSingle: 'admin',
    route: '/admin/users',
    iconName: 'user',
    items: SUBMENU.admin,
    accessLevel: USER_ROLES['organization_owner'].level,
    hideFromNavMenu: false
  },
]
