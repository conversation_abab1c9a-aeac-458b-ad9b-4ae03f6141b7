import { WAREHOUSE } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'
import { STOCK_LEVELS } from '../../../data'
import { ICurrentUser } from '../../../redux-saga/reducers/user'

export const inventoriesRightPanelConfig = (mode: 'create' | 'edit'): IFormConfig => {

  const warehouse = {
    ...WAREHOUSE,
    required: true,
    getDisabled: (formValues?: Record<string, unknown>) => !formValues?.organization_id,
    getOptionsFromFormValues: (formValues: Record<string, unknown>, optionsData?: Record<string, unknown>) => {
      return (
        (optionsData?.warehouses &&
        Object.values(optionsData.warehouses).filter(
          (warehouse) =>
          warehouse.organization_id === (formValues.organization_id?.
          // @ts-ignore
          id || formValues.organization_id))
        ) as Record<string, unknown>
      )
    },
  }
  
  return {
    validationRules: {
      warehouse_id: ['required'],
      organization_id: ['required'],
      type: ['required'],
    },
    fields: {
      ...(mode === 'create' && {
        noTitle: [{
          key: 'state',
          label: 'state',
          optionsKeys: ['states'],
          component: 'dropdown',
        }],
        general: [
            {
              key: 'organization_id',
              label: 'organization',
              noTranslation: true,
              labelKey: 'name',
              valueKey: 'id',
              optionsKeys: ['organizations'],
              component: 'dropdown',
              isClearable: true,
              required: true,
            },
            { ...warehouse },
            {
              key: 'type',
              label: 'type',
              options: STOCK_LEVELS,
              component: 'dropdown',
              required: true,
            }
          ],
        }),
        ...(mode === 'edit' && {
          noTitle: [{
            key: 'state',
            label: 'state',
            optionsKeys: ['states'],
            component: 'dropdown',
          }],
          general: [
            {
              ...WAREHOUSE,
              required: true
            },
            {
              key: 'organization_id',
              label: 'organization',
              noTranslation: true,
              labelKey: 'name',
              valueKey: 'id',
              optionsKeys: ['organizations'],
              component: 'dropdown',
              isClearable: true,
              required: true,
              getDisabled: (_formValues: Record<string, unknown>, optionsData: Record<string, unknown>) => {
                const fieldsEditableRoles = ['administrator', 'system']
                const role = (optionsData.user as ICurrentUser)?.role as string
                return !fieldsEditableRoles.includes(role)
              },
            },
          ],
          inventory_items: [],
          activity: [],
          notes: [],
      }),
    }
  }
}
