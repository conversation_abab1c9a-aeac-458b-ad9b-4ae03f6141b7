import dayjs from 'dayjs'
const utc = require('dayjs/plugin/utc')
const dayOfYear = require('dayjs/plugin/dayOfYear')

dayjs.extend(dayOfYear)
dayjs.extend(utc)

export const rangeOption = (t: (key: string) => string) => [
  { id: 'today', period: t('today') },
  { id: 'yesterday', period: t('yesterday') },
  { id: 'thisWeek', period: t('thisWeek') },
  { id: 'lastWeek', period: t('lastWeek') },
  { id: 'thisMonth', period: t('thisMonth') },
  { id: 'lastMonth', period: t('lastMonth') },
  { id: 'last30days', period: t('last30days') },
  { id: 'last90days', period: t('last90days') },
  { id: 'thisYear', period: t('thisYear') },
  { id: 'lastYear', period: t('lastYear') },
]

const getStartDateTime = (date: string) =>
  // @ts-ignore
  dayjs(date).set('hour', 0).set('minute', 0).set('second', 0).millisecond(0).$d
const getEndDateTime = (date: string) =>
   // @ts-ignore
  dayjs(date).set('hour', 23).set('minute', 59).set('second', 59).millisecond(0).$d

export const getDays = (day: number, range: string) => {
  const rangeDay = dayjs().add(-day, 'day')
  if (range) {
     // @ts-ignore
    return [getStartDateTime(rangeDay), getEndDateTime(dayjs())]
  } else {
     // @ts-ignore
    return [getStartDateTime(rangeDay), getEndDateTime(rangeDay)]
  }
}

export const getWeeks = (range: string) => {
  if (range === 'thisWeek') {
     // @ts-ignore
    return [getStartDateTime(dayjs().day(1)), getEndDateTime(dayjs().day(7))]
  }
  if (range === 'lastWeek') {
     // @ts-ignore
    return [getStartDateTime(dayjs().day(-6)), getEndDateTime(dayjs().day(0))]
  }
}

export const getMonths = (range: string) => {
  let firstDay
  let lastDay
  let currMonth = dayjs().month()

  if (range === 'thisMonth') {
    lastDay = dayjs().daysInMonth()
     // @ts-ignore
    return [getStartDateTime(dayjs().date(1)), getEndDateTime(dayjs().date(lastDay))]
  }
  if (range === 'lastMonth') {
    firstDay = dayjs()
      .month(currMonth - 1)
      .date(1)
    lastDay = dayjs()
      .month(currMonth - 1)
      .daysInMonth()
    return [
      // @ts-ignore
      getStartDateTime(firstDay),
      getEndDateTime(
         // @ts-ignore
        dayjs()
          .month(currMonth - 1)
          .date(lastDay)
      ),
    ]
  }
}

export const getYears = (range: string) => {
  let firstDay
  let lastDay
  let currYear = dayjs().year()
  if (range === 'thisYear') {
    lastDay = dayjs().month(11).daysInMonth()
     // @ts-ignore
    return [getStartDateTime(dayjs().dayOfYear(1)), getEndDateTime(dayjs().month(11).date(lastDay))]
  }
  if (range === 'lastYear') {
    firstDay = dayjs().year(currYear - 1)
    lastDay = dayjs().month(-1).daysInMonth()
     // @ts-ignore
    return [getStartDateTime(firstDay.dayOfYear(1)), getEndDateTime(dayjs().month(-1).date(lastDay))]
  }
  return [firstDay, lastDay]
}

export const rangeDate = {
  today: getDays(0, ''),
  yesterday: getDays(1, ''),
  thisWeek: getWeeks('thisWeek'),
  lastWeek: getWeeks('lastWeek'),
  thisMonth: getMonths('thisMonth'),
  lastMonth: getMonths('lastMonth'),
  last30days: getDays(30, ''),
  last90days: getDays(90, ''),
  thisYear: getYears('thisYear'),
  lastYear: getYears('lastYear'),
}
