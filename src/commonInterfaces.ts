// TODO: move interfaces from FORM folder

import { CurrencyCodeType, Status } from './commonTypes'
import { IOptionData, StateType } from './components/Form/interfaces'
import { ITranslations } from './locales'
import { IUploadedFile } from './redux-saga/reducers/fileUpload'

export interface ICurrency {
  name: string
  code: CurrencyCodeType
  symbol: string,
  id: number
}

export interface ISystemStatuses {
  [status: string]: IOptionData
}

export interface ICountry {
  id: number
  alpha2code: string
  alpha3code: string
  created_at: string
  name: string
  updated_at: string
  translations: ITranslations
  state: StateType
}

export interface IRegion {
  id: number
  category: string
  country_id: number
  code: string | null
  name: string
  translations: ITranslations
  state: StateType
}

export interface IFAQ {
  id?: number
  state?: StateType
  organization_id?: number
  translations?: ITranslations
  body?: ITranslations
  created_at?: string
  updated_at?: string
  [key: string]: unknown
}

export interface IBrandItem {
  id: number
  brand_name: string
  created_at: string
  item_id: number
  item_name: string
  order_id: number
  price: number
  quantity: number
  state: StateType
  tax: number
  total: number
  updated_at: string
  shouldBeDeleted?: boolean
  order_item_id?: number
  quantity_received?: number
  quantity_shipped?: number
  shipment_id?: number
  inquiry_item_id?: number
  modification_id?: number
}

export interface IDocument {
  id?: number
  number?: string
  recipient_id?: number
  supplier_id?: number
  warehouse_id?: number
  state?: StateType
  status?: Status
  created_at?: string
  updated_at?: string
  comments?: IComment[]
  files?: {
    active?: IUploadedFile[]
    disabled?: IUploadedFile[]
  }
  total_weight?: number
}

export interface IInvoices extends IDocument {
  comment?: IComment
  order_id?: number
  issuer_id?: number
  payer_id?: number
  currency_id?: number
  subtotal?: number
  tax?: number
  total?: number
  invoice_items?: IBrandItem[]
  is_being_processed?: boolean
  notes?: string
}

export interface IOrder extends IDocument {
  amount?: number
  tax?: number
  tax_rate?: string
  total?: number
  total_tax?: number
  totalTaxes?: number
  totalDelivery?: number
  external_number?: string
  inquiry_id?: number
  inquiry_number?: string
  currency?: string
  currency_id?: number
  is_being_processed?: boolean
  items?: IBrandItem[]
  order_items?: IBrandItem[]
  organizations?: number[]
  general?: Record<string, unknown>
  notes?: string
}

export interface IShipments extends IDocument {
  shipment_items?: IBrandItem[]
  comment?: IComment
  organization_id?: number[]
  logist_id?: number
  order_id?: number
  is_being_processed?: boolean
  notes?: string
}

export interface IInquiryItem {
  id?: number
  inquiry_id?: number
  item_id?: number
  quantity?: number
  price?: number
  status?: Status
  category_id?: number | { id: number }
  characteristics_keys?: string[]
  characteristics_values?: string[]
  qty_new?: number
  state?: StateType
}

export interface IInquiry {
  comment?: IComment
  comments?: IComment[]
  created_at?: string
  id?: number
  inquirer_id?: number
  inquiry_items?: IInquiryItem[]
  number?: string
  qty_cancelled?: number
  qty_completed?: number
  qty_in_progress?: number
  qty_items_cancelled?: number
  qty_items_completed?: number
  qty_items_in_progress?: number
  qty_items_new?: number
  qty_new?: number
  recipient_city?: string
  recipient_id?: number
  recipient_name?: string
  recipient_organization_type?: string
  recipient_region?: string
  state?: StateType
  status?: Status
  updated_at?: string
  is_being_processed?: boolean
  open_demands?: number
  closed_demands?: number
  in_progress_demands?: number
  notes?: string
}

export interface IComment {
  id: number
  entity_id: number
  entity_type: string
  content: string
  created_at: string
  organization_name: string
  user_full_name: string
  user_photo: string
  user_email: string
  state: StateType
  user_id: number
}

export interface IOrganization {
  id?: number
  name?: string
  name_en?: string | null
  slug?: string
  description?: Record<string, unknown>
  type?: string | null
  reg_number?: string | null
  year_founded?: number | string | null
  email?: string | null
  website?: string | null
  video_url?: string | null
  address?: string | null
  city?: string | null
  country_id?: number | null
  region_id?: number | null
  logo_url?: string | null
  banner_url?: string | null
  facebook_url?: string | null
  twitter_url?: string | null
  instagram_url?: string | null
  linkedin_url?: string | null
  youtube_url?: string | null
  tiktok_url?: string | null
  paypal_url?: string | null
  patreon_url?: string | null
  buymeacoffee_url?: string | null
  venmo_url?: string | null
  wayforpay_url?: string | null
  is_public?: boolean
  rating?: number
  status?: Status
  state?: StateType
  roles?: string[]
  created_at?: string
  updated_at?: string
  [key: string]: unknown
}

export interface ICategory {
  id?: number
  name?: string
  slug?: string
  state?: StateType
  is_used_in_inquiries?: boolean
  created_at?: string
  updated_at?: string
  old_id?: number
  photo_url?: string
  parent_translations?: string
  translations?: ITranslations
  unit_id?: number
  unit?: string
}
