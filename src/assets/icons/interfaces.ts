import { DefaultTheme } from 'styled-components'

export interface IIconProps {
	alignItems?: 'start' | 'end' | 'center' | 'stretch'
	borderRadius?: string
	className?: string
	color?: string
	iconName?: string
	opened?: boolean
	id?: string
	opacity?: number
	dataTooltip?: string
	strokeWidth?: number
	disabled?: boolean
	fill?: string
	fillColor?: string
	checkboxColor?: string
	height?: number
	margin?: string
	justifyContent?: 'start' | 'end' | 'center' | 'stretch' | 'space-around' | 'space-between' | 'space-evenly'
	name?: string | { id: string; slug: string }
	onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
	onTouchEnd?: () => void
	onMouseDown?: () => void
	padding?: string
	size?: number
	stroke?: string;
	style?: React.CSSProperties
	title?: string
  theme?: DefaultTheme
	width?: number
	withoutWrapper?: boolean
	wrapperColor?: string
	wrapperHeight?: number | string
	wrapperWidth?: number | string
}
