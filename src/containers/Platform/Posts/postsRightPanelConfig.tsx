import { TOOLBAR_CONFIG } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'

export const postsRightPanelConfig: IFormConfig = {
  withTabs: ['translations', 'body'],
  validationRules: {
    topic_id: ['required'],
    'translations.en': ['required'],
    'body.en': ['required'],
    visibility: ['required'],
    organization_id: ['required'],
  },
  fields: {
    noTitle: [{
      key: 'state',
      label: 'state',
      optionsKeys: ['states'],
      component: 'dropdown',
    }],
    general: [
      {
        key: 'organization_id',
        label: 'organization',
        noTranslation: true,
        labelKey: 'name',
        valueKey: 'id',
        optionsKeys: ['organizations'],
        component: 'dropdown',
        isClearable: true,
        required: true,
        getDisabled: () => true
      },
      {
        key: 'topic_id',
        label: 'topic',
        labelKey: 'translations',
        optionsKeys: ['topics'],
        component: 'dropdown',
        required: true,
      },
      {
        key: 'visibility',
        label: 'visibility',
        labelKey: 'label',
        optionsKeys: ['visibilityTypes'],
        component: 'dropdown',
        required: true,
      },
    ],
    translations: [
      {
        key: 'translations',
        label: 'title',
        placeholder: 'Your item title',
        required: true,
      },
      {
        key: 'body',
        label: 'texts',
        type: 'richText',
        toolbarOptions: TOOLBAR_CONFIG,
        required: true,
      },
    ],
    photos: [],
  },
}
