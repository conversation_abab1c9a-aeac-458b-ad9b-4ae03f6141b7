import { useState } from 'react'
import { createSearchParams, useLocation, useNavigate } from 'react-router'
import queryString, { ParsedQuery } from 'query-string'
import { DEFAULT_TABLE_SORTING } from '../../../data'
import { sortObjectToUrlString } from '../../../utils/table'

export const sortUrlStringToObj = (sortString: string, type: string) => {
  if (!sortString) {
    return DEFAULT_TABLE_SORTING[type] || {}
  }

  const allSortValuesSplit = sortString.split(',')

  return allSortValuesSplit?.reduce((acc, sort) => {
    const sortSplit = sort.split('_')

    return { ...acc, [sortSplit?.[0]]: sortSplit?.[1] }
  }, {})
}

export const useSorting = (type: string) => {
  const navigate = useNavigate()
  const location = useLocation()

  const searchParsed: ParsedQuery<string> = queryString.parse(location.search)

  const [sortState, setSortState] = useState(() => {
    return sortUrlStringToObj(searchParsed?.sort as string, type)
  })

  const navigateToSortUrl = (state: Record<string, 'asc' | 'desc'>) => {
    const allSortsUrlParams = sortObjectToUrlString(state)

    if (allSortsUrlParams) {
      navigate({
        search: createSearchParams({ ...searchParsed, sort: allSortsUrlParams })
          .toString()
          .replace(/%2C/g, ','),
      })
    } else {
      const newSearchObject = { ...searchParsed }
      if (searchParsed.sort) {
        delete newSearchObject.sort
      }

      navigate({
        search: createSearchParams(newSearchObject.toString()).toString().replace(/%2C/g, ','),
      })
    }
  }

  const onColumnSort = ({ column, key, order }: {
    column: { sortable: boolean };
    key: string;
    order: 'asc' | 'desc';
  }) => {
    if (!column?.sortable) {
      return false
    }
    const newSortObject = {}

    setSortState({ ...newSortObject, [key]: order })

    navigateToSortUrl({ ...newSortObject, [key]: order })
  }

  return { sortState, onColumnSort }
}
