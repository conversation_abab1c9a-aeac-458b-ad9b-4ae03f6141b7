import { useDispatch } from 'react-redux'
import { useTheme } from 'styled-components'
import { Icon, Userpic } from '@aidsupply/components'
import { deletePhoto } from '../../../redux-saga/reducers/fileUpload'
import { StyledUserpic } from './styled'

const OrganizationAvatar = ({
  children,
  entityId,
  photoId,
  name,
  src,
  userpicSize,
  onClick
}: {
  children: React.ReactNode
  entityId: number
  photoId: number
  name: string
  src: string
  userpicSize: string
  onClick?: () => void
}) => {
  const dispatch = useDispatch()
  const theme = useTheme()

  return (
    <StyledUserpic onClick={onClick} style={{ cursor: 'pointer' }}> 
      {children}
      <Userpic
        src={src}
        width={userpicSize}
        height={userpicSize}
        theme={theme}
        fullName={name}
        borderColor={src ? 'transparent' : theme.color.general.gray2}
      />
      <Icon
        name="trashBin"
        onClick={(event: React.MouseEvent<HTMLDivElement>) => {
          event.stopPropagation()
          if (photoId) {
            dispatch(
            deletePhoto({
                entityId,
                entityType: 'organizations',
                primaryPhotoIdKey: 'logo_photo_id',
              })
            )
          }}
        }
      />
    </StyledUserpic>
  )
}


export default OrganizationAvatar
