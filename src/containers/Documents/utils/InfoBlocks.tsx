import { useTheme } from 'styled-components'
import { Icon, Typography, Userpic } from '@aidsupply/components'
import AddressTableCell from '../../../components/Table/components/AddressTableCell'
import { ISystem } from '../../../redux-saga/reducers/data'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'

const InfoBlocks = ({ infoBlocks }: { 
  infoBlocks: {
    label: string,
    icon: string | null,
    type: string,
    key?: string,
    id?: number | string
  }[]
}) => {
  const theme = useTheme()
  const system = useMappedState(selectAllSystemCollections) as ISystem

  return infoBlocks.map(({ label, icon, id, type, key }) => (
    <div key={label}>
      <Typography color={theme.color.general.gray3} fontWeight={600}>
        {label}:
      </Typography>
      <div
        style={{ display: 'flex', alignItems: 'center', marginBottom: '15px', marginTop: '8px' }}
      >
        {type === 'userpic' ? (
          <Userpic
            height="25px"
            width="25px"
            fullName={system?.organizations?.[id as number]?.name || ''}
            src={system?.organizations?.[id as number]?.logo_url}
          />
        ) : (
          <Icon name={icon} fill={theme.color.general.gray3} height={20} width={20} />
        )}
        <Typography
          text={system?.organizations?.[id as number]?.name || ''}
          fontWeight={600}
          style={{ marginLeft: '10px' }}
        />
        {key === 'warehouse' && (
          <>
          <Typography
            text={'['}
            fontWeight={600}
            style={{ marginLeft: '5px' }}
          />
          <AddressTableCell rowData={system?.organizations?.[id as number] as unknown as Record<string, unknown>} onlyFlag={true}/>
          <Typography
            text={system?.organizations?.[id as number]?.city || ''}
            fontWeight={600}
            style={{ marginLeft: '5px' }}
          />
          <Typography
            text={']'}
            fontWeight={600}
          />
        </>
        )}
      </div>
    </div>
  ))
}

export default InfoBlocks
