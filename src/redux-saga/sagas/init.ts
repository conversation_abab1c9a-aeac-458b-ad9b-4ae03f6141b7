import { put, takeLatest } from 'redux-saga/effects'
import { NavigateFunction, Location } from 'react-router'
import { TakeableChannel } from 'redux-saga'
import { verifyToken } from '../../api'
import { getCurrentUser, initializeApp, signInError, signInSuccess } from '../reducers/user'

function* doInitializeApp({
  payload,
}: {
  payload: {
    navigate: NavigateFunction
    location: Location
  }
}) {
  const { navigate, location } = payload

  try {
    const token: string = yield verifyToken()
    if (token) {
      yield put(signInSuccess({ token }))
      yield put(getCurrentUser({ navigate, location }))
    } else {
      navigate('/signin')
      yield put(signInError())
    }
  } catch (error) {
    navigate('/signin')
    console.error(error)
    yield put(signInError())
  }
}

export default function* initSaga() {
  yield takeLatest(initializeApp.type as unknown as TakeableChannel<unknown>, doInitializeApp)
}
