import { useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { object } from 'dot-object'
import { getAvailableTranslation, isObject, isObjectEmpty } from '@aidsupply/components'
import { useMappedState } from '../../../hooks'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { ITranslations } from '../../../locales'
import { StateType } from '../../../components/Form/interfaces'
import { unitsRightPanelConfig } from './unitsRightPanelConfig'

interface IUnitsRightPanel {
  id?: number
  translations?: ITranslations
  state?: StateType
  code?: string
  coefficient?: number
  magnitude?: { id: string, label: ITranslations }
  [key: string]: unknown
}

const UnitsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const unitInitial = useMappedState(selectSidebarInitialData)

  const isReadOnly = false

  const onFormSubmit = (formValuesChanged: IUnitsRightPanel) => {
    const requestBody: IUnitsRightPanel = { ...object(formValuesChanged) }
    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key] as Record<string, unknown>) && (requestBody[key] as Record<string, unknown>).id) {
        requestBody[key] = (requestBody[key] as Record<string, unknown>).id
      }
    })

    if ((formValuesChanged.translations as ITranslations)?.en || !isObjectEmpty(formValuesChanged.translations)) {
      requestBody.name = getAvailableTranslation(formValuesChanged.translations, 'en', 'en')
    }

    const dataToSend = {
      id: unitInitial?.id,
      requestBody,
      type: 'units',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} onSubmit={onFormSubmit} formData={unitsRightPanelConfig()}/>
}

export default UnitsRightPanel
