import { useTheme } from 'styled-components'
import { useTranslation } from 'react-i18next'
import { Icon, Tag, Typography, Userpic } from '@aidsupply/components'
import { StyledDocumentContainer, StyledDocumentDate } from '../../../components/RightPanel/styled'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { IOrganization } from '../../../commonInterfaces'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'
import { useMappedState } from '../../../hooks'
import { formatDateToUserTimezone } from '../../../utils/dates'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { STATES_ICONS, STATUSES_ICONS } from '../../../components/config/table'
import { Status } from '../../../commonTypes'
import { StateType } from '../../../components/Form/interfaces'
import { IInventories } from './InventoriesRightPanel'
import { StyledBorderedContainerWrapper, StyledContainerWrapper, StyledContentWrapper } from './styled'

const InventoriesTableCard = ({data, initialData, className}: {data: IInventories, initialData: Record<string, unknown>, className?: string}) => {
  const { created_at, organization_id, number } = initialData
  const { id, state, status, warehouse_id } = data
  const theme = useTheme()
  const { t } = useTranslation('table')
  const system = useMappedState(selectAllSystemCollections)
  const organization = system?.organizations?.[organization_id as number] as IOrganization
  
  return (
    <TableCardStyled key={id} direction='column' gap={4} className={className}>
      <StyledContentWrapper>
        <StyledContentWrapper>
          {state === 'posted' ?
            <IconWithTooltip text={t(`statuses:${status}`)}  iconName={STATUSES_ICONS[status as Status]} arrowPosition='left'/>
            : <IconWithTooltip text={t(state as StateType)} iconName={STATES_ICONS[state as StateType]} arrowPosition='left'/>
          }
          <Typography as={ReactTexty} color={theme.color.general.dark} fontWeight={600}>
            {number}
          </Typography>
        </StyledContentWrapper>
        <StyledDocumentDate>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'DD.MM.YYYY')}
          </Typography>
          <Typography as={ReactTexty} fontSize={'16px'} style={{
            color: theme.color.general.gray3
          }}>•</Typography>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'H:mm')}
          </Typography>
        </StyledDocumentDate>
      </StyledContentWrapper>
      <StyledBorderedContainerWrapper style={{ margin: '10px 0 0 0' }}>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="homeIcon" width={20} height={20} />
            <StyledContentWrapper>
              {
                organization ?
                <Typography
                  text={organization?.name}
                  style={{ marginRight: '10px' }}
                /> : ''
              }
              {
                organization ?
                <Userpic
                  height="25px"
                  width="25px"
                  fullName={organization?.name}
                  src={organization?.logo_url}
                /> : ''
              }
            </StyledContentWrapper>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="warehouse" width={20} height={20} />
            {warehouse_id ?
            <Tag
              backgroundColor={theme.color.general.gray1}
              color={theme.color.general.gray3}
              fontSize={12}
              fontWeight={600}
              >
                {warehouse_id}
              </Tag> : ''
            }
          </StyledDocumentContainer>
        </StyledContainerWrapper>
      </StyledBorderedContainerWrapper>
    </TableCardStyled>
  )
}

export default InventoriesTableCard
