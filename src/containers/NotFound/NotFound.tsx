import { useTranslation } from 'react-i18next'
import { <PERSON> } from 'react-router'
import { Button, Icon, Typography, UiLink } from '@aidsupply/components'
import { StyledErrorWrapper, StyledNotFound } from './styled'

const NotFound = () => {
  const { t } = useTranslation('errorPage')

  return (
    <StyledNotFound>
      <StyledErrorWrapper>
        <Icon name="page404v3" width="300px" height="300px" />
        <Typography margin="20px 0" textAlign="center" type="h2">
          {t('noCoordinates')}
        </Typography>
        <UiLink to="/" Link={Link} noStyles>
          <Button>{t('goHome')}</Button>
        </UiLink>
      </StyledErrorWrapper>
    </StyledNotFound>
  )
}

export default NotFound
