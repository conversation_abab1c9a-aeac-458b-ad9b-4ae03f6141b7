import { Link } from 'react-router'
import { DefaultTheme } from 'styled-components'
import { FlexRow, IMAGEKIT_PARAMS_CONFIG, Image, UiLink } from '@aidsupply/components'
import { AIDSUPPLY_MINI_LOGO_DARK_LINK, AIDSUPPLY_MINI_LOGO_LIGHT_LINK, IMAGEKIT_URL } from '../../constants'
import LogoText from '../../assets/icons/LogoText/LogoText'
import { StyledLogoWrapper } from './styled'

type Size = 'small' | 'medium' | 'large';

const sizes: Record<Size, { mini: { width: number; height: number }; text?: { width: number; height: number } }> = {
  small: {
    mini: { width: 36, height: 36 },
    text: { width: 93, height: 25 },
  },
  medium: {
    mini: { width: 36, height: 36 },
    text: { width: 130, height: 25 },
  },
  large: {
    mini: { width: 40, height: 40 },
    text: { width: 93, height: 25 },
  },
};

const Logo = ({ theme, variant, isExtended, isDark, to, className, padding, withText = true }: {
  theme: DefaultTheme
  variant: string
  isExtended?: boolean
  isDark?: boolean
  to?: string
  className?: string
  width?: number
  height?: number
  padding?: string
  withText?: boolean
}) => {
  // const { width: screenWidth } = useContext(ScreenContext) || {}
  // const isMobile = screenWidth && screenWidth < theme.breakpoints.sm
  // const isTablet = screenWidth && screenWidth < theme.breakpoints.lg

  const getMiniLogo = () => {
    return (
      <Image
        alt="Logo"
        disableImageKit={theme.logoSrc.startsWith('https://')}
        src={isDark ? AIDSUPPLY_MINI_LOGO_DARK_LINK : AIDSUPPLY_MINI_LOGO_LIGHT_LINK}
        className="logo"
        imageKitParams={IMAGEKIT_PARAMS_CONFIG.crm.goodzyk?.logo}
        imageKitUrl={IMAGEKIT_URL}
        {...sizes[variant as Size].mini}
      />
    )
  }

  return (
    <StyledLogoWrapper className={className} padding={padding}>
      <UiLink noStyles to={to} Link={Link}>
        {isExtended ? (
          <FlexRow>
            {getMiniLogo()}
            {withText && <LogoText theme={theme} {...sizes[variant as Size].text} />}
          </FlexRow>
        ) : (
          getMiniLogo()
        )}
      </UiLink>
    </StyledLogoWrapper>
  )
}

export default Logo
