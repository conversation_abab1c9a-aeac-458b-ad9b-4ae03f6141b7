import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import ItemsTableCard from './ItemsTableCard'

const Items = () => {
  return (
    <GridLayout
      rightPanelComponent={<Outlet context={{}} />}
    >
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={ItemsTableCard as unknown as React.JSX.Element}
        tableCardHeight={265}
        iconName='warehouses'
      />
    </GridLayout>
  )
}

export default Items
