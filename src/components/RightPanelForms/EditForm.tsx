import merge from 'deepmerge'
import { useDispatch } from 'react-redux'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router'

import { useMappedState } from '../../hooks'
import Form from '../Form'
import {
  selectAllSystemCollections,
  selectDataTypeObject,
  selectFileUploadState,
  selectSidebarInitialData,
  selectSidebarState,
  selectUserDetails,
} from '../../redux-saga/selectors'
import { DEFAULT_VALUES_DATA } from '../../data/defaultValues'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import { ISideBar, sidebarPostComment } from '../../redux-saga/reducers/sideBar'
import { IFormProps } from '../Form/interfaces'
import { ISubmenu } from '../NavMenu/config'
import FormBlockWrapperDefault from './FormBlockWrapper'

const EditForm = <T extends Record<string, unknown>>({
  initialValues,
  initialValuesChanged,
  initialValuesFromApi,
  isReadOnly,
  formConfig,
  FormBlockWrapper,
  onSubmit,
  customType,
  expansionPanelProps,
  formData,
}: IFormProps<T>) => {
  const dispatch = useDispatch()
  const { rightPanelId: editedItemId } = useParams()
  const [comment, setComment] = useState<null | string>(null)
  const [formSubmitted, setFormSubmitted] = useState(false)

  const { inProgress } = useMappedState(selectFileUploadState)
  const sidebar = useMappedState(selectSidebarState) as unknown as ISideBar
  const system = useMappedState(selectAllSystemCollections)
  const dataTypeObject = useMappedState(selectDataTypeObject) as ISubmenu
  const type = customType || dataTypeObject?.key
  const itemInitial = useMappedState(selectSidebarInitialData) as Record<string, unknown>
  const user = useMappedState(selectUserDetails) as ICurrentUser

  const handleFormSubmit = (values: T) => {
    if (onSubmit) {
      onSubmit(values)
    }

    if (typeof values === 'object' && values !== null && 'comment' in values) {
      setComment((values as { comment?: string }).comment as string);
    }
    setFormSubmitted(true)
  }

  useEffect(() => {
    if (formSubmitted && comment && itemInitial.id) {
      const commentData = {
        entity_id: itemInitial.id as number,
        content: comment,
      }
      dispatch(sidebarPostComment(commentData))
      setComment(null)
      setFormSubmitted(false)
    }
  }, [formSubmitted, comment, dispatch])

  if (!itemInitial && !initialValues && !initialValuesFromApi) {
    return null
  }

  const options = {
    ...(system as Record<string, unknown>),
    user,
  }

  const initialValuesDefault =
    initialValues ||
    merge(
      DEFAULT_VALUES_DATA[type] as Record<string, unknown>,
      (initialValuesFromApi as Record<string, unknown>) || (itemInitial as Record<string, unknown>)
    )

  // TODO: uncomment when user role 'recipient' or 'inquiries'
  // const initialValuesChangedDefault = user?.role === 'recipient' &&
  //   type === 'inquiries' &&
  //   'id' in initialValuesFromApi?.client &&
  //   !initialValuesFromApi?.client?.id && { client: { id: user.organization } }

  let initialValuesChangedDefault

  if (
    user?.role === 'recipient' &&
    type === 'inquiries' &&
    typeof initialValuesFromApi?.client === 'object' &&
    initialValuesFromApi.client &&
    !('id' in initialValuesFromApi.client)
  ) {
    initialValuesChangedDefault = { client: { id: user.organization } }
  }

  const formInitialValuesChanged = editedItemId === 'clone' ? itemInitial : undefined

  return (
    <Form
      isDraft={editedItemId === 'clone'}
      isReadOnly={isReadOnly}
      formConfig={formConfig || formData}
      inProgress={sidebar.upsertInProgress || inProgress}
      serverError={sidebar.error}
      optionsData={options}
      validationRules={formConfig?.validationRules || formData?.validationRules}
      id={editedItemId}
      initialValues={editedItemId === 'clone'
        ? { ...(itemInitial || initialValuesDefault), notes: '' }
        : itemInitial || initialValuesDefault}
      initialValuesChanged={formInitialValuesChanged || initialValuesChanged || initialValuesChangedDefault}
      type={type}
      labelType="top"
      labelKey="label"
      // @ts-ignore
      FormWrapper={FormBlockWrapper || FormBlockWrapperDefault}
      withActions
      buttonsAreSticky
      onSubmit={(values) => handleFormSubmit(values as T)}
      expansionPanelProps={expansionPanelProps}
    />
  )
}

export default EditForm
