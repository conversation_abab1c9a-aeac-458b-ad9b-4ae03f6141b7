import { useTranslation } from 'react-i18next'
import { Typography } from '@aidsupply/components'
import pendingBg from '../../assets/svg/pendingBg.svg'
import { useCopyToClipboard } from '../../hooks/useCopyToClipboard.ts'
import { useMappedState } from '../../hooks/index.ts'
import { selectUserStatus } from '../../redux-saga/selectors.ts'
import { StyledStatusInfoPage } from './styled.ts'

const StatusInfoPage = () => {
  const { t } = useTranslation('signIn')
  const copy = useCopyToClipboard()
  const status = useMappedState(selectUserStatus)

  const getPendingContent = () => {
    return(
      <>
        <Typography margin="24px 0 0 0" textAlign="center" type="h1">
          {t('checkAccount')}
        </Typography>
        <Typography margin="24px 0 0 0" textAlign="center" type="h4">
          {t('checkAccountTextFirstPart')}
          &nbsp;
          {t('checkAccountTextSecondPart')}
          &nbsp;
          <span onClick={() => copy('<EMAIL>')} className="linkToAdmin">
            <EMAIL>
          </span>
        </Typography>
      </>
    )
  }

  const getInactiveContent = () => {
    return(
      <>
        <Typography margin="24px 0 0 0" textAlign="center" type="h1">
          {t('inactiveAccount')}
        </Typography><Typography type="h4" margin="10px 0">
          {t('inactiveAccountTextFirstPart')}
          &nbsp;
          <span onClick={() => copy('<EMAIL>')} className="linkToAdmin">
            <EMAIL>
          </span>
          <span>.</span>
          &nbsp;
          {t('inactiveAccountTextSecondPart')}
        </Typography>
      </>
    )
  }

  return (
    <StyledStatusInfoPage>
      <img src={pendingBg} alt="bg" />
      {status === 'pending' && getPendingContent()}
      {status === 'inactive' && getInactiveContent()}
    </StyledStatusInfoPage>
  )
}

export default StatusInfoPage
