import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import PostsTableCard from './PostsTableCard'

const Posts = () => {
  return (
    <GridLayout
      rightPanelComponent={<Outlet context={{}} />}
    >
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={PostsTableCard as unknown as React.JSX.Element}
        tableCardHeight={90}
        iconName='fileSimple'
      />
    </GridLayout>
  )
}

export default Posts
