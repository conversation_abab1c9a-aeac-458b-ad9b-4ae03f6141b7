import dayjs from 'dayjs'
import 'dayjs/locale/uk'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import duration from 'dayjs/plugin/duration'
import { capitalizeFirstLetter } from './common'

dayjs.extend(timezone)
dayjs.extend(utc)
dayjs.extend(duration)

export const formatDateToUserTimezone = (date: string, formatTemplate: string) => {
  return dayjs.utc(date).tz().format(formatTemplate)
}

export const getYears = (firstYear: number) => {
  function getAllYearsInRange(startYear: number, endYear: number) {
    const years = []
    for (let year = startYear; year <= endYear; year++) {
      const value = { id: year }
      years.push(value)
    }
    return years
  }

  const currentYear = dayjs().year()
  const years = getAllYearsInRange(firstYear, currentYear)
  return years
}

export const getMonths = () => {
  function getAllMonths() {
    const months = []
    for (let month = 0; month < 12; month++) {
      const value = {
        id: dayjs().month(month).format('MMMM').toLowerCase(),
        label: {
          en: capitalizeFirstLetter(dayjs().month(month).format('MMMM')),
          uk: capitalizeFirstLetter(dayjs().locale('uk').month(month).format('MMMM')),
        },
      }
      months.push(value)
    }
    return months
  }

  const months = getAllMonths()
  return months
}
