import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import clsx from 'clsx'
import { Button, Typography, Image } from '@aidsupply/components'
import { useTheme } from 'styled-components'
import { deletePhoto } from '../../../redux-saga/reducers/fileUpload'
import { selectUpsertInProgress } from '../../../redux-saga/selectors'
import { useMappedState } from '../../../hooks'
import { StyledBanner } from './styled'

const OrganizationBanner = ({ children, entityId, photoId, src }: {
  children: React.ReactNode
  entityId: number
  photoId: number
  src: string
}) => {
  const dispatch = useDispatch()
  const { t } = useTranslation(['general', 'table'])
  const theme = useTheme()

  const inProgress = useMappedState(selectUpsertInProgress)

  const content = (
    <div className="container">
      <Typography
        type="h3"
        color={theme.color.general.light}
        fontWeight={700}
        text={t('replaceBannerImage')}
      />
      <Typography
        type="body1"
        color={theme.color.general.light}
        fontWeight={500}
        text={t('optimalDimensions')}
      />
      <div className={clsx('containerButton', { noImage: !src })}>
        <Button
          variant="bordered"
          onClick={() => {
            if (photoId) {
                  dispatch(
                    deletePhoto({
                      entityId,
                      entityType: 'organizations',
                      primaryPhotoIdKey: 'banner_photo_id',
                    })
                  )
            }
          }}
          disabled={inProgress || !photoId}
        >
          {t(`table:delete`)}
        </Button>
        <label>
        <Button variant="primary" disabled={inProgress} as="span">
          {t('replaceImage')}
        </Button>
        {children}
        </label>
      </div>
    </div>
  )

  return (
    <>
      {src ? (
        <StyledBanner src={src}>
          <Image src={src} width="100%" maxHeight={'210px'} alt="Banner" />
          {content}
        </StyledBanner>
      ) : (
        <StyledBanner src={src}>
          {content}
        </StyledBanner>
      )}
    </>
  )
}

export default OrganizationBanner
