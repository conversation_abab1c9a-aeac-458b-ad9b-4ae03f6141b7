import styled from 'styled-components'

export const StyledStateWrapper = styled.div`{
  display: flex;
  justify-content: space-between;
  align-items: center;
}`

export const StyledContentWrapper = styled.div`{
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px;
}`

export const StyledContainerWrapper = styled.div`{
  display: flex;
  justify-content: space-between;
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  border-radius: 10px;
  margin: 10px 0 0 0;

  .verticalLine {
    width: 1px;
    height: 36px;
    background-color: ${({ theme }) => theme.color.general.gray2};
  }
}`
