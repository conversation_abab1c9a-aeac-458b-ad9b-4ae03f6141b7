import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import ShipmentsTableCard from './ShipmentsTableCard'

const Shipments = () => {

  return (
    <GridLayout
      isIconRightPanelWide={true}
      rightPanelComponent={
      <>
        <Outlet context={{}} />
      </>
    }
    >
    <TableBlockInfiniteScroll
      TableCardContent={ShipmentsTableCard as unknown as React.JSX.Element}
      tableCardHeight={210}
      iconName='deliveryBox'
      
    />
    </GridLayout>
  )
}

export default Shipments
