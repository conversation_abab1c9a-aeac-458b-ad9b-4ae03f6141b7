import { useEffect } from 'react'
import { Accept, useDropzone } from 'react-dropzone'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Icon, TextPanel, Typography } from '@aidsupply/components'
import { IDraftFile } from '../../redux-saga/reducers/fileUpload'
import { StyledDropButton, StyledDropZone } from './styled'

interface IDropZoneProps {
  fileType: Accept
  className?: string
  currentFiles?: IDraftFile[]
  fileTypeText?: string
  isSimpleButton?: boolean
  maxFiles?: number
  maxSize?: number
  setFiles: (newFiles: IDraftFile[]) => void
  simpleButtonProps?: Record<string, unknown>
  fileGroup?: string
  type?: string | number
  noClick?: boolean
  onFileDrop?: (value: File[] | File) => void
  multiple?: boolean
  setIsDropZoneActive?: (isActive: boolean) => void
  isUploadInProgress?: boolean
  isMultipleUploadDisabled?: boolean
}

const DropZone = (props: IDropZoneProps) => {
  const {
    className,
    currentFiles,
    fileType,
    fileTypeText,
    isSimpleButton,
    maxFiles,
    maxSize,
    setFiles,
    simpleButtonProps,
    fileGroup,
    noClick,
    onFileDrop,
    setIsDropZoneActive,
    isUploadInProgress,
    isMultipleUploadDisabled,
  } = props
  const { t } = useTranslation('forms')
  const theme = useTheme()

  const getNewFiles = (newFiles: IDraftFile[]) => {
    if (!setFiles) {
      return
    }
    setFiles(
      newFiles.map((file) =>
        Object.assign(file, {
          // @ts-ignore
          preview: URL.createObjectURL(file),
        })
      )
    )
  }

  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject, acceptedFiles } =
    useDropzone({
      accept: fileType,
      noClick,
      onDropRejected(fileRejections) {
        if (!acceptedFiles.length && maxFiles && fileRejections.length > maxFiles) {
          const newFiles = fileRejections.slice(0, maxFiles)
          getNewFiles(newFiles as unknown as IDraftFile[])
        }

        const maxNewFiles = maxFiles ? maxFiles - acceptedFiles.length : undefined
        if (acceptedFiles.length && maxNewFiles && fileRejections.length > maxNewFiles) {
          const newFiles = fileRejections.slice(0, maxNewFiles)
          getNewFiles(newFiles as unknown as IDraftFile[])
        }
      },
      onDrop(acceptedFiles) {
        if (!acceptedFiles?.length) {
          return
        }
        const maxNewFiles = maxFiles ? maxFiles - (currentFiles?.length || 0) : undefined
        if (maxNewFiles && acceptedFiles.length > maxNewFiles) {
          acceptedFiles.splice(maxNewFiles)
        }

        const newFiles = currentFiles
          ? acceptedFiles.filter(
            (file) => !currentFiles.some((currFile) => currFile.name === file.name)
          )
          : acceptedFiles;

        if (onFileDrop) {
          onFileDrop(newFiles)
        }

        if (setFiles) {
          setFiles(
            // @ts-ignore
            newFiles.map((file: IDraftFile) =>
              // @ts-ignore
              Object.assign(file, {
                // @ts-ignore
                preview: URL.createObjectURL(file),
              })
            )
          );
        }
      },
      maxFiles,
      maxSize,
    })

  useEffect(() => {
    if (setIsDropZoneActive) {
      setIsDropZoneActive(isDragActive)
    }
  }, [isDragActive])

  if (isSimpleButton) {
    return (
      <StyledDropButton
        {...simpleButtonProps}
        {...getRootProps({ isDragActive, isDragAccept, isDragReject })}
        className={className}
      >
        <input {...getInputProps()} id={fileGroup} />
      </StyledDropButton>
    )
  }

  const getFileTypeText = (fileType: Accept) => {
    if (!fileType) return ''

    const allowedExtensions = Object.values(fileType)
      .flat()
      .map((extension) => extension.slice(1).toUpperCase())

    return allowedExtensions.join(', ')
  }

  if (isUploadInProgress) {
    return (
      <div style={{ margin: '20px 0' }}>
        <TextPanel
          content={t('forms:uploadInProgress')}
          backgroundColor="#DEEBFF"
          contentTypographyProps={{ color: theme.color.primary.main, fontWeight: 600 }}
          iconLeft={<Icon name="hourglass" wrapperWidth={16} wrapperHeight={16} margin="0 4px 0 0" />}
          padding="12px 15px"
          className="uploadInProgressOnEntityReentry"
        />
      </div>
    )
  }

  return isMultipleUploadDisabled ? (
    <Typography type="body2" textAlign="center" color={theme.color.general.dark} text={t('oneFileAllowed')} />
  ) : (
    <StyledDropZone
      {...getRootProps({ isDragActive, isDragAccept, isDragReject })}
      className={className}
    >
      <input {...getInputProps()} id={fileGroup} />
      <Icon name="upload" fill={theme.color.general.dark} wrapperWidth={20} wrapperHeight={20} />
      <Typography type="body1" textAlign="center" color={theme.color.general.gray5}>
        {t('dropOrSelect')}
      </Typography>
      {(fileType || fileTypeText) && (
        <Typography
          type="body2"
          textAlign="center"
          color={theme.color.general.gray3}
          text={fileTypeText || getFileTypeText(fileType)}
        />
      )}
    </StyledDropZone>
  )
}

export default DropZone
