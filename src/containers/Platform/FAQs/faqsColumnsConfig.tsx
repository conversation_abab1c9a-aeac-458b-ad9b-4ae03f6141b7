import { Typography } from '@aidsupply/components'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import ReactTexty from '../../../lib/react-texty/ReactTexty'

export const faqsColumnsConfig = (
  lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    {
      ...getNameByMode({
        t,
        dataKey: 'question',
        option: 'state',
        tableMode: tableMode || 'table',
        lng
      }),
      sortable: false,
      width: 0,
      flexGrow: 2,
    },
    {
      key: 'organization',
      sortable: true,
      dataKey: 'organization_id',
      labelKey: 'organization',
      optionsKeys: ['organizations'],
      noTranslation: true,
      width: 0,
      flexGrow: 1,
      cellRenderer: ({ rowData, container }: any) => {
        const orgs = container?.props?.cellProps?.system?.organizations || {}
        const org = orgs[rowData.organization_id] || Object.values(orgs).find((o: any) => o.id === rowData.organization_id)
        const orgName = org?.name || 'N/A'
        return (
          <Typography as={ReactTexty}>{orgName}</Typography>
        )
      },
      headerRenderer: headerRenderer(t, tableMode),
    },
  ]
}
