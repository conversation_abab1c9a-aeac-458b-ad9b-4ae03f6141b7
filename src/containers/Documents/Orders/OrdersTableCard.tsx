import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Icon, Tag, Typography, Userpic } from '@aidsupply/components'
import { ICurrency, IOrder, IOrganization } from '../../../commonInterfaces'
import { TableCardStyled } from '../../../components/Table/styled'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import ReactTexty from '../../../lib/react-texty'
import { formatDateToUserTimezone } from '../../../utils/dates'
import { getCurrencyObjectById } from '../../../utils/common'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'
import { StyledDocumentContainer, StyledContainerWrapper, StyledDocumentDate } from '../../../components/RightPanel/styled'
import { STATES_ICONS, STATUSES_ICONS } from '../../../components/config/table'
import { StateType } from '../../../components/Form/interfaces'
import { Status } from '../../../commonTypes'
import { StyledContentWrapper, StyledBorderedContainerWrapper, StyledHorizontalLine } from './styled'

const OrdersTableCard = ({data, initialData, className}: {data: IOrder, initialData: Record<string, unknown>, className?: string}) => {
  const { id, status, number, currency_id, warehouse_id, inquiry_number, state} = data || {}
  const { created_at, total, recipient_id } = initialData
  const theme = useTheme()
  const { t } = useTranslation('table')
  const system = useMappedState(selectAllSystemCollections)
  const currency = getCurrencyObjectById(currency_id as number, system?.currencies || {}) as ICurrency

  const recipient = system?.organizations?.[recipient_id as string] as IOrganization
  
  return (
    <TableCardStyled key={id} direction='column' gap={4} className={className}>
      <StyledContentWrapper>
        <StyledContentWrapper>
          {state === 'posted' ?
            <IconWithTooltip text={t(`statuses:${status}`)}  iconName={STATUSES_ICONS[status as Status]} arrowPosition='left'/>
            : <IconWithTooltip text={t(state as StateType)} iconName={STATES_ICONS[state as StateType]} arrowPosition='left'/>
          }
          <Typography as={ReactTexty} color={theme.color.general.dark} fontWeight={600}>
            {number}
          </Typography>
        </StyledContentWrapper>
        <StyledDocumentDate>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'DD.MM.YYYY')}
          </Typography>
          <Typography as={ReactTexty} fontSize={'16px'} style={{
            color: theme.color.general.gray3
          }}>•</Typography>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'H:mm')}
          </Typography>
        </StyledDocumentDate>
      </StyledContentWrapper>
      <StyledHorizontalLine />
      <StyledBorderedContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="stateDrafted" width={20} height={20} />
            <Typography as={ReactTexty} type="button1" fontWeight="400" fontSize="12px">
            {inquiry_number}
            </Typography>
          </StyledDocumentContainer>
          <div className='verticalLine'></div>
          <StyledDocumentContainer>
            {currency?.symbol}
            <Typography as={ReactTexty} type="button1" fontWeight="400">
              {total}
            </Typography>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="boxesOut" width={20} height={20} />
            <StyledContentWrapper>
              {
                recipient ?
                <Typography
                  text={recipient?.name}
                  style={{ marginRight: '10px' }}
                /> : ''
              }
              {
                recipient ?
                <Userpic
                  height="25px"
                  width="25px"
                  fullName={recipient?.name}
                  src={recipient?.logo_url}
                /> : ''
              }
            </StyledContentWrapper>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="warehouse" width={20} height={20} />
            {warehouse_id ?
            <Tag
              backgroundColor={theme.color.general.gray1}
              color={theme.color.general.gray3}
              fontSize={12}
              fontWeight={600}
              >
                {warehouse_id}
              </Tag> : ''
            }
          </StyledDocumentContainer>
        </StyledContainerWrapper>
      </StyledBorderedContainerWrapper>
    </TableCardStyled>
  )
}

export default OrdersTableCard
