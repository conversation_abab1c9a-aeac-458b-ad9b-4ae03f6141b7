import { ReactNode, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useNavigate, useParams } from 'react-router'
import { object, pick } from 'dot-object'
import { FlexRow, Input, isObject, Select } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sidebarItemSet, sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { shipmentsRightPanelConfig } from './shipmentsRightPanelConfig'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections, selectSidebarInitialData, selectUserDetails } from '../../../redux-saga/selectors'
import { IBrandItem, IShipments } from '../../../commonInterfaces'
import { IFieldsProps, IFormConfig, UpdateInput } from '../../../components/Form/interfaces'
import EditableTableFormPart from '../../../components/RightPanelForms/EditableTableFormPart'
import StatusSelect from '../../../components/RightPanelForms/StatusSelect'
import ActionsDropdown from '../../../components/RightPanelForms/ActionsDropdown'
import ActivityTabs from '../../../components/RightPanelForms/ActivityTabs'
import { getEditableTableColumns } from '../../../components/config/columnsEditableTable'
import { EDITABLE_TABLE_NAVBAR_FORM_CONFIG } from '../../../components/config/editableTableNavBarForms'
import { TLanguages } from '../../../locales'
import { FILE_TYPES } from '../../../constants'
import { fileParse, IDraftFile } from '../../../redux-saga/reducers/fileUpload'
import UploadFiles from '../../../components/UploadFiles'
import InfoPanel from '../../../components/InfoPanel'
import InfoBlocks from '../utils/InfoBlocks'
import UploadFileButtons from '../utils/UploadFileButtons'

const FormBlockWrapper = ({
  children,
  blockKey,
  id,
  isReadOnly,
  formValues,
  formFieldsProps,
  fields,
  getPanelHeader,
  optionsData,
  customBlockValues,
  updateSelect,
  updateInput,
  formValuesChanged
}: {
  children: ReactNode
  blockKey: string
  id: number
  isReadOnly: boolean
  formValues: IShipments
  formFieldsProps: Record<string, unknown>
  fields: Record<string, unknown>
  getPanelHeader: (blockKey: string) => string
  optionsData: Record<string, unknown>
  customBlockValues: Record<string, unknown>
  updateSelect: (blockKey: string) => void
  setTextsChosenLng: (lng: TLanguages) => void
  updateInput: UpdateInput
  formValuesChanged: IShipments
}) => {
  const dispatch = useDispatch()
  const {
    t,
    i18n: { language },
  } = useTranslation(['table', 'forms', 'general'])
  const params = useParams()
  const { rightPanelId } = params

  const system = useMappedState(selectAllSystemCollections)
  const user = useMappedState(selectUserDetails)
  const { order_id } = formValues

  const [uploadType, setUploadType] = useState<'file' | 'table'>('file')
  const [isUploadFileActive, setIsUploadFileActive] = useState(false)
  const [replaceOption, setReplaceOption] = useState({ id: 'add', label: t('forms:addToExistingItems') })
  
  const editableTableColumns = getEditableTableColumns('shipments.shipment_items', language as TLanguages, t)
  const editableTableConfig = EDITABLE_TABLE_NAVBAR_FORM_CONFIG['shipments.shipment_items'] as unknown as IFormConfig

  const additionalFormValues = editableTableConfig?.
  optionsFromValuesDependencies?.reduce(
    (acc, curr) => ({ ...acc, [curr]: pick(curr, formValues) }),
    {}
  )

  useEffect(() => {
    setReplaceOption({ id: 'add', label: t('forms:addToExistingItems') })
  }, [formValues.id])

  useEffect(() => {
    if (rightPanelId === 'clone') {
      setUploadType('table')
    } else {
      setUploadType('file')
      setIsUploadFileActive(false)
    }
  }, [rightPanelId])

  const onFileDrop = (newFiles: IDraftFile[]) => {
    let is_replace_existing = false
    if (replaceOption.id === 'replace') {
      is_replace_existing = true
    }
    const payload = {
      entity_type: 'shipmentsParseFile',
      file: newFiles[0],
      id: id as number,
      entityTypeId: 'shipment_id',
      is_replace_existing: is_replace_existing
    }
    
    dispatch(fileParse({ ...payload }))
    dispatch(sidebarItemSet({ ...formValues, is_being_processed: true }))
  }

  const isShowSelect = () => {
    if (uploadType === 'file' &&
      formValues.shipment_items &&
      Array.isArray(formValues.shipment_items) &&
      formValues.shipment_items.length > 0 &&
      formValues.state === 'drafted' &&
      !order_id
  ) {
    return (
      <Select
        value={replaceOption}
        isDisabled={formValues.is_being_processed}
        onChange={(option: { id: string; label: string }) => setReplaceOption(option)}
        options={[
          { id: 'add', label: t('forms:addToExistingItems') },
          { id: 'replace', label: t('forms:replaceExistingItems') },
        ]}
        withBorder
        margin="20px 0"
        />
      )}
  }

  const infoBlocks = [
    {
      label: t('supplier'),
      icon: null,
      id: formValues?.supplier_id,
      type: 'userpic',
    },
    {
      label: t('warehouse'),
      icon: 'warehouseAndLocation',
      id: formValues?.supplier_id,
      type: 'icon',
      key: 'warehouse',
    },
    {
      label: t('recipient'),
      icon: null,
      id: formValues?.recipient_id,
      type: 'userpic',
    },
  ]

  return (
      <>
        {blockKey === 'info' && <InfoBlocks infoBlocks={infoBlocks} />}

        {blockKey === 'statusWithActions' && system?.shipmentStatuses && (
          <FlexRow gap="10px" alignItems="center" justifyContent="space-between">
            <StatusSelect
              id={formValues.id as number}
              type="shipments"
              currentStatus={formValues.status as string}
              systemStatuses={system?.shipmentStatuses}
              isDisabled={formValues.state === 'deleted'}
            />
            <ActionsDropdown selectedItem={formValues} type="shipments" />
          </FlexRow>
        )}

        {blockKey === 'activity' && (
          <ActivityTabs
            updateInput={updateInput}
            formValues={formValues as unknown as Record<string, unknown>}
            formValuesChanged={formValuesChanged as unknown as Record<string, unknown>}
            isReadOnly={formValues.state === 'deleted'}
            withTabsFieldsCustom={['comments', 'attachments']}
          />
        )}

        {blockKey === 'notes' && (
          <Input
            multiline
            withBorder
            variant="secondary"
            value={formValues.notes || ''}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => updateInput({ target: { name: 'notes', value: event.target.value } })}
            maxLength="1000"
            readOnly={formValues.state === 'deleted'}
          />
        )}

        {blockKey === 'shipment_items' && !id && rightPanelId !== 'clone' && (
          <InfoPanel text={t('forms:fillGeneralAndSaveBeforeUploadingFile')} />
        )}

        {blockKey === 'shipment_items' && (id || rightPanelId === 'clone') && (
          <>
            {!isReadOnly && rightPanelId !== 'clone' && !order_id && (
              <UploadFileButtons<IShipments>
                formValues={formValues}
                uploadType={uploadType}
                setUploadType={setUploadType}
                setIsUploadFileActive={setIsUploadFileActive}
                isUploadFileActive={isUploadFileActive}
              />
            )}

            {uploadType === 'file' && isUploadFileActive && formValues.state !== 'posted' && (
              <div style={{margin: '20px 0'}}>
                {isShowSelect()}
              </div>
            )}

            {uploadType === 'file' && isUploadFileActive && formValues.state !== 'posted' && (
              <UploadFiles
                style={{ marginTop: 10, marginBottom: 10 }}
                entityType="orders"
                entityId={id}
                fileGroup="order_items"
                fileTypes={{ 'application/vnd.ms-excel': FILE_TYPES.files['application/vnd.ms-excel'] }}
                filesType="files"
                onFnSet={onFileDrop as unknown as (newFiles: File[] | IDraftFile[]) => void}
                isUploadInProgress={formValues.is_being_processed as boolean}
                maxFiles={1}
                isReadOnly
              />
            )}

            <EditableTableFormPart
              additionalFormValues={additionalFormValues as unknown as Record<string, unknown>}
              id={id}
              cellProps={{
                system,
                custom_block_values: customBlockValues,
                lng: language,
                id,
                user,
                is_read_only: isReadOnly,
                additional_form_values: additionalFormValues,
              }}
              compoundTypeKey="shipments.shipment_items"
              isNavbarHidden={false}
              optionsData={optionsData}
              language={language}
              typeData={{ key: 'shipments.shipment_items' }}
              columns={editableTableColumns}
              data={formValues[blockKey] as unknown as Record<string, unknown>[]}
              blockKey={blockKey}
              formFieldsProps={formFieldsProps}
              fields={fields as unknown as IFieldsProps[]}
              getPanelHeader={getPanelHeader}
              updateTableRows={updateSelect.bind(null, blockKey)}
              validationRules={editableTableConfig.validationRules as unknown as Record<string, unknown>}
              t={t}
            />
          </>
        )}
        {children}
      </>
  )
}

export const ShipmentsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const params = useParams()
  const { rightPanelId } = params

  const dataInitial = useMappedState(selectSidebarInitialData) as unknown as IShipments

  const onFormSubmit = (formValuesChanged: IShipments) => {
    const requestBody = { ...object(formValuesChanged) } as IShipments

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key as keyof IShipments]) && (requestBody[key as keyof IShipments] as {id: number}).id) {
        // @ts-ignore
        requestBody[key as keyof IShipments] = requestBody[key as keyof IShipments]?.id
      }
    })

    if (formValuesChanged.shipment_items?.length) {
      requestBody.shipment_items = formValuesChanged.shipment_items.map((item: IBrandItem) => ({
        order_item_id: typeof item.order_item_id === 'object' && item.order_item_id? (item.order_item_id as {id: string}).id : item.order_item_id,
        shipment_id: dataInitial.id || undefined,
        state: item.state,
        item_id: item.item_id,
        quantity: item.quantity,
        id: item.id,
      })) as IBrandItem[]
    }

    const dataToSend = {
      id: dataInitial.id,
      requestBody,
      type: 'shipments',
      parentType: 'documents',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return (
    <EditForm
      initialValues={dataInitial as unknown as Record<string, unknown>}
      formData={dataInitial?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new' ? {
        validationRules: {},
        fields: {
          info: [],
          statusWithActions: [],
          shipment_items: [],
          activity: [],
          notes: [],
        },
      } : shipmentsRightPanelConfig(dataInitial?.id  || rightPanelId === 'clone' ? 'edit' : 'create')}
      onSubmit={onFormSubmit}
      buttonsAreSticky
      withActions
      isReadOnly={dataInitial?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new'}
      FormBlockWrapper={FormBlockWrapper as unknown as ReactNode}
    />
  )
}
