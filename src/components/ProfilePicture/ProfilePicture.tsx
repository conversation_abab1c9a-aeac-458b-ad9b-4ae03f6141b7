import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useTheme } from 'styled-components'
import { isObjectEmpty, Userpic } from '@aidsupply/components'
import { IMAGEKIT_URL } from '../../constants'
import { deleteAvatar, uploadAvatar } from '../../redux-saga/reducers/fileUpload'
import { useMappedState } from '../../hooks'
import { selectFileUploadState, selectUserDetails } from '../../redux-saga/selectors'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import UploadFiles from '../UploadFiles'
import { StyledIcon, StyledProfilePicture } from './styled'


const ProfilePicture = () => {
  const dispatch = useDispatch()
  const theme = useTheme()
  const user = useMappedState(selectUserDetails) as ICurrentUser
  const { inProgress } = useMappedState(selectFileUploadState)

  const [avatar, setAvatar] = useState('')

  useEffect(() => {
    setAvatar(user.profile_pic as string)
  }, [user.profile_pic])

  const fnSet = (newFiles: File[]) => {
    if (!isObjectEmpty(newFiles)) {
      dispatch(
        uploadAvatar({
          file: newFiles[0],
          entityId: user.id,
          entityType: 'users',
          fileType: 'image',
        })
      )
    }
  }

  const onRemoveAvatar = () => {
    if (user.id) {
      dispatch(
        deleteAvatar({
          entityType: 'users',
          entityId: user.id,
        })
      )
    }
  }

  return (
    <StyledProfilePicture>
      <Userpic
        imagekitUrl={IMAGEKIT_URL}
        height="64px"
        width="64px"
        src={avatar}
        theme={theme}
        fullName={user.full_name || '?'}
      />
      {avatar && <StyledIcon
        name="trashBin"
        wrapperHeight={40}
        wrapperWidth={40}
        onClick={onRemoveAvatar}
        disabled={inProgress}
        borderRadius="6px"
      />
      }
      <div style={{ width: '100%' }}>
        <UploadFiles
          entityType="user"
          entityId={user.id}
          fileGroup="noGroup"
          // @ts-ignore
          onFnSet={fnSet}
          maxFiles={1}
          withImagePreview={false}
        />
      </div>
    </StyledProfilePicture>
  )
}

export default ProfilePicture
