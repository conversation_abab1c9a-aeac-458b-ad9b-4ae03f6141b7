import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import AttributesTableCard from './AttributesTableCard'

const Attributes = () => {
  return (
    <GridLayout rightPanelComponent={<Outlet context={{}} />}>
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={AttributesTableCard as unknown as React.JSX.Element}
        tableCardHeight={80}
        iconName='listBulleted'
      />
    </GridLayout>
  )
}

export default Attributes
