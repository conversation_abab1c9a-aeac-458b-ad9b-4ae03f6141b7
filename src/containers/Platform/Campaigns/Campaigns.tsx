import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import CampaignsTableCard from './CampaignsTableCard'

const Campaigns = () => {
  return (
    <GridLayout
      rightPanelComponent={<Outlet context={{}} />}
    >
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={CampaignsTableCard as unknown as React.JSX.Element}
        tableCardHeight={90}
        iconName='moneyBox'
      />
    </GridLayout>
  )
}

export default Campaigns
