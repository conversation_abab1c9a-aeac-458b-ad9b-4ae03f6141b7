import { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router'
import { useMappedState } from '../../hooks'
import { selectUserState } from '../../redux-saga/selectors'
import PopupAlertsComponent from '../../wrappers/PopupAlertsComponent'
import { ICurrentUser, initializeApp } from '../../redux-saga/reducers/user'
import TopBar from '../TopBar'
import { StyledWrapperWithTopBar } from './styled'

const IntroTopBarWrapper = ({ children }: {
  children: React.ReactNode
}) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const location = useLocation()

  const user = useMappedState(selectUserState)
  const isEmailExist = (user.details as ICurrentUser).email

  useEffect(() => {
    if (!user.isAppInitialized && !isEmailExist) {
      dispatch(initializeApp({ navigate, location }))
    }
  }, [user.isAppInitialized, isEmailExist, navigate, location, dispatch])

  return (
    <StyledWrapperWithTopBar>
      <PopupAlertsComponent />
      <TopBar />
      {children}
    </StyledWrapperWithTopBar>
  )
}

export default IntroTopBarWrapper
