import queryString from 'query-string'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { createSearchParams, useLocation, useNavigate } from 'react-router'
import clsx from 'clsx'
import { Button, Pagination, Tag } from '@aidsupply/components'
import { useMappedState } from '../../hooks'
import { selectDataTypeObject, selectFiltersState, selectIsLeftPanelExtended, selectTableDataCount } from '../../redux-saga/selectors'
import { ISubmenu } from '../NavMenu/config'
import { CRM_DATA_TYPES_WITH_NO_FACETS } from '../../apiConstants'
import { FRONTEND_TABLE_ACTIONS } from '../config/table'
import TableSearch from './TableSearch'
import { StyledNavBar } from './styled'
import { useDispatch } from 'react-redux'
import { toggleLeftPanelExtended } from '../../redux-saga/reducers/common'
import { TableModeType } from '../../redux-saga/reducers/data'

interface INavBar {
  maxTableRowsCount?: number
  isMainTable?: boolean
  rowsCount?: number
  addBtnDisabled?: boolean
  cellProps?: Record<string, unknown> 
  gridColCount?: number
  isPaginationDisabled?: boolean
  tableMode?: TableModeType
  onRowClick?: (args: { rowKey: string }) => void;
  isDropdownSearch?: boolean
}

const NavBar = ({
  addBtnDisabled,
  cellProps,
  gridColCount,
  isMainTable,
  isPaginationDisabled,
  onRowClick,
  rowsCount,
  tableMode,
  isDropdownSearch,
}: INavBar) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { t } = useTranslation(['table', 'menu', 'productsForm'])
  const theme = useTheme()
  const location = useLocation()
  const { search } = location
  const searchParsed = queryString.parse(search)
  const currentPage = parseInt(searchParsed.page as string) || 1

  const totalItems = useMappedState(selectTableDataCount) as number
  const typeData = useMappedState(selectDataTypeObject) as ISubmenu
  const { active, facets } = useMappedState(selectFiltersState)
  const isLeftPanelExtended = useMappedState(selectIsLeftPanelExtended) as boolean  

  const type = typeData?.key
  const isProductsOrDemands = type === 'products' || type === 'inquiry_items'

  const onPageChange = (page: number) => {
    const updatedSearchParsed = { ...searchParsed };
  
    if (page === 1) {
      if (updatedSearchParsed.page) {
        delete updatedSearchParsed.page;
      }
    } else {
      updatedSearchParsed.page = page.toString();
    }
  
    navigate({
      search: createSearchParams(
        Object.fromEntries(
          Object.entries(updatedSearchParsed).filter(([_, value]) => value != null)
        ) as Record<string, string | string[]>
      ).toString()
    });
  };

  return (
    <StyledNavBar
      className={isDropdownSearch ? 'withDropdownSearch ' + tableMode : tableMode}
    >
      {isMainTable &&
        (isProductsOrDemands || !cellProps?.is_read_only) && (
          <Button
            className={clsx('navBar', isProductsOrDemands ? 'cart' : 'add')}
            disabled={addBtnDisabled}
            style={{ margin: '0' }}
            onClick={() => onRowClick?.({ rowKey: 'new' })}
            size="small"
            variant="primary"
            uppercase={false}
            withIcon={type !== 'brands'}
            text={t('add')}
            iconLeftProps={{
              name: isProductsOrDemands ? 'shoppingCart2' : 'plus2',
              stroke: theme.color.general.light,
              height: 20,
              width: 20,
              strokeWidth: 1.5,
              wrapperWidth: isProductsOrDemands ? 20 : 32,
              wrapperHeight: isProductsOrDemands ? 20 : 32,
            }}
          >
            {isProductsOrDemands && (
              <>
                {t('productsForm:cart')}
                <Tag
                  className="itemsTag"
                  backgroundColor={theme.color.general.light}
                  color={theme.color.general.dark}
                  fontSize={12}
                  fontWeight={600}
                >
                  {totalItems > 0 ? totalItems : '0'}
                </Tag>
              </>
            )}
          </Button>
        )}
      {isMainTable && !CRM_DATA_TYPES_WITH_NO_FACETS.includes(type) && (
        <Button
          onClick={() => dispatch(toggleLeftPanelExtended(!isLeftPanelExtended))}
          variant="bordered"
          iconLeftProps={{ name: 'hamburgerMenu3', stroke: theme.color.general.gray5, height: 20 }}
          size="small"
          className="navBar filters"
        />
      )}

      {isMainTable && FRONTEND_TABLE_ACTIONS[type]?.search !== false && (
        <TableSearch
          isMainTable={isMainTable}
          type={type}
          activeFilters={active}
          facets={facets}
          isDropdownSearch={isDropdownSearch}
          iconRightProps={{ name: 'cross', strokeWidth: 1.5 }}
        />
      )}

      {!isPaginationDisabled && (
        <Pagination
          dashText={t('of')}
          currentPage={currentPage}
          itemsCount={totalItems}
          itemsPerRowCount={gridColCount}
          onPageChange={onPageChange}
          rowsCount={rowsCount}
        />
      )}
    </StyledNavBar>
  )
}

export default NavBar
