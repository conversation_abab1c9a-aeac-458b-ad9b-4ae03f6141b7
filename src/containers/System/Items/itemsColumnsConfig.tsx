import { Tag } from '@aidsupply/components'
import ReactTexty from '../../../lib/react-texty'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { getSelectCellValue } from '../../../components/config/columns'

export const itemsColumnsConfig = (lng: TLanguages, t: (key: string) => string, tableMode?: TableModeType) => {
  return [
      getNameByMode({
        t,
        dataKey: 'name',
        option: 'state',
        tableMode: tableMode || 'table',
        lng
      }),
      {
        key: 'model',
        sortable: true,
        dataKey: 'sku',
        width: 0,
        flexGrow: 0.5,
        headerRenderer: headerRenderer(t, tableMode),
      },
      {
        key: 'brand',
        sortable: true,
        dataKey: 'brand_id',
        noTranslation: true,
        labelKey: 'name',
        width: 0,
        flexGrow: 1,
        headerRenderer: headerRenderer(t, tableMode),
        cellRenderer: getSelectCellValue,
      },
      {
        key: 'category',
        sortable: true,
        dataKey: 'category_id',
        labelKey: 'translations',
        width: 0,
        flexGrow: 1,
        headerRenderer: headerRenderer(t, tableMode),
        cellRenderer: getSelectCellValue,
      },
      {
        key: 'tags',
        dataKey: 'tags',
        headerRenderer: headerRenderer(t, tableMode),
        width: 0,
        flexGrow: 0.8,
        cellRenderer: ({ cellData, column }: { cellData: Record<string, unknown>[], column: { key: string } }) =>
          cellData?.length ? (
            <ReactTexty key={column.key}>
              {cellData.map((tag) => (
                <Tag key={tag} colorKey="gray2" text={tag} style={{ marginRight: 3 }} />
              ))}
            </ReactTexty>
          ) : (
            ''
          ),
      },
  ]
}
