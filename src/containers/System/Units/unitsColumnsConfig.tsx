import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { getSelectCellValue } from '../../../components/config/columns'

export const unitsColumnsConfig = (lng: TLanguages, t: (key: string) => string, tableMode?: TableModeType) => {
  return [
      getNameByMode({
        t,
        dataKey: 'name',
        option: 'state',
        tableMode: tableMode || 'table',
        lng
      }),
      {
        key: 'code',
        title: 'Code',
        dataKey: 'code',
        sortable: true,
        width: 0,
        flexGrow: 1,
        resizable: false,
        headerRenderer: headerRenderer(t, tableMode),
      },
      {
        key: 'magnitude',
        title: 'Magnitude',
        dataKey: 'magnitude',
        sortable: true,
        width: 0,
        flexGrow: 0.8,
        cellRenderer: getSelectCellValue,
        headerRenderer: headerRenderer(t, tableMode),
      },
      {
        key: 'coefficient',
        title: 'Coefficient',
        dataKey: 'coefficient',
        sortable: true,
        width: 0,
        flexGrow: 0.8,
        headerRenderer: headerRenderer(t, tableMode),
      },
  ]
}
