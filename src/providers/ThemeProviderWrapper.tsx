import { ReactNode } from 'react'
import { ThemeProvider } from 'styled-components'
import { buildImagePath } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import theme from '../theme'
import { AIDSUPPLY_LOGO_LINK } from '../constants'

interface PlatformConfig {
  photos?: {
    miniLogos?: {
      active?: string[]
    }
  }
  id?: string
  seo?: {
    title?: Record<string, string>
  }
}

const ThemeProviderWrapper = ({ children }: { children: ReactNode }) => {
  const { i18n, t } = useTranslation('signIn')
  
  let logoSrc = AIDSUPPLY_LOGO_LINK

  const platforms: PlatformConfig[] = []

  const platform = 'aidsupply'

  const platformConfig = platforms && Object.values(platforms)[0]
  const platformLogo = platformConfig?.photos?.miniLogos?.active?.[0]
  if ((platforms && Object.values(platforms).length === 1) || platformLogo) {
    logoSrc = buildImagePath('platforms', platformConfig?.id, 'miniLogos', platformLogo)
  }

  theme.title = platformConfig?.seo?.title?.[i18n?.language] || 'Aidsupply' + ' ' + t('inUkraine')
  theme.logoSrc = logoSrc
  theme.platform = platform
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>
}

export default ThemeProviderWrapper
