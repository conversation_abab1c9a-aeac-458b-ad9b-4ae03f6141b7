import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Icon, Typography, Userpic } from '@aidsupply/components'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'
import { useMappedState } from '../../../hooks'
import { getCurrencyObjectById } from '../../../utils/common'
import { ICurrency, IInvoices, IOrganization } from '../../../commonInterfaces'
import { TableCardStyled } from '../../../components/Table/styled'
import { StyledDocumentContainer, StyledDocumentDate, StyledContainerWrapper } from '../../../components/RightPanel/styled'
import ReactTexty from '../../../lib/react-texty'
import { formatDateToUserTimezone } from '../../../utils/dates'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StateType } from '../../../components/Form/interfaces'
import { STATUSES_ICONS } from '../../../components/config/table'
import { Status } from '../../../commonTypes'
import { StyledBorderedContainerWrapper, StyledContentWrapper } from './styled'

const InvoicesTableCard = ({
  data,
  initialData,
  className
}: {
  data: IInvoices,
  initialData: Record<string, unknown>,
  className?: string
}) => {
  const { id, status, number, currency_id, order_id, payer_id: payerId, issuer_id: issuerId, state } = data || {}
  const { created_at, total, payer_id, issuer_id } = initialData
  const theme = useTheme()
  const { t } = useTranslation('table')
  const system = useMappedState(selectAllSystemCollections)
  const currency = getCurrencyObjectById(currency_id as number, system?.currencies || {}) as ICurrency
  const payer = system?.organizations?.[payer_id as number] as IOrganization
  const issuer = system?.organizations?.[issuer_id as number] as IOrganization

  return (
    <TableCardStyled key={id} direction='column' gap={4} className={className}>
      <StyledContentWrapper>
        <StyledContentWrapper>
          {state === 'posted' ?
            <IconWithTooltip text={t(`statuses:${status}`)}  iconName={STATUSES_ICONS[status as Status]} arrowPosition='left'/>
            : <IconWithTooltip text={t(state as StateType)} iconName={STATES_ICONS[state as StateType]} arrowPosition='left'/>
          }
          <Typography as={ReactTexty} color={theme.color.general.dark} fontWeight={600}>
            {number}
          </Typography>
        </StyledContentWrapper>
        <StyledDocumentDate>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'DD.MM.YYYY')}
          </Typography>
          <Typography as={ReactTexty} fontSize={'16px'} style={{
            color: theme.color.general.gray3
          }}>•</Typography>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'H:mm')}
          </Typography>
        </StyledDocumentDate>
      </StyledContentWrapper>
      <StyledBorderedContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer  >
            <Icon name="deliveryTruck3" width={20} height={20} />
            <Typography as={ReactTexty} type="button1" fontWeight="400" fontSize={'12px'}>
              {order_id}
            </Typography>
          </StyledDocumentContainer>
          <div className='verticalLine'></div>
          <StyledDocumentContainer  >
            {currency?.symbol}
            <Typography as={ReactTexty} type="button1" fontWeight="400">
              {total}
            </Typography>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="boxesOut" width={20} height={20} />
            <StyledContentWrapper>
              {issuerId}
              {
                issuer ?
                  <Userpic
                    height="25px"
                    width="25px"
                    fullName={issuer?.name}
                    src={issuer?.logo_url}
                  /> : ''
                }
            </StyledContentWrapper>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="boxesIn" width={20} height={20} />
            <StyledContentWrapper>
            {payerId}
            {
              payer ?
                <Userpic
                  height="25px"
                  width="25px"
                  fullName={payer?.name}
                  src={payer?.logo_url}
              /> : ''
            }
            </StyledContentWrapper>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
      </StyledBorderedContainerWrapper>
    </TableCardStyled>
  )
}

export default InvoicesTableCard
