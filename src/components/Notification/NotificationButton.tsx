import { useDispatch } from 'react-redux'

import { Icon } from '@aidsupply/components'
import { useMappedState } from '../../hooks'
import { selectNotifications } from '../../redux-saga/selectors'
import { INotification, toggleNotifications } from '../../redux-saga/reducers/common'
import { StyledNotification } from './styled'

const NotificationButton = () => {
  const dispatch = useDispatch()

  const notifications = useMappedState(selectNotifications)

  const newNotifications = () => {
    return notifications?.filter((item: INotification) => item.status === 'new') || []
  }

  const handleNotifications = () => {
    dispatch(toggleNotifications())
  }

  return (
    <StyledNotification onClick={handleNotifications} className="ignore-click-outside">
      <Icon
        name="notificationOff"
        wrapperWidth={32}
        wrapperHeight={32}
        borderRadius="6px"
        className="iconNotificationOff"
      />
      {newNotifications().length > 0 && (
        <Icon name="notificationOn" className="iconNotificationOn" fill="none" />
      )}
    </StyledNotification>
  )
}

export default NotificationButton
