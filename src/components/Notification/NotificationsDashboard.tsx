import { useRef } from 'react'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import clsx from 'clsx'
import { Icon, Typography } from '@aidsupply/components'
import { useMappedState } from '../../hooks'
import { clearNotifications, INotification, toggleNotifications } from '../../redux-saga/reducers/common'
import { selectIsNotificationsOpened, selectNotifications } from '../../redux-saga/selectors'
import useClickOutside from '../../hooks/useClickOutside'
import { capitalizeFirstLetter } from '../../utils/common'
import InfoPanel from '../InfoPanel'
import {
  StyledEmpty,
  StyledEmptyWrapper,
  StyledNotificationsHeader,
  StyledNotificationsPanel,
} from './styled'

const NotificationsDashboard = () => {
  const dispatch = useDispatch()
  const {
    i18n: { language },
    t,
  } = useTranslation(['notifications', 'table'])
  const notificationsPanelRef = useRef(null);

  const notifications = useMappedState(selectNotifications) as INotification[]
  const isNotificationsOpened = useMappedState(selectIsNotificationsOpened) as boolean

  const notificationItems =
    notifications?.filter(
      (item: INotification) =>
        item.notification_type === 'document_updated' || item.details.message === 'Parsing order items'
    ) || []

  const onClearAll = () => {
    dispatch(clearNotifications())
  }

  useClickOutside(notificationsPanelRef, () => dispatch(toggleNotifications(false)));

  return (
    <StyledNotificationsPanel
      ref={notificationsPanelRef}
      className={clsx(
        'notificationsPanel',
        !notifications.length && 'empty',
        isNotificationsOpened && 'notificationsExtended'
      )}
    >
      <StyledNotificationsHeader>
        <Typography text={t('Notifications')} type="h4" />
        {!!notificationItems.length && (
          <Typography text={t('clearNotifications')} type="body1" onClick={onClearAll} className="clearAll" />
        )}
        {isNotificationsOpened && <Icon name="cross" onClick={() => dispatch(toggleNotifications())} />}
      </StyledNotificationsHeader>
      {notificationItems.length ? (
        notificationItems.map((item, i) => {
          const formattedDate = capitalizeFirstLetter(
            dayjs(item.created_at).locale(language).format('MMM D, YYYY, HH:mm')
          )

          return (
            <InfoPanel
              date={formattedDate}
              key={i}
              text={t(
                item.notification_type === 'document_updated' ? 'yourOrderUploaded' : 'parsingOrderItems',
                { number: item.details.number}
              )}
            />
          )
        })
      ) : (
        <StyledEmptyWrapper>
          <Icon name="noNewNotification" />
          <StyledEmpty>{t('noNotifications')}</StyledEmpty>
        </StyledEmptyWrapper>
      )}
    </StyledNotificationsPanel>
  )
}

export default NotificationsDashboard
