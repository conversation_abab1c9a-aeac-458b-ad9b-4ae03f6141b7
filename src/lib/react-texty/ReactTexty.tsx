import React, { JSX } from 'react'
import ReactDom from 'react-dom'
import { <PERSON><PERSON> } from 'react-popper'

// Type for intrinsic HTML elements (div, span, etc.) or React components
type ElementType = keyof JSX.IntrinsicElements | React.ComponentType<any>

interface TextyProps {
  /**
   * Get inner ref of the component
   */
  innerRef?: (ref: HTMLElement | null) => void
  /**
   * Tag name for the component
   */
  tagName?: ElementType
  /**
   * Should be string or inline element
   */
  children?: React.ReactNode
  /**
   * Tooltip for the truncated text if set, or the children will be used
   */
  tooltip?: React.ReactNode
  /**
   * Classname for the tooltip
   */
  tooltipClassName?: string
  /**
   * Custom style of the tooltip
   */
  tooltipStyle?: React.CSSProperties
  /**
   * Max width of the tooltip
   */
  tooltipMaxWidth?: number
  /**
   * Delay milliseconds to show when mouse enter
   */
  showDelay?: number
  /**
   * Delay milliseconds to hide when mouse leave
   */
  hideDelay?: number
  /**
   * Classname for the arrow
   */
  arrowClassName?: string
  /**
   * Whether to show the tooltip arrow
   */
  hideArrow?: boolean
  /**
   * The placement of the tooltip
   */
  placement?: 'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end'
  /**
   * The HTML Element to append the tooltip **In most cases you don't need to set it manually**
   */
  container?: Element
  /**
   * Mouse enter event handler
   */
  onMouseEnter?: (e: React.MouseEvent) => void
  /**
   * Mouse leave event handler
   */
  onMouseLeave?: (e: React.MouseEvent) => void

  [key: string]: any // For rest props
}

interface TextyState {
  isHovered: boolean
}

interface ArrowProps {
  ref: React.Ref<any>
  style: React.CSSProperties
}

interface PopperRenderProps {
  ref: React.Ref<any>
  style: React.CSSProperties
  placement: string
  arrowProps: ArrowProps
}

// Define the type for Popper modifiers
interface PopperModifier {
  name: string
  options?: Record<string, any>
}

// Create properly typed modifiers for Popper v2
const modifiers: PopperModifier[] = [
  {
    name: 'preventOverflow',
    options: {
      boundary: 'viewport',
      padding: 10,
    },
  },
]

/**
 * Text component with tooltip support powered by Popper
 */
class Texty extends React.Component<TextyProps, TextyState> {
  state = {
    isHovered: false,
  }

  componentDidUpdate() {
    if (this.state.isHovered) {
      window.addEventListener('scroll', this.handleScroll, true)
      // react-virtualized-auto-sizer would trigger scroll events after tooltip shown in some case, we have to skip those scroll events
      this.listenTimer = window.setTimeout(() => {
        window.addEventListener('scroll', this.handleScroll, true)
        this.listenTimer = undefined
      }, 50)
    } else {
      this._clearListenTimer()
      window.removeEventListener('scroll', this.handleScroll, true)
    }
  }

  componentWillUnmount() {
    this._clearListenTimer()
    window.removeEventListener('scroll', this.handleScroll, true)
    this._clearShowTimer()
    this._clearHideTimer()
  }

  render() {
    /* eslint-disable no-unused-vars */
    const {
      tagName: Tag = 'div' as ElementType,
      children,
      placement = 'top',
      // omit the following from rest
      innerRef,
      showDelay = 150,
      hideDelay = 150,
      tooltip,
      tooltipClassName,
      tooltipStyle,
      tooltipMaxWidth,
      hideArrow = false,
      container,
      ...rest
    } = this.props
    /* eslint-enable no-unused-vars */

    // TypeScript needs this check to ensure Tag is a valid component type
    const TagComponent = Tag as React.ElementType

    if (!children) {
      return <TagComponent {...rest} ref={this.setTargetRef} data-texty={false} />
    }

    const target = this.targetNode
    const isTruncated = !!target && target.scrollWidth > target.offsetWidth
    const showTooltip = this.state.isHovered && isTruncated
    return (
      <TagComponent
        {...rest}
        ref={this.setTargetRef}
        data-texty={showTooltip}
        onMouseEnter={this.handleMouseEnter}
        onMouseLeave={this.handleMouseLeave}
      >
        {children}
        {showTooltip && (
          <Popper referenceElement={target} placement={placement} modifiers={modifiers}>
            {this.renderTooltip}
          </Popper>
        )}
      </TagComponent>
    )
  }

  renderTooltip = ({ ref, style, placement, arrowProps }: PopperRenderProps) => {
    const {
      children,
      container,
      tooltip,
      tooltipClassName,
      tooltipStyle,
      tooltipMaxWidth,
      arrowClassName,
      hideArrow = false,
    } = this.props

    const content = tooltip || children
    const extraStyle = tooltipMaxWidth ? { ...tooltipStyle, maxWidth: tooltipMaxWidth } : tooltipStyle
    return ReactDom.createPortal(
      <div
        ref={ref}
        data-texty-tooltip={placement}
        className={tooltipClassName}
        style={extraStyle ? { ...style, ...extraStyle } : style}
        onClick={this.handleMouseEvent}
        onDoubleClick={this.handleMouseEvent}
        onContextMenu={this.handleMouseEvent}
        onMouseDown={this.handleMouseEvent}
        onMouseUp={this.handleMouseEvent}
      >
        {content}
        {!hideArrow && (
          <div
            ref={arrowProps.ref}
            data-texty-arrow={placement}
            className={arrowClassName}
            style={arrowProps.style}
          />
        )}
      </div>,
      container ||
        (this.targetNode && this.targetNode.ownerDocument
          ? this.targetNode.ownerDocument.body
          : document.body)
    )
  }

  private targetNode: HTMLElement | null = null
  private listenTimer?: number
  private showTimer?: number
  private hideTimer?: number

  setTargetRef = (ref: HTMLElement | null) => {
    this.props.innerRef && this.props.innerRef(ref)
    this.targetNode = ref
  }

  handleMouseEvent = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  handleScroll = () => {
    this.setState({ isHovered: false })
  }

  _clearListenTimer() {
    if (this.listenTimer) {
      window.clearTimeout(this.listenTimer)
      this.listenTimer = undefined
    }
  }

  _clearShowTimer() {
    if (this.showTimer) {
      window.clearTimeout(this.showTimer)
      this.showTimer = undefined
    }
  }

  _clearHideTimer() {
    if (this.hideTimer) {
      window.clearTimeout(this.hideTimer)
      this.hideTimer = undefined
    }
  }

  handleMouseEnter = (e: React.MouseEvent) => {
    const { showDelay = 150, onMouseEnter } = this.props
    onMouseEnter && onMouseEnter(e)

    this._clearHideTimer()

    if (!showDelay) {
      this.setState({ isHovered: true })
      return
    }

    this.showTimer = window.setTimeout(() => {
      this.setState({ isHovered: true })
      this.showTimer = undefined
    }, showDelay)
  }

  handleMouseLeave = (e: React.MouseEvent) => {
    const { hideDelay = 150, onMouseLeave } = this.props
    onMouseLeave && onMouseLeave(e)

    this._clearShowTimer()

    const { isHovered } = this.state
    if (!isHovered) return

    if (!hideDelay) {
      this.setState({ isHovered: false })
      return
    }

    this.hideTimer = window.setTimeout(() => {
      this.setState({ isHovered: false })
      this.hideTimer = undefined
    }, hideDelay)
  }
}

export default Texty
