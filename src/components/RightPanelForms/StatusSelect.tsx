import { Select, FlexRow } from '@aidsupply/components'
import { useEffect } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import { sideBarUpsert } from '../../redux-saga/reducers/sideBar'
import { ISystemStatuses } from '../../commonInterfaces'
import { TLanguages } from '../../locales'

const StatusSelect = ({ id, type, currentStatus, systemStatuses, isDisabled }: { 
  id: number
  type: string
  currentStatus: string
  systemStatuses: ISystemStatuses
  isDisabled?: boolean 
}) => {
  const dispatch = useDispatch()
  const { i18n } = useTranslation()
  const language = i18n.language

  const [status, setStatus] = useState(currentStatus)

  useEffect(() => {
    setStatus(currentStatus)
  }, [currentStatus])

  const handleStatusChange = (option: { id: string, iconName: string, label: string }) => {
    setStatus(option.id)

    const requestBody = { status: option.id }
    dispatch(sideBarUpsert({ id: id, requestBody, type: type }))
  }

  return (
    <FlexRow gap="10px" style={{ width: 'calc(100% - 100px)' }}>
      <Select
        value={{
          id: status,
          iconName: systemStatuses[status]?.iconName,
          label: typeof systemStatuses[status]?.label === 'string'
            ? systemStatuses[status]?.label
            : systemStatuses[status]?.label?.[language as TLanguages] ||
              systemStatuses[status]?.label?.en ||
              status,
        }}
        onChange={handleStatusChange}
        options={Object.values(systemStatuses).map((status) => ({
          id: status.id,
          iconName: status.iconName,
          label: typeof status.label === 'string'
            ? status.label
            : status.label[language as TLanguages] || status.label.en,
        }))}
        withBorder
        isDisabled={isDisabled}
        width="100%"
      />
    </FlexRow>
  )
}

export default StatusSelect
