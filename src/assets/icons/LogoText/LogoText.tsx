import { IIconProps } from '../interfaces'

const LogoText = ({
  height,
  width,
  theme
}: IIconProps) => (
  <svg width={width || 89} height={height || 19} viewBox="0 0 89 19" fill="none" className="textLogo">
    <path
      d="M0.36 14L4.446 1.04H7.623L11.709 14H9.477L5.769 2.372H6.255L2.592 14H0.36ZM2.637 11.192V9.167H9.441V11.192H2.637ZM13.5162 2.804V0.815H15.6852V2.804H13.5162ZM13.5162 14V4.28H15.6852V14H13.5162ZM22.2781 14.27C21.3841 14.27 20.6041 14.045 19.9381 13.595C19.2721 13.145 18.7561 12.533 18.3901 11.759C18.0241 10.985 17.8411 10.112 17.8411 9.14C17.8411 8.156 18.0241 7.28 18.3901 6.512C18.7621 5.738 19.2871 5.129 19.9651 4.685C20.6431 4.235 21.4411 4.01 22.3591 4.01C23.2831 4.01 24.0571 4.235 24.6811 4.685C25.3111 5.129 25.7881 5.738 26.1121 6.512C26.4361 7.286 26.5981 8.162 26.5981 9.14C26.5981 10.106 26.4361 10.979 26.1121 11.759C25.7881 12.533 25.3051 13.145 24.6631 13.595C24.0211 14.045 23.2261 14.27 22.2781 14.27ZM22.6111 12.326C23.1931 12.326 23.6611 12.194 24.0151 11.93C24.3751 11.66 24.6361 11.285 24.7981 10.805C24.9661 10.325 25.0501 9.77 25.0501 9.14C25.0501 8.504 24.9661 7.949 24.7981 7.475C24.6361 6.995 24.3811 6.623 24.0331 6.359C23.6851 6.089 23.2351 5.954 22.6831 5.954C22.1011 5.954 21.6211 6.098 21.2431 6.386C20.8651 6.668 20.5861 7.052 20.4061 7.538C20.2261 8.018 20.1361 8.552 20.1361 9.14C20.1361 9.734 20.2231 10.274 20.3971 10.76C20.5771 11.24 20.8501 11.621 21.2161 11.903C21.5821 12.185 22.0471 12.326 22.6111 12.326ZM25.0501 14V7.178H24.7801V1.04H26.9671V14H25.0501ZM34.2875 14.27C33.3395 14.27 32.4845 14.105 31.7225 13.775C30.9665 13.445 30.3425 12.974 29.8505 12.362C29.3645 11.744 29.0555 11.012 28.9235 10.166L31.1735 9.824C31.3655 10.592 31.7585 11.186 32.3525 11.606C32.9525 12.026 33.6425 12.236 34.4225 12.236C34.8845 12.236 35.3195 12.164 35.7275 12.02C36.1355 11.876 36.4655 11.666 36.7175 11.39C36.9755 11.114 37.1045 10.775 37.1045 10.373C37.1045 10.193 37.0745 10.028 37.0145 9.878C36.9545 9.722 36.8645 9.584 36.7445 9.464C36.6305 9.344 36.4805 9.236 36.2945 9.14C36.1145 9.038 35.9045 8.951 35.6645 8.879L32.3165 7.889C32.0285 7.805 31.7165 7.694 31.3805 7.556C31.0505 7.412 30.7355 7.217 30.4355 6.971C30.1415 6.719 29.8985 6.401 29.7065 6.017C29.5205 5.627 29.4275 5.147 29.4275 4.577C29.4275 3.743 29.6375 3.044 30.0575 2.48C30.4835 1.91 31.0535 1.484 31.7675 1.202C32.4875 0.919999 33.2855 0.781999 34.1615 0.788C35.0495 0.793999 35.8415 0.946999 36.5375 1.247C37.2335 1.541 37.8155 1.97 38.2835 2.534C38.7515 3.098 39.0815 3.779 39.2735 4.577L36.9425 4.982C36.8465 4.526 36.6605 4.139 36.3845 3.821C36.1145 3.497 35.7815 3.251 35.3855 3.083C34.9955 2.915 34.5785 2.825 34.1345 2.813C33.7025 2.807 33.3005 2.873 32.9285 3.011C32.5625 3.143 32.2655 3.335 32.0375 3.587C31.8155 3.839 31.7045 4.133 31.7045 4.469C31.7045 4.787 31.8005 5.048 31.9925 5.252C32.1845 5.45 32.4215 5.609 32.7035 5.729C32.9915 5.843 33.2825 5.939 33.5765 6.017L35.8985 6.665C36.2165 6.749 36.5735 6.863 36.9695 7.007C37.3655 7.151 37.7465 7.352 38.1125 7.61C38.4785 7.868 38.7785 8.207 39.0125 8.627C39.2525 9.047 39.3725 9.581 39.3725 10.229C39.3725 10.901 39.2315 11.492 38.9495 12.002C38.6735 12.506 38.2985 12.926 37.8245 13.262C37.3505 13.598 36.8075 13.85 36.1955 14.018C35.5895 14.186 34.9535 14.27 34.2875 14.27ZM44.9655 14.279C44.2455 14.279 43.6515 14.159 43.1835 13.919C42.7155 13.679 42.3435 13.373 42.0675 13.001C41.7915 12.629 41.5875 12.233 41.4555 11.813C41.3235 11.393 41.2365 10.997 41.1945 10.625C41.1585 10.247 41.1405 9.941 41.1405 9.707V4.28H43.3365V8.96C43.3365 9.26 43.3575 9.599 43.3995 9.977C43.4415 10.349 43.5375 10.709 43.6875 11.057C43.8435 11.399 44.0715 11.681 44.3715 11.903C44.6775 12.125 45.0885 12.236 45.6045 12.236C45.8805 12.236 46.1535 12.191 46.4235 12.101C46.6935 12.011 46.9365 11.858 47.1525 11.642C47.3745 11.42 47.5515 11.117 47.6835 10.733C47.8155 10.349 47.8815 9.863 47.8815 9.275L49.1685 9.824C49.1685 10.652 49.0065 11.402 48.6825 12.074C48.3645 12.746 47.8935 13.283 47.2695 13.685C46.6455 14.081 45.8775 14.279 44.9655 14.279ZM48.1425 14V10.985H47.8815V4.28H50.0595V14H48.1425ZM57.2591 14.27C56.3111 14.27 55.5161 14.045 54.8741 13.595C54.2321 13.145 53.7491 12.533 53.4251 11.759C53.1011 10.979 52.9391 10.106 52.9391 9.14C52.9391 8.162 53.1011 7.286 53.4251 6.512C53.7491 5.738 54.2231 5.129 54.8471 4.685C55.4771 4.235 56.2541 4.01 57.1781 4.01C58.0961 4.01 58.8911 4.235 59.5631 4.685C60.2411 5.129 60.7661 5.738 61.1381 6.512C61.5101 7.28 61.6961 8.156 61.6961 9.14C61.6961 10.112 61.5131 10.985 61.1471 11.759C60.7811 12.533 60.2651 13.145 59.5991 13.595C58.9331 14.045 58.1531 14.27 57.2591 14.27ZM52.5701 18.32V4.28H54.4871V11.102H54.7571V18.32H52.5701ZM56.9261 12.326C57.4901 12.326 57.9551 12.185 58.3211 11.903C58.6871 11.621 58.9571 11.24 59.1311 10.76C59.3111 10.274 59.4011 9.734 59.4011 9.14C59.4011 8.552 59.3111 8.018 59.1311 7.538C58.9511 7.052 58.6721 6.668 58.2941 6.386C57.9161 6.098 57.4361 5.954 56.8541 5.954C56.3021 5.954 55.8521 6.089 55.5041 6.359C55.1561 6.623 54.8981 6.995 54.7301 7.475C54.5681 7.949 54.4871 8.504 54.4871 9.14C54.4871 9.77 54.5681 10.325 54.7301 10.805C54.8981 11.285 55.1591 11.66 55.5131 11.93C55.8731 12.194 56.3441 12.326 56.9261 12.326ZM68.3509 14.27C67.4029 14.27 66.6079 14.045 65.9659 13.595C65.3239 13.145 64.8409 12.533 64.5169 11.759C64.1929 10.979 64.0309 10.106 64.0309 9.14C64.0309 8.162 64.1929 7.286 64.5169 6.512C64.8409 5.738 65.3149 5.129 65.9389 4.685C66.5689 4.235 67.3459 4.01 68.2699 4.01C69.1879 4.01 69.9829 4.235 70.6549 4.685C71.3329 5.129 71.8579 5.738 72.2299 6.512C72.6019 7.28 72.7879 8.156 72.7879 9.14C72.7879 10.112 72.6049 10.985 72.2389 11.759C71.8729 12.533 71.3569 13.145 70.6909 13.595C70.0249 14.045 69.2449 14.27 68.3509 14.27ZM63.6619 18.32V4.28H65.5789V11.102H65.8489V18.32H63.6619ZM68.0179 12.326C68.5819 12.326 69.0469 12.185 69.4129 11.903C69.7789 11.621 70.0489 11.24 70.2229 10.76C70.4029 10.274 70.4929 9.734 70.4929 9.14C70.4929 8.552 70.4029 8.018 70.2229 7.538C70.0429 7.052 69.7639 6.668 69.3859 6.386C69.0079 6.098 68.5279 5.954 67.9459 5.954C67.3939 5.954 66.9439 6.089 66.5959 6.359C66.2479 6.623 65.9899 6.995 65.8219 7.475C65.6599 7.949 65.5789 8.504 65.5789 9.14C65.5789 9.77 65.6599 10.325 65.8219 10.805C65.9899 11.285 66.2509 11.66 66.6049 11.93C66.9649 12.194 67.4359 12.326 68.0179 12.326ZM74.9517 14V0.77H77.1207V14H74.9517ZM81.0946 18.32L82.9666 13.181L83.0026 14.693L78.7636 4.28H81.0226L84.0286 11.984H83.4526L86.3776 4.28H88.5466L83.1286 18.32H81.0946Z"
      fill={theme?.color.general.dark}
    />
  </svg>
)

export default LogoText
