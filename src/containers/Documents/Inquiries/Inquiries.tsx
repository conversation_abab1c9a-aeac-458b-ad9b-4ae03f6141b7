import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import InquiriesTableCard from './InquiriesTableCard'

const Inquiries = () => {
  return (
    <GridLayout
      isIconRightPanelWide={true}
      rightPanelComponent={
      <>
        <Outlet context={{}} />
      </>
    }
    >
    <TableBlockInfiniteScroll
      TableCardContent={InquiriesTableCard as unknown as React.JSX.Element}
      tableCardHeight={210}
      iconName='fileQuestion'
    />
    </GridLayout>
  )
}

export default Inquiries
