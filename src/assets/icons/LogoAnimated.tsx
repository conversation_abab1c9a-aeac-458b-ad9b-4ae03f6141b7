export const AidSupplyLogo = ({
  width,
  height,
}: {
  width?: number;
  height?: number;
}) => (
  <svg
    width={width || 50}
    height={height || 50}
    viewBox='0 0 50 50'
    fill='none'
    style={{ animation: 'pulse 2s infinite' }}
  >
    <style>
      {`
        @keyframes pulse {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
          100% {
            opacity: 1;
          }
        }
      `}
    </style>

    <g id='symbol'>
      <path
        id='Vector'
        d='M8.34727 43.7295C8.88541 42.5472 24.8554 7.29089 24.8554 7.29089C23.394 4.69194 20.0755 0.127797 15.9498 2.80131C15.8073 3.02499 15.6754 3.25932 15.5646 3.50963L0.607592 36.5291C-0.758854 39.5381 0.243558 43.1064 2.97117 44.9438L3.60428 45.3698L6.66427 47.4415L9.17558 49.1404C9.46575 49.3375 9.77175 49.4866 10.083 49.6144C7.66668 48.3416 7.81441 44.9118 8.35254 43.7295H8.34727Z'
        fill='url(#paint0_linear_4285_1311)'
      />
      <path
        id='Vector_2'
        d='M49.6096 37.8712C49.5094 37.4025 49.3722 36.9392 49.1718 36.4865L34.2253 3.50965C33.2545 1.37404 31.1442 0 28.8122 0H20.9776C18.92 0 17.026 1.0758 15.9497 2.80133C20.0754 0.127817 23.3939 4.69196 24.8553 7.29091L24.8976 7.20038L39.691 39.8524C40.5668 40.3637 50.9022 46.2006 49.6096 37.8712Z'
        fill='url(#paint1_linear_4285_1311)'
      />
      <path
        id='Vector_3'
        d='M49.6098 37.8765C50.8972 46.2006 40.5723 40.3636 39.6912 39.847L39.7018 39.879L36.378 37.1469C36.2725 37.0563 36.1617 36.9818 36.0562 36.9126C35.9296 36.8273 35.7924 36.7581 35.6552 36.6889C35.5655 36.6463 35.4706 36.6037 35.3809 36.5717C35.3545 36.5611 35.3334 36.5504 35.307 36.5451C35.2173 36.5078 35.1276 36.4812 35.0379 36.4492C34.9746 36.4279 34.906 36.4119 34.8375 36.396C34.7583 36.3747 34.6792 36.3534 34.6 36.3374C34.3257 36.2841 34.0461 36.2468 33.7612 36.2362C33.7506 36.2362 33.7401 36.2362 33.7348 36.2362C32.8854 36.1936 32.0202 36.3161 31.2657 36.5078C31.2446 36.5131 31.2235 36.5185 31.2024 36.5238C30.965 36.5877 30.7328 36.6516 30.5218 36.7262C30.5113 36.7262 30.506 36.7315 30.4954 36.7368C30.3002 36.806 30.1156 36.8753 29.952 36.9445C29.9256 36.9552 29.8993 36.9658 29.8729 36.9765C29.7146 37.0457 29.5774 37.1149 29.4561 37.1788C29.4297 37.1948 29.4033 37.2108 29.3769 37.2214C29.2609 37.2854 29.1606 37.3493 29.0973 37.4078C28.9285 37.5516 28.1899 38.1588 27.1347 39.0269C26.8234 39.2825 26.4963 39.5541 26.1323 39.8524C25.763 40.1559 25.3726 40.4755 24.9611 40.811L34.6634 48.757C36.3833 50.1789 38.826 50.2961 40.6831 49.0552L43.1153 47.4096L46.2544 45.2899L46.8189 44.9065C49.135 43.3407 50.1796 40.5394 49.6151 37.8818L49.6098 37.8765Z'
        fill='url(#paint2_linear_4285_1311)'
      />
      <path
        id='Vector_4'
        d='M32.8905 34.2657L27.4564 29.7868C26.7441 29.185 25.8631 28.8867 24.982 28.8867C24.1009 28.8867 23.2251 29.185 22.5076 29.7868L17.0735 34.2657L9.9722 40.124C9.04365 42.1744 8.43693 43.5165 8.34196 43.7348C7.80383 44.9171 7.6561 48.3416 10.083 49.6144C11.7713 50.3174 13.7392 50.0565 15.2006 48.8529C15.2006 48.8529 20.8457 44.1982 24.9503 40.8057C25.3619 40.4702 25.747 40.1506 26.1216 39.8471C26.4856 39.5488 26.8127 39.2772 27.124 39.0216C28.1739 38.1535 28.9125 37.5463 29.0866 37.4026C29.1552 37.3493 29.2554 37.2854 29.3662 37.2161C29.3926 37.2002 29.4137 37.1895 29.4454 37.1735C29.5667 37.1096 29.7039 37.0404 29.8622 36.9712C29.8885 36.9605 29.9149 36.9499 29.9413 36.9392C30.1049 36.87 30.2895 36.8007 30.4847 36.7315C30.4953 36.7315 30.5005 36.7262 30.5111 36.7209C30.7221 36.6516 30.9543 36.5824 31.1917 36.5185C31.2128 36.5132 31.2339 36.5078 31.255 36.5025C32.0094 36.3108 32.8747 36.1883 33.7241 36.2309C33.7346 36.2309 33.7452 36.2309 33.7505 36.2309C34.0354 36.2469 34.315 36.2788 34.5893 36.3321C34.6737 36.3427 34.7476 36.3694 34.8267 36.3907C34.8953 36.4066 34.9586 36.4226 35.0272 36.4439C35.1222 36.4759 35.2119 36.5025 35.2963 36.5398C35.3227 36.5504 35.3438 36.5611 35.3702 36.5664C35.4651 36.5984 35.5548 36.641 35.6445 36.6836C35.7817 36.7528 35.9136 36.822 36.0455 36.9073C36.151 36.9765 36.2618 37.0564 36.3673 37.1416L32.88 34.2604L32.8905 34.2657Z'
        fill='url(#paint3_linear_4285_1311)'
      />
    </g>
    <defs>
      <linearGradient
        id='paint0_linear_4285_1311'
        x1='22.5973'
        y1='5.14995'
        x2='3.75778'
        y2='45.6558'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#3046CD' />
        <stop offset='1' stopColor='#2F7ACE' />
      </linearGradient>
      <linearGradient
        id='paint1_linear_4285_1311'
        x1='25.9474'
        y1='4.92097'
        x2='44.2125'
        y2='41.7935'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#FDCE11' />
        <stop offset='0.82' stopColor='#FB9715' />
        <stop offset='1' stopColor='#F86216' />
      </linearGradient>
      <linearGradient
        id='paint2_linear_4285_1311'
        x1='43.2155'
        y1='47.5533'
        x2='33.9972'
        y2='35.0353'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#FDCE11' />
        <stop offset='0.82' stopColor='#FB9715' />
        <stop offset='1' stopColor='#F86216' />
      </linearGradient>
      <linearGradient
        id='paint3_linear_4285_1311'
        x1='10.5578'
        y1='48.741'
        x2='30.4928'
        y2='32.9203'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#3046CD' />
        <stop offset='1' stopColor='#2F7ACE' />
      </linearGradient>
    </defs>
  </svg>
);
