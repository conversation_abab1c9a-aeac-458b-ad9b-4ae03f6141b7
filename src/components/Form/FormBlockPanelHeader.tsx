import { Icon, Typography } from '@aidsupply/components'

import { useMappedState } from '../../hooks/index.js'
import { selectFormValueChangedByKey } from '../../redux-saga/selectors.js'
import { TFunction } from 'i18next'

const FormBlockPanelHeader = ({
  blockKey,
  i18n,
  onToggleInfo,
  t,
  type,
  withInfo,
}: {
  blockKey: string
  i18n: any
  onToggleInfo: Function
  t: TFunction
  type: string
  withInfo: boolean
}) => {
  if (blockKey === 'noTitle') {
    return null
  }

  const blockFormValuesChanged = useMappedState(selectFormValueChangedByKey(type, blockKey))

  const translationKey = blockKey?.endsWith('_items') ? 'items' : blockKey
  const blockNameTranslation =
    translationKey && (i18n.exists(translationKey, { ns: 'forms' }) ? t(translationKey) : t(`menu:${translationKey}`))

  const itemsCount =
    blockKey?.endsWith('_items') && Array.isArray(blockFormValuesChanged) && blockFormValuesChanged?.length > 0
      ? blockFormValuesChanged.length
      : ' '

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {blockNameTranslation}
        {!!itemsCount && (
          <Typography type="h4" margin="0 0 0 5px">
            ({itemsCount})
          </Typography>
        )}
      </div>
      {withInfo && (
        <Icon
          name="info"
          className="infoIcon"
          wrapperWidth={16}
          wrapperHeight={16}
          borderRadius="50%"
          onClick={onToggleInfo}
        />
      )}
    </>
  )
}

export default FormBlockPanelHeader
