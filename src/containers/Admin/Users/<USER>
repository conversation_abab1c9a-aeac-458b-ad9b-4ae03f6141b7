import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import UsersTableCard from './UsersTableCard'

const Users = () => {
  return(
    <GridLayout
      rightPanelComponent={
        <>
          <Outlet context={{}} />
        </>
      }
    >
      <TableBlockInfiniteScroll
        TableCardContent={UsersTableCard as unknown as React.JSX.Element}
        tableCardHeight={88}
      />
    </GridLayout>
  )
}

export default Users
