import { Drawer } from '@aidsupply/components'
import styled from 'styled-components'
import { MIN_RIGHT_PANEL_WIDTH, TOP_BAR_BORDER_BOTTOM, TOP_BAR_HEIGHT, TOP_BAR_HEIGHT_XL } from '../../constants'

  export const StyledRightPanel = styled(Drawer)<{ isCommentsRightPanel?: boolean,
  isRightPanelExtendedWider?: boolean
}>`
  background-color: #fff;
  border-left: 1px solid ${({ theme }) => theme.color.general.gray1};
  min-width: ${({ minWidth }) => minWidth || MIN_RIGHT_PANEL_WIDTH};
  padding: 0 20px;
  height: calc(100vh - ${TOP_BAR_HEIGHT}px - ${TOP_BAR_BORDER_BOTTOM}px);
  position: relative;

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
    position: absolute;
    right: 0;
    top: 0;
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
    height: calc(100vh - ${TOP_BAR_HEIGHT_XL}px - ${TOP_BAR_BORDER_BOTTOM}px);
  }

  .subTitle {
    margin: 0 0 0 25px;

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xxl - 150}px) {
      margin: ${({ isRightPanelExtendedWider }) => isRightPanelExtendedWider ? '0 0 0 25px' : '0'};
    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
      margin: 0;
    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.lg - 50}px) {
      margin: ${({ isRightPanelExtendedWider }) => isRightPanelExtendedWider ? '0 0 0 25px' : '0'};
    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px) {
      margin: 0 0 0 25px;
    }
  }

  .titleRow {
    border-bottom: 1px solid ${({ theme }) => theme.color.general.gray1};
    padding: 20px 0;
    margin: 0 0 20px 0;
    font-size: ${({ theme }) => theme.font.size.h3.value};
    color: ${({ theme }) => theme.color.general.dark};
    gap: 10px;
    line-height: 28px;
    z-index: 10;

    &:before,
    &:after {
      content: '';
      position: absolute;
      top: 0;
      height: 100%;
      width: 20px;
      background-color: ${({ theme }) => theme.color.general.light};
    }

    &:before {  
      left: -20px;
    }
    
    &:after {
      right: -20px;
    }

    .icon {
      padding: 0;

      &.topCardClose {
        ${({ isCommentsRightPanel }) => !isCommentsRightPanel && `
          position: absolute;
          top: 19px;
          right: 0;
        `}
        ${({ isCommentsRightPanel }) => isCommentsRightPanel && `
          display: none;
          opacity: 0;
        `}
      }
      
      &.bottomCardClose {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;    
        pointer-events: auto;
      }
    }
  }
  
  .editPanelTitleRow {
    width: 100%;
    font-weight: 600;
    display: block;
  }

  .formInput.isHidden,
  .isHidden {
    display: none;
  }

  .confirmationButtons {
    margin: 20px 0;
  }

  .noItemsRightPanelContent {
    margin-top: 25%;
  }

  .addItemRightPanelContent {
    margin-top: 15px;
    position: relative;
    flex: 1;
    overflow-y: scroll;
    height: calc(100vh - 210px);

    &.withAddedItems {
      height: calc(100vh - 290px);
    }

    .itemCard {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 5px 0;
      border-bottom: 1px solid ${({ theme }) => theme.color.general.gray2};
      cursor: pointer;
      width: 100%;

      .titleCard {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 50%;
      }
    }
    
    .addItemButton:hover {
      background-color: ${({ theme }) => theme.color.primary.main};
      border: none;
      .icon {
        path {
          stroke: ${({ theme }) => theme.color.general.light};
        }
      }
    }

      .inputWrapper.quantity {
        border: 1px solid rgba(203, 210, 222, 1);
        box-shadow: 0px 1px 2px 0px rgba(24, 24, 28, 0.04);
        box-shadow: 0px 3px 4px -5px rgba(24, 24, 28, 0.03);
        border-radius: 6px;

        input {
          box-shadow: none;
          width: 30px;
        }
      }
    }
    .iconDelete {
      margin: 0px 10px;
      path {
        stroke: ${({ theme }) => theme.color.status.error};
      }
    }
  }
`

export const StyledFlexRow = styled.div`
  display: flex;
  align-items: baseline;

  .typography {
    line-height: 24px;
  }

  .label {
    margin-right: 10px;
  }
`

export const StyledPicture = styled.div`{
  width: 46px;
  height: 46px;
  background-color: ${({ theme: { color } }) => color.general.gray4};
  border-radius: 50%;
  display: block;
}`

export const StyledTitle = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
  overflow: hidden;
  width: 100%;

  .icon {
    &:hover {
      opacity: 0.7;
    }
  }
`

export const StyledTitleWithTooltip = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
`

export const StyledTitleWrapper = styled.div<{
  withNoPic?: boolean
}>`
  display: flex;
  flex-direction: column;
  width: ${({ withNoPic }) => withNoPic ? `calc(100% - 40px)` : `calc(100% - 95px)`};
  overflow: hidden;
`

export const StyledDateWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  border-radius: 6px;
  margin: 20px 0 0 0;

  .container {
    display: flex;
    width: 100%;
    max-width: 50%;
    justify-content: space-between;
    padding: 10px;

    .noData {
      width: 50%;
      font-weight: 400;
      line-height: 21px;
    }

    &:first-child {
      border-right: 1px solid ${({ theme }) => theme.color.general.gray2};
      max-width: calc(50% - 1px);
    }
  }
`

export const StyledBorderedContainerWrapper = styled.div<{
  margin?: string
}>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  border-radius: 6px;
  flex-direction: column;
  margin: ${({ margin }) => margin || '20px 0 0 0'};
`

export const StyledContainerWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  border-bottom: 1px solid ${({ theme }) => theme.color.general.gray2};

  &:last-child{
    border-bottom: none;
  }

  .container {
    display: flex;
    width: 50%;
    align-items: center;
    padding: 10px;
    gap: 10px;

    &:last-child{
      justify-content: flex-end;
    }
  }
`

export const StyledDocumentContainer = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px;
  align-items: center;
`

export const StyledDocumentDate = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  color: ${({ theme }) => theme.color.general.dark};

  span {
    color: ${({ theme }) => theme.color.general.gray2};
  }
`

export const StyledTitleContainer = styled.div<{
  padding?: string
  isRightPanelExtendedWider?: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: ${({ padding }) => padding || '0 20px 0 0'};

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xxl - 150}px) {
    display: ${({ isRightPanelExtendedWider }) => isRightPanelExtendedWider ? 'flex' : 'block'};
    width: 100%;
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
    display: block;
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.lg - 50}px) {
    display: ${({ isRightPanelExtendedWider }) => isRightPanelExtendedWider ? 'flex' : 'block'};
    width: 100%;
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px) {
    display: flex;
  }
`
