import { useEffect, useState } from 'react'
import { getAvailableTranslation, Select } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { DEFAULT_LANGUAGE, ITranslations, TLanguages } from '../../locales'
import { getSelectOptions, getSelectValue } from '../../utils/common'
import ReadOnlyInfo from '../RightPanel/ReadOnlyInfo'
import { BasicValue, IFormInputProps, UpdateSelect } from './interfaces'

interface IFormSelectProps extends IFormInputProps {
  labelKey?: string
  lng?: string
  initialValue?: BasicValue
  optionsData?: Record<string, unknown>
  optionsFromFormValues?: Record<string, unknown> | Record<string, unknown>[]
  optionsArray?: Record<string, unknown>[]
  label?: string
  updateSelect?: UpdateSelect
}

const FormSelect = (props: IFormSelectProps) => {
  const {
    field,
    formSubmit,
    initialValue,
    isDisabled,
    isHidden,
    isSubmitOnBlur,
    labelKey,
    label,
    lng,
    name,
    optionsData,
    optionsFromFormValues,
    updateSelect,
    value,
    optionsArray,
  } = props

  const { t } = useTranslation()
  const [options, setOptions] = useState<Record<string, unknown>[]>([])

  const fieldLabelKey = field.noTranslation
    ? field.labelKey || labelKey
    : `${field.labelKey || labelKey}.${lng}`

  const getOneOfTranslations = (option: Record<string, ITranslations>) => {
    return getAvailableTranslation(option?.translations, DEFAULT_LANGUAGE, lng as TLanguages)
  }

  useEffect(() => {
      if (optionsData) {
      const filteredOptions = (
        optionsFromFormValues ||
        optionsArray ||
        getSelectOptions(field, optionsData, lng as TLanguages)
      )?.filter((option: Record<string, unknown>) => option.state !== 'deleted')
      setOptions(filteredOptions)
    }
  }, [field, lng, optionsData, optionsArray, optionsFromFormValues])

  const val = getSelectValue(value, options, field) as Record<string, unknown>

  if (field.readOnly) {
    return <ReadOnlyInfo labelKey={fieldLabelKey || 'label'} label={label || 'label'} value={val} />
  }

  const onCreateOption = (newValue: Record<string, unknown>) => {
    if (field.isCreatable && !field.getOptionsFromFormValues) {
      setOptions((prevOptions) => [...prevOptions, newValue])
    }

    const updatedValue = field.isMulti
      ? Array.isArray(val) ? [...val, newValue] : [newValue]
      : newValue
  
    updateSelect?.(name as string, field.onSelectValueChange)(updatedValue)
  
    if (isSubmitOnBlur && formSubmit) {
      formSubmit(undefined, { [name as string]: updatedValue })
    }
  }

  const handleChange = (newValue: Record<string, unknown>) => {
    updateSelect?.(name as string, field.onSelectValueChange)(newValue)

    if (isSubmitOnBlur && formSubmit) {
      formSubmit(undefined, { [name as string]: newValue })
    }
  }

  return (
    <>
      <Select
        {...props}
        menuIsOpen={field.menuIsOpen}
        value={val}
        key={val}
        onChange={handleChange}
        onCreateOption={onCreateOption}
        createLabelText={t('create')}
        isDisabled={isDisabled || (field.disabledWhenNotEmpty && initialValue !== '') || !options}
        autosize={field.autosize}
        isMulti={field.isMulti || false}
        isCreatable={field.isCreatable || false}
        isClearable={typeof field.isClearable === 'undefined' && field.isMulti ? true : field.isClearable}
        noOptionsMessage={t('noOptionsFound')}
        getOptionBeforeTextComponent={field.getOptionBeforeTextComponent}
        customGetOptionLabel={
          field.customGetOptionLabel || (['translations'].includes(field.labelKey as string) && getOneOfTranslations)
        }
        labelKey={fieldLabelKey || 'label'}
        valueKey={field.valueKey || 'id'}
        options={options}
        t={field.customGetOptionLabel && t}
        lng={field.customGetOptionLabel && lng}
        withTranslation={!field.noTranslation}
      />
      {!isHidden && field.addElementToSelect && field.addElementToSelect(t, field.disabled ?? false, updateSelect)}
    </>
  )
}

export default FormSelect
