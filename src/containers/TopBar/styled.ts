import styled from 'styled-components'
import { TOP_BAR_BORDER_BOTTOM } from '../../constants'

export const StyledTopBar = styled.div`
  border-bottom: ${TOP_BAR_BORDER_BOTTOM}px solid ${({ theme }) => theme.color.general.gray2};
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  position: relative;

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.lg}px) {
    padding: 8px;
  }
`

export const StyledSearchField = styled.div<{
  isFullWidthSearch: boolean
}>`
  margin: 0;
  width: 100%;
  max-width: 450px;
  right: none;
  margin: 0 15px;

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.lg}px)  {
    max-width: 300px;
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px)  {
    max-width: ${({ isFullWidthSearch }) => isFullWidthSearch ? 'calc(100% - 160px)' : '40px'};
    position: absolute;
    right: ${({ isFullWidthSearch }) => isFullWidthSearch ? '181px' : '170px'}};
    margin: 0;
  }
`

export const StyledNotificationWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
`

export const StyledUserPic = styled.div`
  img {
    border-radius: 6px;
  }
  &:hover {
    cursor: pointer;
  }
`
