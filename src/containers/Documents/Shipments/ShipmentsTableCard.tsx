import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Icon, Tag, Typography, Userpic } from '@aidsupply/components'
import { IOrganization, IShipments } from '../../../commonInterfaces'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { formatDateToUserTimezone } from '../../../utils/dates'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'
import { StyledDocumentContainer, StyledContainerWrapper, StyledDocumentDate } from '../../../components/RightPanel/styled'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StateType } from '../../../components/Form/interfaces'
import { STATUSES_ICONS } from '../../../components/config/table'
import { Status } from '../../../commonTypes'
import { StyledBorderedContainerWrapper, StyledContentWrapper } from './styled'

const ShipmentsTableCard = ({data, initialData, className}: {
  data: IShipments,
  initialData: Record<string, unknown>,
  className?: string
}) => {
  const { id, status, number, warehouse_id, order_id, state } = data || {}
  const { created_at, recipient_id } = initialData
  const theme = useTheme()
  const { t } = useTranslation(['table', 'statuses'])
  const system = useMappedState(selectAllSystemCollections)

  const recipient = system?.organizations?.[recipient_id as string] as IOrganization
  
  return (
    <TableCardStyled key={id} direction='column' gap={4} className={className}>
      <StyledContentWrapper>
        <StyledContentWrapper>
          {state === 'posted' ?
            <IconWithTooltip text={t(`statuses:${status}`)}  iconName={STATUSES_ICONS[status as Status]} arrowPosition='left'/>
            : <IconWithTooltip text={t(state as StateType)} iconName={STATES_ICONS[state as StateType]} arrowPosition='left'/>
          }
          <Typography as={ReactTexty} color={theme.color.general.dark} fontWeight={600}>
            {number}
          </Typography>
        </StyledContentWrapper>
        <StyledDocumentDate>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'DD.MM.YYYY')}
          </Typography>
          <Typography as={ReactTexty} fontSize={'16px'} style={{
            color: theme.color.general.gray3
          }}>•</Typography>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'H:mm')}
          </Typography>
        </StyledDocumentDate>
      </StyledContentWrapper>
      <StyledBorderedContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="deliveryTruck3" width={20} height={20} />
            <Typography as={ReactTexty} type="button1" fontWeight="400" fontSize={'12px'}>
              {order_id}
            </Typography>
          </StyledDocumentContainer>
          <div className='verticalLine'></div>
          <StyledDocumentContainer>
            <Icon name="deliveryBoxes" width={20} height={20} />
            <Typography as={ReactTexty} type="button1" fontWeight="400">
              {/* TODO: add shipment_items length, rid of hardcoded 0 */}
              {'0'}
            </Typography>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="boxesOut" width={20} height={20} />
            <StyledContentWrapper>
              {
                recipient ?
                <Typography
                  text={recipient?.name}
                  style={{ marginRight: '10px' }}
                /> : ''
              }
              {
                recipient ?
                <Userpic
                  height="25px"
                  width="25px"
                  fullName={recipient?.name}
                  src={recipient?.logo_url}
                /> : ''
              }
            </StyledContentWrapper>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="warehouse" width={20} height={20} />
            {warehouse_id ?
            <Tag
              backgroundColor={theme.color.general.gray1}
              color={theme.color.general.gray3}
              fontSize={12}
              fontWeight={600}
              >
                {warehouse_id}
              </Tag> : ''
            }
          </StyledDocumentContainer>
        </StyledContainerWrapper>
      </StyledBorderedContainerWrapper>
    </TableCardStyled>
  )
}

export default ShipmentsTableCard
