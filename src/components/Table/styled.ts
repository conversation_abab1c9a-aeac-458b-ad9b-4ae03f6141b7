import { UiLink } from '@aidsupply/components'
import styled from 'styled-components'

export const StyledContent = styled.div<{
  topHeight?: number
  maxHeight?: number
  withShowMore?: boolean
  isReadOnly?: boolean
}>`
  height: ${({ topHeight, maxHeight }) => (maxHeight ? `${maxHeight}px` : `calc(100vh - ${topHeight}px)`)};
  position: relative;
  overflow: hidden;
  overflow-y: auto;

  &.notFoundSearch {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .BaseTable__header {
    position: sticky !important;
    top: 0;
    z-index: 1;
  }

  .BaseTable__empty-layer {
    .icon {
      width: 100%;
      height: 70%;
    }
  }

  &.editableTable {
    z-index: 2;
    margin: ${({ withShowMore }) => (withShowMore ? '0 0 10px 0' : '0 0 0 0')};

    input {
      padding: 9px;
      width: 100%;
    }

    .BaseTable__table-main .BaseTable__header-row {
      background-color: ${({ theme }) => theme.color.general.light};
      border-top: none;

      .BaseTable__header-cell:first-child {
        margin-left: 5px;
      }
    }

    .BaseTable__table-main .BaseTable__row-cell:first-child {
      margin-left: 7px;
    }

    .BaseTable__table-main .BaseTable__row-cell:last-child {
      padding-right: 7px;
      border-bottom: 1px solid ${({ theme }) => theme.color.general.gray1};
    }

    .BaseTable__table-main {
      padding-bottom: 45px;
    }

    &.withShowMore {
      .BaseTable__table-main {
        padding-bottom: 0;
      }

      .summary {
        bottom: 0;
      }
    }

    .summary {
      background-color: ${({ theme }) => theme.color.general.light};
      border-bottom: ${({ theme, withShowMore }) => (withShowMore ? `1px solid ${theme.color.general.gray1}` : 'none')};
      left: 3px;
      border-radius: 0;
      width: 100%;
      padding: ${({ isReadOnly }) => (isReadOnly ? '0 20px 0 0' : '0 60px 0 0')};
      bottom: 4px;
    }

    .summaryCell {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .emailForCopy {
    cursor: pointer;
  }

  & .card:not(.isSelected) {
    border-color: ${({ theme }) => theme.color.general.gray1};

    .checkbox {
      border-color: ${({ theme }) => theme.color.general.gray1};
    }

    &:hover {
      border-color: ${({ theme }) => theme.color.general.gray5};
    }
  }

  && .BaseTable__header-row {
    background-color: ${({ theme }) => theme.color.general.gray1};
    border-radius: 0;

    .typography:first-letter {
      text-transform: capitalize;
    }
  }

  && .BaseTable__table.BaseTable__table-main .BaseTable__row {
    border-color: ${({ theme }) => theme.color.general.gray1};
    font-size: 14px;
  }

  && .BaseTable__table.BaseTable__table-main .BaseTable__row.deletedRow {
    background-color: ${({ theme }) => theme.color.status.error}20;
  }

  .listVirtualized {
    overflow: hidden !important;
  }
`

export const StyledNavBar = styled.div`
  display: flex;
  align-items: center;
  gap: 15px 10px;
  padding: 20px 0;

  .download {
    padding-left: 9px;
  }

  .drawer & {
    gap: 5px;
  }

  .multiSelectTable & {
    padding-bottom: 0;
  }

  &.editable {
    justify-content: space-between;
    flex-wrap: wrap;
    padding-bottom: 0;

    .row {
      flex-grow: 1;

      &.fullWidth {
        flex-basis: 100%;
        width: 100%;
      }

      &.lastRow {
        &.characteristics {
          order: 100;
        }

        flex-basis: calc(100% - 40px);
        width: calc(100% - 40px);

        .characteristicsToChoose {
          margin-top: 10px;
          display: flex;
          flex-wrap: wrap;

          & > div {
            max-width: 50%;

            &:first-child {
              margin-right: 5px;
            }

            .selectWrapper {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    &:not(.withFields) {
      padding: 5px 15px;
    }

    .iconSelect {
      flex-grow: 1;
    }
  }

  .navRightIcon {
    margin-left: 15px;
  }

  &.withDropdownSearch {
    flex-wrap: wrap;

    .searchInput {
      order: 10;
    }
  }

  .selectWrapper .react-select__control {
    min-height: 32px;
  }

  & .inputWrapper {
    & > .icon.leftIcon {
      left: 6px;
    }

    & input {
      height: 32px;

      &.hasLeftIcon {
        padding-right: 6px;
        padding-left: 26px;
      }
    }
  }

  button {
    &.navBar {
      min-width: auto;

      &.add {
        padding: 0 14px 0 0;

        .icon {
          margin: 0;
        }
      }

      &.filters {
        padding: 5px;
      }

      &.filters,
      &.cart {
        .itemsTag {
          margin-left: 6px;
          margin-right: 0;
        }
      }

      &.search {
        padding: 0;
      }
    }
  }

  .actions {
    margin: 0;

    div:nth-child(2) {
      padding: 0;
    }

    .menuItem {
      padding: 8px 0;
      margin: 0 16px;

      &.clickable {
        border-bottom: none;
      }

      &:not(.clickable) {
        margin: 0;
        padding: 8px;
      }
    }
  }

  .downloadActions {
    display: flex;
  }

  .opened {
    padding: 0;
    width: 34px;

    .icon {
      padding: 0;
    }
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
    padding: 10px 8px;

    .downloadActions {
      display: none;
    }
  }
`

export const StyledListItem = styled.div`
  display: flex;

  &.delete {
    border-top: 1px solid ${({ theme }) => theme.color.general.gray1};
  }

  .icon {
    margin-right: 10px;
  }

  .actionHeader {
    font-size: 12px;
    line-height: 14px;
  }
`

export const TableCardStyled = styled.div<{
  padding?: string
  direction?: 'row' | 'column'
  gap?: number
}>`
{
  display: flex;
  flex-direction: ${({ direction }) => (direction ? `${direction}` : 'row')};
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  border-radius: 24px;
  padding: ${({ padding }) => (padding ? `${padding}` : '20px')};
  gap: ${({ gap }) => (gap ? `${gap}px` : '10px')};
  position: relative;
  min-height: 80px;
  margin: 5px 0 0 0;

  &.clicked {
    box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 12px;
    border: 1px solid ${({ theme }) => theme.color.general.gray1};
  }

  &: hover {
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    transition: box-shadow 0.5s;
  }
}

  .brandLogo {
    margin: 10px 0 0 0;
  }
`

export const StyledLink = styled(UiLink)`
  position: absolute;
  bottom: 15px;
  left: 0;
  padding-bottom: 13px;
  padding-top: 12px;
  width: 100%;
  justify-content: center;

  &.editableTableLink {
    bottom: 70px;
    z-index: 5;
  }

  a {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  & .showMore {
    margin-top: 3px;
    z-index: 10;
  }

  & .showLess {
    transform: rotate(180deg);
  }
`

export const StyledLoadingLine = styled.div<{
  isLoading: boolean
  tableMode: 'table' | 'cards'
}>`
  display: block;
  width: ${({ tableMode }) => (tableMode === 'table' ? '100%' : 'calc(100% - 50px)')};
  position: ${({ tableMode }) => (tableMode === 'table' ? 'sticky' : 'absolute')};
  top: ${({ tableMode }) => (tableMode === 'table' ? '-2px' : 'none')};
  bottom: ${({ tableMode }) => (tableMode === 'cards' ? '2px' : 'none')};
  left: ${({ tableMode }) => (tableMode === 'table' ? '0' : '25px')};
  z-index: 100;
  text-align: center;
  background-color: ${({ isLoading, theme }) => (isLoading ? theme.color.primary.main : 'transparent')};
  height: 2px;
  overflow: hidden;

  &:before {
    display: ${({ isLoading }) => (isLoading ? 'block' : 'none')};
    content: '';
    position: absolute;
    left: 0;
    height: 2px;
    width: 100%;
    background-color: ${({ theme }) => theme.color.general.gray2};
    -webkit-animation: lineAnim 1000ms linear infinite;
    -moz-animation: lineAnim 1000ms linear infinite;
    animation: lineAnim 1000ms linear infinite;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
  }

  @keyframes lineAnim {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(100%);
    }
  }
`

export interface StyledTableSummaryProps {
  headerHeight: number
}

export const StyledTableSummary = styled.div<StyledTableSummaryProps>`
  position: absolute;
  bottom: ${({ headerHeight }) => `${-headerHeight + 2}px`};
  left: 20px;
  right: 20px;
  height: ${({ headerHeight }) => `${headerHeight}px`};
  padding: 0;
  display: flex;
  align-items: center;
  background-color: ${({ theme }) => theme.color.general.gray2};
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  font-size: 13px;
  font-weight: 600;

  .summaryTitle {
    margin-right: auto;
    padding-left: 7px;
  }

  .summaryLabel {
    margin-right: 5px;
  }

  @media only screen and (max-width: ${({ theme }) => `${theme.breakpoints.sm}px`}) {
    .expansionPanel & {
      .opened & {
        left: 10px;
        right: 10px;
      }
    }
  }
`

export interface StyledSummaryCellProps {
  width?: number
  flexGrow?: number
  padding?: string
}

export const StyledSummaryCell = styled.div<StyledSummaryCellProps>`
  width: ${({ width }) => (typeof width === 'number' ? `${width}px` : 'auto')};
  flex-grow: ${({ flexGrow = 0 }) => flexGrow};
  padding: ${({ padding = '0 7px' }) => padding};
`
