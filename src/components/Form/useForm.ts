import { useState } from 'react'
import dot from 'dot-object'
import cloneDeep from 'lodash.clonedeep'
import merge from 'deepmerge'
import { useDispatch } from 'react-redux'
import { validate, validateOneField, isEqualArraysById } from '@aidsupply/components'
import {
  addToFormValuesChanged,
  removeFromFormValuesChanged,
  setFormValuesChanged,
  setFormValuesInitial,
} from '../../redux-saga/reducers/forms.ts'

// // @ts-ignore
// const overwriteMerge = (target, source, options) => {
//   // combine arrays
//   if (target[0]?.code || target[0]?.phone || target[0]?.phonePart) {
//     const destination = target.slice()
//     // @ts-ignore
//     source.forEach((item, index) => {
//       if (typeof destination[index] === 'undefined') {
//         destination[index] = options.cloneUnlessOtherwiseSpecified(item, options)
//       } else if (options.isMergeableObject(item)) {
//         destination[index] = merge(target[index], item, options)
//       } else if (target.indexOf(item) === -1) {
//         destination.push(item)
//       }
//     })
//     return destination
//   }
//   // overwrite arrays
//   return source
// }

// const mergeOptions = {
//   // @ts-ignore
//   customMerge(key) {
//     if (key === 'photos') {
//       // @ts-ignore
//       return (photosA, photosB) => photosB
//     }
//   },
//   arrayMerge: overwriteMerge,
// }

const useForm = ({
  initialValues = {},
  // @ts-ignore
  initialValuesChanged,
  // @ts-ignore
  optionsData,
  // @ts-ignore
  validationRules,
  // @ts-ignore
  type,
}) => {
  const dispatch = useDispatch()
  const [formErrors, setFormErrors] = useState({})

  const addToValuesChangedObject = (
    path: string,
    value: string | Record<string, unknown> | boolean,
    isInput?: boolean
  ) => {
    dot.keepArray = true
    // @ts-ignore
    dot.override = true

    if (isInput && value === Object(value) && !Array.isArray(value)) {
      //if input value (by updateInput) is object
      dispatch(addToFormValuesChanged({ type, valuesChanged: dot.dot({ [path]: value }) }))
    } else {
      dispatch(addToFormValuesChanged({ type, valuesChanged: { [path]: value } }))
    }
  }

  const removeFromValuesChangedObject = (path: string) => {
    dispatch(removeFromFormValuesChanged({ type, path }))
  }

  const validateField = (key: string, value: string | Record<string, unknown>, formValues: Record<string, unknown>) => {
    if (
      validationRules[key] &&
      // @ts-ignore
      validationRules[key].findIndex((rule) => rule.type === 'requiredIfFieldsEmpty') !== -1
    ) {
      const newValues = { ...formValues, [key]: value }
      const errors = validate(validationRules)(newValues, { values: newValues })
      setFormErrors(errors)
      return
    }

    // @ts-ignore
    const ruleWithSuccess = validationRules[key]?.find((rule) => rule.withSuccess)
    const dataForValidation = ruleWithSuccess?.dataForValidation
    const listKey = dataForValidation?.listKey

    // @ts-ignore
    if (formErrors[key] || ruleWithSuccess) {
      const initialValueObj = { initialValue: dot.pick(key, initialValues), values: formValues }
      const data = ruleWithSuccess
        ? {
            ...initialValueObj,
            ruleWithSuccess,
            [listKey]: optionsData?.[listKey],
          }
        : initialValueObj
      // @ts-ignore
      const fieldError = validateOneField(key, validationRules)(value, data)

      if (!Object.keys(fieldError).length) {
        setFormErrors((prev) => {
          const newErrors = { ...prev }
          // @ts-ignore
          delete newErrors[key]

          return newErrors
        })
      } else {
        setFormErrors((prev) => ({ ...prev, ...fieldError }))
      }
    }
  }

  const setValuesChanged = (valuesChanged: Record<string, unknown>, isInitialValuesUpdateNeeded: boolean = false) => {
    dispatch(setFormValuesChanged({ type, valuesChanged }))
    if (isInitialValuesUpdateNeeded) {
      dispatch(
        setFormValuesInitial({
          type,
          valuesInitial: merge(initialValues, valuesChanged, { arrayMerge: (_target, source) => source }),
        })
      )
    }
  }

  const updateCheckboxValue = (checked: boolean, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name } = e.target
    const initialValue = dot.pick(name, initialValues)

    if (checked === initialValue || (!initialValue && !checked)) {
      removeFromValuesChangedObject(name)
    } else {
      addToValuesChangedObject(name, checked)
    }
  }

  // @ts-ignore
  const updateInputValue = (e, customOnChange) => {
    const { name, value } = e.target

    if (value === dot.pick(name, initialValues)) {
      removeFromValuesChangedObject(name)
    } else {
      addToValuesChangedObject(name, value, true)
    }

    // @ts-ignore
    // setTouchedFields((prev) => (prev.includes(name) ? prev : [...prev, name]))

    if (customOnChange) {
      customOnChange(value, setValuesChanged, cloneDeep(initialValues)) // TODO: do we need cloneDeep here?
    }
  }

  // @ts-ignore
  const updateSelectValue = (name, customOnChange) => (value, equalsToInitialValue) => {
    const initialValue = dot.pick(name, initialValues)

    if (typeof equalsToInitialValue === 'undefined') {
      if (Array.isArray(value)) {
        // @ts-ignore
        if (value?.length === initialValue?.length && isEqualArraysById(value, initialValue)) {
          removeFromValuesChangedObject(name)
        } else {
          // @ts-ignore
          addToValuesChangedObject(name, value)
        }
      } else if (value === null) {
        if (!initialValue || (Array.isArray(initialValue) && !initialValue.length)) {
          removeFromValuesChangedObject(name)
        } else {
          // @ts-ignore
          addToValuesChangedObject(name, value)
        }
      } else if (value.id === initialValue?.id) {
        removeFromValuesChangedObject(name)
      } else {
        // @ts-ignore
        addToValuesChangedObject(name, value)
      }
    } else {
      if (equalsToInitialValue === true) {
        removeFromValuesChangedObject(name)
      } else if (equalsToInitialValue === false) {
        // @ts-ignore
        addToValuesChangedObject(name, value)
      }
    }

    // @ts-ignore
    // setTouchedFields((prev) => (prev.includes(name) ? prev : [...prev, name]))

    if (customOnChange) {
      customOnChange(value, setValuesChanged, cloneDeep(initialValues), optionsData) // TODO: do we need cloneDeep here?
    }
  }

  return {
    formErrors,
    setFormErrors,
    updateCheckboxValue,
    updateInputValue,
    updateSelectValue,
    validateField,
    addToValuesChangedObject,
  }
}

export default useForm
