import { useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { object } from 'dot-object'
import merge from 'deepmerge'
import { slugify } from 'transliteration'
import { isObject } from '@aidsupply/components'
import { useMappedState } from '../../../hooks'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { organizationsRightPanelConfig } from './organizationsRightPanelConfig'
import { IOrganization } from '../../../commonInterfaces'

const OrganizationsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const dataInitial = useMappedState(selectSidebarInitialData)

  const onFormSubmit = (formValuesChanged: IOrganization) => {
    const requestBody: IOrganization = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key: string) => {
      if (isObject(requestBody[key] as Record<string, unknown>) && (requestBody[key] as Record<string, unknown>).id) {
        requestBody[key] = (requestBody[key] as Record<string, unknown>).id
      } else if (
        key !== 'supervised_organizations' &&
        Array.isArray(requestBody[key]) &&
        requestBody[key].length
      ) {
        requestBody[key] = requestBody[key].map((item) => (isObject(item) && item.id ? item.id : item))
      }
    })

    if (!dataInitial?.id && !requestBody.state) {
      requestBody.state = 'posted'
    }

    if (requestBody.description) {
      requestBody.description = merge(dataInitial?.description as Record<string, unknown>, requestBody?.description as Record<string, unknown>)
    }

    if (formValuesChanged.name_en || (formValuesChanged.name && !dataInitial?.name_en)) {
      requestBody.slug = slugify(formValuesChanged.name_en as string || formValuesChanged.name as string)
    }

    if ('year_founded' in requestBody && requestBody.year_founded === '') {
      requestBody.year_founded = null
    }

    const dataToSend = {
      id: dataInitial?.id,
      requestBody,
      type: 'organizations',
      skipFileUpload: true,
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm onSubmit={onFormSubmit} formData={organizationsRightPanelConfig(dataInitial?.id ? 'edit' : 'create')} />
}

export default OrganizationsRightPanel
