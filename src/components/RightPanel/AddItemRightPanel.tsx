import { useTranslation } from 'react-i18next'
import { EmptyTable, Icon, IMAGEKIT_PARAMS_CONFIG, NoImageAvailable, Typography, Image, Button, Input, FlexRow } from '@aidsupply/components'
import { useEffect, useState } from 'react'
import { useTheme } from 'styled-components'
import { useParams } from 'react-router'
import { useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { selectAllSystemCollections, selectDataType, selectSidebarInitialData, selectUpsertInProgress } from '../../redux-saga/selectors'
import { sideBarUpsert } from '../../redux-saga/reducers/sideBar'
import ReactTexty from '../../lib/react-texty'
import { useMappedState } from '../../hooks'
import { IMAGEKIT_URL } from '../../constants'
import { IBrandItem, ICategory, IInquiryItem } from '../../commonInterfaces'
import { TLanguages } from '../../locales'
import TableSearch from '../Table/TableSearch'
import { StateType } from '../Form/interfaces'

const AddItemRightPanel = () => {
  const theme = useTheme()
  const { t, i18n: { language } } = useTranslation('table')
  const { rightPanelId } = useParams()
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const system = useMappedState(selectAllSystemCollections)
  const isUpsertInProgress = useMappedState(selectUpsertInProgress)
  const initialData = useMappedState(selectSidebarInitialData)
  const type = useMappedState(selectDataType)

  const isCategoryItemsRightPanel = ['inquiries'].includes(type)

  const items: ICategory[] | Record<string, unknown>[] | IBrandItem[] = (isCategoryItemsRightPanel ? Object.values(system?.categories || {}) : 
  // @ts-ignore
  system?.items) || [] as Record<string, unknown>[]

  const [searchItem, setSearchItem] = useState('')
  const [addedItems, setAddedItems] = useState<IInquiryItem[] | Record<string, unknown>[] | IBrandItem[]>([])
  const [filteredItems, setFilteredItems] = useState<IInquiryItem[] | Record<string, unknown>[] | IBrandItem[]>([])

  useEffect(() => {
    if (searchItem.length) {
      const searchLower = searchItem.toLowerCase()
      const filteredItems = Object.values(items).filter((item) => {
        // @ts-ignore
        const sku = item?.sku?.toLowerCase() || ''
  
        if (sku.includes(searchLower)) {
          return true
        }
  
        if (item.translations && typeof item.translations === 'object') {
          const translationValues = Object.values(item.translations)
          const foundInTranslations = translationValues.some(
            // @ts-ignore
            (translation) => translation && (translation as string).toLowerCase().includes(searchLower)
          )
          if (foundInTranslations) {
            return true
          }
        }
  
        const name = item.name?.toLowerCase() || ''
        return name.includes(searchLower)
      })
      setFilteredItems(filteredItems as ICategory[])
    }
    if (searchItem.length === 0) {
      setFilteredItems(addedItems)
    }
  }, [searchItem, items.length, addedItems.length])

  const renderItemsList = () => {
    if (filteredItems.length === 0) {
      return (
        <div className="noItemsRightPanelContent">
          <EmptyTable text={t('noResults')}>
            <Icon name="notFound2" width="117px" height="117px" wrapperWidth="100%" margin="0 0 25px 0" />
          </EmptyTable>
          <Typography type="h4" margin="10px 0 0 0" fontWeight={500} textAlign="center">
            {t('searchEditableTableHintNoResults')}
          </Typography>
        </div>
      )
    }

    return (
      <>
        {filteredItems.map((item) => (
          <div key={item.id as number} className="itemCard">
            <div className="titleCard">
              {/* @ts-ignore */}
              {item?.photo_url ? (
                <Image
                  className="brandLogo"
                  // @ts-ignore
                  src={item?.photo_url || ''}
                  height={46}
                  width={46}
                  maxWidth={46}
                  imagekitParams={IMAGEKIT_PARAMS_CONFIG?.crm?.thumbnail}
                  imagekitUrl={IMAGEKIT_URL}
                  alt="photo item"
                />
              ) : (
                <NoImageAvailable height={46} width={46} iconWidth={25} iconHeight={25} />
              )}

              <div style={{ marginLeft: '10px', width: 'calc(100% - 46px)' }}>
                <Typography variant="body1" as={ReactTexty}>
                  {getItemTitle(item)}
                </Typography>
                <Typography variant="body2" className="text-gray-500">
                  {/* @ts-ignore */}
                  SKU: {item?.sku || ''}
                </Typography>
              </div>
            </div>
            <div>{renderItemControls(item)}</div>
          </div>
        ))}
      </>
    )
  }

  const handleAddItem = (item: IInquiryItem | Record<string, unknown>) => {
    const newItem = { id: item.id, ...item, quantity: 1 }
    // @ts-ignore
    setAddedItems((prev) => [...prev, newItem])
  }

  const handleQuantityChange = (itemId: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(itemId)
      return
    }
    setAddedItems((prev) =>
      prev.map((item) => (item.id === itemId ? { ...item, quantity: newQuantity } : item))
    )
  }

  const getAddedItemQuantity = (itemId: number) => {
    const addedItem = addedItems.find((item) => item.id === itemId)
    return addedItem?.quantity || 0
  }

  const isItemAdded = (itemId: number) => {
    return addedItems.some((addedItem) => addedItem.id === itemId)
  }

  const getItemTitle = (item: IInquiryItem | Record<string, unknown>) => {
    // @ts-ignore
    return item?.translations?.[language as TLanguages] || item?.name
  }

  const handleRemoveItem = (itemId: number) => {
    setAddedItems((prev) => prev.filter((item) => item.id !== itemId))
  }

  const handleResetItems = () => {
    setAddedItems([])
  }

  const renderItemControls = (item: IInquiryItem | Record<string, unknown>) => {
    const isAdded = isItemAdded(item?.id as number)
    const quantity = getAddedItemQuantity(item?.id as number)

    if (!isAdded) {
      return (
        <Button
          padding="6px"
          variant="bordered"
          fullWidth
          onClick={() => handleAddItem(item)}
          type="button"
          className="addItemButton"
        >
          <Icon name="plus2" height={20} width={20} wrapperHeight={20} wrapperWidth={20} />
        </Button>
      )
    }

    return (
      <div style={{ display: 'flex', alignItems: 'center', marginLeft: '10px' }}>
        <Input
          type="number"
          className="quantity"
          value={quantity}
          onChange={(value: string) => {
            handleQuantityChange(item?.id as number, parseInt(value) || 0)
          }}
          min={1}
        />

        <Icon
          name="trashBin"
          width={16}
          height={16}
          onClick={() => handleRemoveItem(item?.id as number)}
          className="iconDelete"
        />
      </div>
    )
  }

  const handleSaveItems = () => {
    if (addedItems.length === 0) {
      return
    }

    let requestBody: Record<string, unknown> = {}

    if (isCategoryItemsRightPanel) {
      const initialItems = (initialData?.inquiry_items ?? []) as IInquiryItem[];

      const addedItemsUpdated = addedItems.map<IInquiryItem>((item) => ({
        category_id: Number(item.id),
        characteristics: {},
        inquiry_id: rightPanelId ? Number(rightPanelId) : undefined,
        qty_new: Number(item.quantity),
        state: item?.state as StateType
      }));

      const updatedItems: IInquiryItem[] = [...initialItems, ...addedItemsUpdated];

      requestBody = {
        inquiry_items: updatedItems
      };
    }
  
    if (!isCategoryItemsRightPanel) {
      const mapItemsByType = {
        orders: 'order_items',
        shipments: 'shipment_items',
        invoices: 'invoice_items',
        inventories: 'inventory_items',
      } as const
    
      type ItemType = keyof typeof mapItemsByType
      const itemKey = mapItemsByType[type as ItemType]
    
      const initialItems = (initialData?.[itemKey] ?? []) as IBrandItem[]
    
      // @ts-ignore
      const addedItemsUpdated = addedItems.map<IBrandItem>((item) => {
        switch (type) {
          case 'orders':
            return {
              item_id: item?.id as number,
              order_id: initialData?.id as number,
              price: item?.price || 0,
              quantity: item?.quantity as number,
              // @ts-ignore
              total: item?.total || 0
            }
          case 'invoices':
            return {
              order_item_id: item?.id as number,
              price: item?.price || 0,
              quantity: item?.quantity as number,
              invoice_id: initialData?.id as number,
              // @ts-ignore
              total: item?.total || 0
            }
    
          case 'shipments':
            return {
              item_id: item?.id as number,
              quantity: item?.quantity as number,
              shipment_id: initialData?.id as number
            }
          case 'inventories':
            return {
              item_id: item?.id as number,
              quantity: item?.quantity as number,
              inventory_id: initialData?.id as number
            }
    
          default:
            return {} as IBrandItem
        }
      })
    
      const updatedItems: IBrandItem[] = [...initialItems, ...addedItemsUpdated]
    
      requestBody = {
        [itemKey]: updatedItems,
      }
    }

    const dataToSend = {
      id: initialData?.id,
      requestBody,
      type: type,
      parentType: 'documents',
    }    

    dispatch(sideBarUpsert({ ...dataToSend, navigate, forceNavigate: true }))
  }
  
  return (
    <>
      <TableSearch
        isMainTable={false}
        type="items"
        iconLeftProps={{ stroke: theme.color.general.gray3 }}
        iconRightProps={{ name: 'cross' }}
        searchValueState={searchItem}
        setSearchValue={setSearchItem}
        placeholder={t('searchEditableTable')}
        data={Object.values(items) as unknown as Record<string, unknown>[]}
      />
      <div
        className={
          searchItem === '' && addedItems.length === 0
          ? 'noItemsRightPanelContent'
          : addedItems.length > 0
            ? 'addItemRightPanelContent withAddedItems'
            : 'addItemRightPanelContent'
        }
      >
        {searchItem === '' && filteredItems.length === 0 ? (
          <>
            <EmptyTable text={t('noHereYet')}>
              <Icon name="notAdded" width="117px" height="117px" wrapperWidth="100%" margin="0 0 25px 0" />
            </EmptyTable>
            <Typography type="h4" margin="10px 0 0 0" fontWeight={500} textAlign="center">
              {t('searchEditableTableHint')}
            </Typography>
          </>
        ) : (
          renderItemsList()
        )}
      </div>
      {addedItems.length > 0 && (
        <div className="confirmationButtons">
          <FlexRow gap="10px">
            <Button
              variant="bordered"
              onClick={handleResetItems}
              disabled={isUpsertInProgress}
              iconName="reload"
            >
              {t('reset')}
            </Button>
            <Button
              className="saveItemsButton"
              variant="primary"
              fullWidth
              type="submit"
              onClick={handleSaveItems}
              disabled={isUpsertInProgress}
            >
              <Icon
                name="statusCompleted"
                size={20}
                stroke={theme.color.general.light}
                fill={'transparent'}
                style={{ marginRight: '5px' }}
              />
              {t('save')} ({addedItems.length})
            </Button>
          </FlexRow>
        </div>
      )}
    </>
  )
}

export default AddItemRightPanel
