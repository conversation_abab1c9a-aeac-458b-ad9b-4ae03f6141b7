declare module '@aidsupply/components' {
  export const Typography: ITypographyProps
  export const Button: IButtonProps
  export const Icon: IIconProps
  export const Select: ISelectProps
  export const Checkbox: ICheckboxProps
  export const DatePicker: IDatePickerProps
  export const Input: IInputProps
  export const ExpansionPanel: IExpansionPanelProps
  export const UiLink: IUiLinkProps
  export const Image: IImageProps
  export const FlexRow: IFlexRowProps
  export const IMAGEKIT_PARAMS_CONFIG: IParamsProps
  export const buildImagePath: IBuildImagePath
  export const CategoryCharacteristicsToChoose: ICategoryCharacteristicsToChooseProps
  export const IconsList: IIconsListProps
  export const ScreenContext: React.Context<IScreenContext>
  export const validateOneField: () => void
  export const isEqualArraysById: boolean
  export const PopupAlerts: IPopupAlerts
  export const Tabs: ITabs
  export const Userpic: IUserpic
  export const Label: ILabel
  export const TextPanel: ITextPanel
  export const Badge: IBadge
  export const Menu: IMenu
  export const HeaderMenuIcon: IHeaderMenuIcon
  export const Drawer: IDrawer
  export const Loader: ILoader
  export const Table: ITable
  export const Tooltip: ITooltip
  export const Tag: ITag
  export const Pagination: IPagination
  export const ErrorBoundary: IErrorBoundary
  export const Line: ILine
  export const Filters: IFilters
  export const Dropdown: IDropdown
  export const VirtualizedList: IVirtualizedList
  export const InfoRowsBlock: IInfoRowsBlock
  export const TranslationsCell: ITranslationsCell
  export const NoImageAvailable: INoImageAvailable
  export const EmptyTable: IEmptyTable
  export const Comment: IComment
  export const CategoryCharacteristicsChosen: ICategoryCharacteristicsChosenProps
  export const getPriceWithCurrency: (data: number, currency: ICurrency) => string
  export const useWindowWidth: () => number
  export const useElementRect: (ref: RefObject<null>, type: 'resize' | 'scroll') => { top: number; left: number; width: number; height: number }
  export const isObject: (val: Record<string, unknown> | string | number | IWarehousesRightPanel) => boolean
  export const convertArrayToObject: (val: unknown[], id: string) => Record<string, unknown>
  export const isObjectEmpty: (obj: Record<string, unknown> | boolean | Blob | File[] | IDraftFile | undefined) => boolean
  export const getTokens: (key: string, theme: DefaultTheme) => string
  export const usePrevious: (value?: string | number | boolean | null) => string | number | boolean | null
  export const validate: (
    validationRules: Record<string, Record<string, unknown>>
  ) => (values: Record<string, unknown>, data: Record<string, unknown>) => Record<string, string>
  export const useScreenContext: (theme: DefaultTheme) => React.Context<IScreenContext>
  export const getAvailableTranslation: (translations: ITranslations, lang: TLanguages, lng: TLanguages) => string
  export const getFilterLabels: (options: { id: string; label: {uk: string, en: string} | string}[], id: string | number) => string | { en: string; uk: string; }
  export const getFilterSlug: (options: { id: string; label: {uk: string, en: string} | string}[], id: string | number) => string
  export const ITEM_TO_API_KEYS: Record<string, unknown>
  export const ALL_PLATFORMS_LANGUAGES: string[]
}
