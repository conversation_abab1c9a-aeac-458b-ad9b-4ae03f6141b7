import styled from 'styled-components'

export const StyledDashboardWrapper = styled.div`
    padding: 20px 0;
    flex-grow: 1;
    max-height: 100vh;

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
      height: 100%;
      overflow: scroll;
      padding: 0 0 100px 0;
    }

    .filters {
        margin-left: auto;
    }

    .download {
        margin-left: 10px;

        .icon {
            rotate: 180deg;
        }
    }

    .charts {
      flex-grow: 1
    }

    .iconNotificationOff {
        border: 1px solid ${({ theme }) => theme.color.general.gray2};
        box-shadow: 0px 3px 4px -5px rgba(24, 24, 28, 0.03), 0px 1px 2px 0px rgba(24, 24, 28, 0.04);
        cursor: pointer;
    }

    .iconNotificationOn {
        position: absolute;
        bottom: 13px;
        left: 16px;
      }
    }
      .userpic {
        img {
          border-radius: 6px;
        }
    }
`

export const StyledDashboardCards = styled.div`
  display: flex;
  flex-wrap: wrap;
  height: auto;
  gap: 10px;
  margin-top: 0;

  //.gridItem {
  //    justify-content: center;
  //    display: flex;
  //}

  //
  // @media only screen and (min-width: ${({ theme }) => theme.breakpoints.md}px) {
  //     padding-left: 15px;
  // }

  // @media only screen and (min-width: ${({ theme }) => theme.breakpoints.sm}px) {
  //     justify-content: flex-start;
  // }

  //@media only screen and (max-width: 1285px) {
  //    grid-template-columns: [start] 1fr 1fr [end];
  //}
  //
  //@media only screen and (max-width: 890px) {
  //    grid-template-columns: [start] 1fr [end];
  //    margin-right: 0;
  //    order: 2;
  //}
`
export const StyledPanel = styled.div`
    border: 1px solid ${({ theme }) => theme.color.general.gray2};
    padding: 20px;
    border-radius: ${({ theme }) => theme.size.border.radius.bigger};

    .recipientsStats {
      margin-top: 15px;
    }

    .panelTitle {
      font-size: 16px;
      font-weight: 600;
    }

    &.main {
      flex-grow: 1;
      height: 400px;
    
      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
        width: 100%;
        max-width: 100%;
        margin: 0 0 20px 0;
        height: 500px;
      }
    }

    &.pie {
      height: 400px;
      max-height: 400px;
      width: 30%;
      max-width: 400px;

      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
        width: 100%;
        max-width: 100%;
        margin: 0 0 20px 0;
        height: 500px;
      }

      .legend {
        margin-top: -15px;
      }

      .recharts-layer {
        .recharts-pie-labels {
          .text {
            text-anchor: middle;
          }
        }
      }
    }
  }
}
`

export const StyledChartHeader = styled.div`
  display: flex;
  justify-content: space-between;

  .disclaimer {
    color: #8e8e93;
    font-size: 12px;
    font-weight: 400;
  }

  .opened {
    & > div {
      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
        font-size: 12px;
      }
    }
  }

  & button.withIcon.primary {
    background: none;
    text-transform: none;
    padding: 0;
    font-size: 16px;
    color: ${({ theme }) => theme.color.general.dark};

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
      font-size: 12px;
    }

    &:hover {
      background: none;
      opacity: 0.8;
    }
  }
`

export const StyledCard = styled.div`
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  padding: 20px;
  border-radius: ${({ theme }) => theme.size.border.radius.bigger};
  text-decoration: none;
  display: flex;
  min-width: 240px;
  max-width: 240px;
  justify-content: flex-start;
  flex: 1 0 auto;
  gap: 15px;

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xxl - 100}px) {
    padding: 15px;
    min-width: 215px;
    max-width: 215px;
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.sm}px) {
    min-width: 160px;
    max-width: 100%;
  }

  &.inProgress {
    .number.typography {
      color: transparent;
    }
  }

  .percentage {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: ${({ percent }: { percent: number }) => (percent > 0 ? '#36B37E' : percent < 0 ? '#FFAB00' : 'black')};
    margin-bottom: 4px;

    &.zero {
      margin-left: 7px;
      margin-bottom: 6px;
    }
  }

  .flexRow {
    display: flex;
    align-items: center;

    .icon {
      margin-right: 10px;
    }
  }

  // &:hover {
  //   border-color: ${({ theme }) => theme.color.primary.main};
  //   background-color: ${({ theme }) => theme.color.general.light};
  // }

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints.md}px) {
    // margin: 20px;
  }

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints.sm}px) {
    flex-direction: row;
  }
`
