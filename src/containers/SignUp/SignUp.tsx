import { Link as RouterLink } from 'react-router'
import { useTheme } from 'styled-components'
import { Typography, UiLink } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { INTRO_PAGES_IMG } from '../../constants'
import IntroScreenWrapper from '../IntroScreen/IntroScreenWrapper'
import FormSignUp from './FormSignUp'

const initialValues: {
  full_name: string
  organization_name?: string
  email: string
  password: string
} = {
  full_name: '',
  organization_name: '',
  email: '',
  password: '',
}

const SignUp = () => {
  const theme = useTheme()
  const { t } = useTranslation('signIn')

  return (
    <IntroScreenWrapper text={t('signUp')} type="signUp" theme={theme} imageUrl={INTRO_PAGES_IMG}>
      <FormSignUp initialValues={initialValues} />

      <div className="inscriptionWrapper">
        <Typography type="body1" color={theme.color.general.dark} margin="20px 0 0 0">
          {t('alreadyHaveAccount')}
        </Typography>
        <Typography type="button1">
          <UiLink to="/signin" className="auth inline" Link={RouterLink} themeColor="general.dark">
            {t('signIn')}
          </UiLink>
        </Typography>
      </div>

      <div className="inscriptionWrapper terms">
        <Typography type="body2" color={theme.color.general.dark} margin="20px 0 0 0" className="inline">
          {t('legalPagesInscription')}
        </Typography>
        <Typography type="body2" fontWeight={500} color={theme.color.primary.main} className="inline">
          <UiLink
            to="/terms-and-conditions"
            className="link inline"
            themeColor="primary.main"
            target="_blank"
          >
            &nbsp;{`${t('withTermsAndConditions')}.`}
          </UiLink>
        </Typography>
      </div>
    </IntroScreenWrapper>
  )
}

export default SignUp
