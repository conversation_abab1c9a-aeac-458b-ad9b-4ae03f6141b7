class ErrorService {
  constructor() {
    this.callbacks = {}
  }

  error(type, errorString) {
    if (!this.callbacks[type]) {
      console.log('invalid type!')
      return
    }

    this.callbacks[type](errorString)
  }

  add(type, callback) {
    if (this.callbacks[type]) {
      console.log('Type is already defined!')
      return
    }

    this.callback[type] = callback
  }

  remove(index) {
    if (!this.callbacks[index]) {
      console.log('invalid index!')
      return
    }

    delete callbacks[index]
  }

  update(type, callback) {
    this.callback[type] = callback
  }

  onError(callback) {
    this.callbacks[this.pointer] = callback
    const pointer = this.pointer // index of where the error handler is kept
    this.pointer += 1

    return pointer
  }
}
