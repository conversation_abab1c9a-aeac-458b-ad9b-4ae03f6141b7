import { ReactNode, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router'
import { useDispatch } from 'react-redux'
import { object, pick } from 'dot-object'
import { FlexRow, Input, isObject } from '@aidsupply/components'
import { useMappedState } from '../../../hooks'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { selectAllSystemCollections, selectSidebarInitialData, selectUserDetails } from '../../../redux-saga/selectors'
import { sidebarItemSet, sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { inquiriesRightPanelConfig } from './inquiriesRightPanelConfig'
import { IInquiry, IInquiryItem } from '../../../commonInterfaces'
import { TLanguages } from '../../../locales'
import { IFieldsProps, IFormConfig, UpdateInput } from '../../../components/Form/interfaces'
import { useTranslation } from 'react-i18next'
import InfoBlocks from '../utils/InfoBlocks'
import StatusSelect from '../../../components/RightPanelForms/StatusSelect'
import ActionsDropdown from '../../../components/RightPanelForms/ActionsDropdown'
import { ISystem } from '../../../redux-saga/reducers/data'
import UploadFileButtons from '../utils/UploadFileButtons'
import UploadFiles from '../../../components/UploadFiles'
import EditableTableFormPart from '../../../components/RightPanelForms/EditableTableFormPart'
import { getEditableTableColumns } from '../../../components/config/columnsEditableTable'
import { EDITABLE_TABLE_NAVBAR_FORM_CONFIG } from '../../../components/config/editableTableNavBarForms'
import { fileParse, IDraftFile } from '../../../redux-saga/reducers/fileUpload'
import { FILE_TYPES } from '../../../constants'
import ActivityTabs from '../../../components/RightPanelForms/ActivityTabs'

const FormBlockWrapper = ({
  children,
  blockKey,
  id,
  isReadOnly,
  formValues,
  formFieldsProps,
  fields,
  getPanelHeader,
  optionsData,
  customBlockValues,
  updateSelect,
  updateInput,
  formValuesChanged,
}: {
  children: ReactNode
  blockKey: string
  id: number
  isReadOnly: boolean
  formValues: IInquiry
  formFieldsProps: Record<string, unknown>
  fields: Record<string, unknown>
  getPanelHeader: (blockKey: string) => string
  optionsData: Record<string, unknown>
  customBlockValues: Record<string, unknown>
  updateSelect: (blockKey: string) => void
  setTextsChosenLng: (lng: TLanguages) => void
  updateInput: UpdateInput
  formValuesChanged: IInquiry
}) => {
  const dispatch = useDispatch()
  const {
    t,
    i18n: { language },
  } = useTranslation(['table', 'forms', 'general'])
  const params = useParams()
  const { rightPanelId } = params

  const system = useMappedState(selectAllSystemCollections) as ISystem
  const user = useMappedState(selectUserDetails)
  const [uploadType, setUploadType] = useState<'file' | 'table'>('file')
  const [isUploadFileActive, setIsUploadFileActive] = useState(false)
  const [replaceOption, setReplaceOption] = useState({ id: 'add', label: t('forms:addToExistingItems') })
  const [formValuesWithCategoriesNames, setFormValuesWithCategoriesNames] = useState<IInquiry>(formValues)
  
  const categories = system?.categories
  
  const editableTableColumns = getEditableTableColumns('inquiries.inquiry_items', language as TLanguages, t)
  const editableTableConfig = EDITABLE_TABLE_NAVBAR_FORM_CONFIG['inquiries.inquiry_items'] as unknown as IFormConfig

  useEffect(() => {
    setReplaceOption({ id: 'add', label: t('forms:addToExistingItems') })
  }, [formValues.id])

  useEffect(() => {
    if(formValues.inquiry_items?.length) {
    const inquiryItemsWithCategoriesNames = formValues.inquiry_items?.map((item: IInquiryItem) => ({
      ...item,
      name: (categories?.[item?.category_id as number] as unknown as { translations: Record<string, string> })?.translations?.[language as TLanguages],
    }))
      const newFormValues = { ...formValues, inquiry_items: inquiryItemsWithCategoriesNames }
      setFormValuesWithCategoriesNames(newFormValues)
    }
  }, [formValues.id, categories, formValues.inquiry_items?.length])

  useEffect(() => {
    if (rightPanelId === 'clone') {
      setUploadType('table')
    } else {
      setUploadType('file')
      setIsUploadFileActive(false)
    }
  }, [rightPanelId])

  const additionalFormValues = editableTableConfig?.
  optionsFromValuesDependencies?.reduce(
    (acc, curr) => ({ ...acc, [curr]: pick(curr, formValues) }),
    {}
  )

  const onFileDrop = (newFiles: IDraftFile[]) => {
    let is_replace_existing = false
    if (replaceOption.id === 'replace') {
      is_replace_existing = true
    }
    const payload = {
      entity_type: 'inquiriesParseFile',
      file: newFiles[0],
      id: id as number,
      entityTypeId: 'inquiry_id',
      is_replace_existing: is_replace_existing
    }
    dispatch(fileParse({ ...payload }))

    dispatch(sidebarItemSet({ ...formValues, is_being_processed: true }))
  }

  // TODO: uncomment when upload file added
  // const isShowSelect = () => {
  //   if (uploadType === 'file' &&
  //     formValues.inquiry_items &&
  //     Array.isArray(formValues.inquiry_items) &&
  //     formValues.inquiry_items.length > 0 &&
  //     formValues.state === 'drafted'
  // ) {
  //   return (
  //     <Select
  //       value={replaceOption}
  //       onChange={(option: { id: string; label: string }) => setReplaceOption(option)}
  //       options={[
  //         { id: 'add', label: t('forms:addToExistingItems') },
  //         { id: 'replace', label: t('forms:replaceExistingItems') },
  //       ]}
  //       withBorder
  //       margin="20px 0"
  //       />
  //     )}
  // }

  const infoBlocks = [
    {
      label: t('recipient'),
      icon: null,
      id: formValues?.recipient_id,
      type: 'userpic',
    }
  ]

  return (
      <>
        {blockKey === 'info' && <InfoBlocks infoBlocks={infoBlocks} />}

        {blockKey === 'statusWithActions' && system?.inquiryStatuses && (
          <FlexRow gap="10px" alignItems="center" justifyContent="space-between">
            <StatusSelect
              id={formValues.id as number}
              type="inquiries"
              currentStatus={formValues.status as string}
              systemStatuses={system.inquiryStatuses}
              isDisabled={formValues.state === 'deleted'}
            />
            <ActionsDropdown selectedItem={formValues} type="inquiries" />
          </FlexRow>
        )}

        {blockKey === 'activity' && (
          <ActivityTabs
            updateInput={updateInput}
            formValues={formValues as unknown as Record<string, unknown>}
            formValuesChanged={formValuesChanged as unknown as Record<string, unknown>}
            isReadOnly={formValues.state === 'deleted'}
            withTabsFieldsCustom={['comments', 'attachments']}
          />
        )}

        {blockKey === 'notes' && (
          <Input
            multiline
            withBorder
            variant="secondary"
            value={formValues.notes || ''}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => updateInput({ target: { name: 'notes', value: event.target.value } })}
            maxLength="1000"
            readOnly={formValues.state === 'deleted'}
          />
        )}

        {blockKey === 'inquiry_items' && (id || rightPanelId === 'clone') && (
          <>
            {!isReadOnly && rightPanelId !== 'clone' && (
              <UploadFileButtons<IInquiry>
                formValues={formValues}
                uploadType={uploadType}
                setUploadType={setUploadType}
                setIsUploadFileActive={setIsUploadFileActive}
              />
            )}
            {/* TODO: uncomment when upload file added */}
            {/* <div style={{margin: '20px 0'}}>
              {isShowSelect()}
            </div> */}

            {uploadType === 'file' && isUploadFileActive && formValues.state !== 'posted' && (
              <UploadFiles
                style={{ marginTop: 10, marginBottom: 10 }}
                entityType="inquiries"
                entityId={id}
                fileGroup="inquiry_items"
                fileTypes={{ 'application/vnd.ms-excel': FILE_TYPES.files['application/vnd.ms-excel'] }}
                filesType="files"
                onFnSet={onFileDrop as unknown as (newFiles: File[] | IDraftFile[]) => void}
                isUploadInProgress={formValues.is_being_processed as boolean}
                maxFiles={1}
                isReadOnly
              />
            )}

            <EditableTableFormPart
              additionalFormValues={additionalFormValues as unknown as Record<string, unknown>}
              id={id}
              cellProps={{
                system,
                custom_block_values: customBlockValues,
                lng: language,
                id,
                user,
                is_read_only: isReadOnly,
                additional_form_values: additionalFormValues,
              }}
              isNavbarHidden={uploadType === 'file'}
              compoundTypeKey="inquiries.inquiry_items"
              optionsData={optionsData}
              language={language}
              typeData={{ key: 'inquiries.inquiry_items' }}
              columns={editableTableColumns}
              // @ts-ignore
              data={formValuesWithCategoriesNames[blockKey] as unknown as Record<string, unknown>[]}
              blockKey={blockKey}
              formFieldsProps={formFieldsProps}
              fields={fields as unknown as IFieldsProps[]}
              getPanelHeader={getPanelHeader}
              updateTableRows={updateSelect.bind(null, blockKey)}
              validationRules={editableTableConfig.validationRules as unknown as Record<string, unknown>}
              t={t}
            />
          </>
        )}
        
        {children}
      </>
  )
}

const InquiriesRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const params = useParams()
  const { rightPanelId } = params

  const sidebarInitialData = useMappedState(selectSidebarInitialData) as unknown as IInquiry

  const onFormSubmit = (formValuesChanged: IInquiry) => {
    const requestBody = { ...object(formValuesChanged) } as IInquiry

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key as keyof IInquiry]) && (requestBody[key as keyof IInquiry] as { id: number }).id) {
        // @ts-ignore
        requestBody[key as keyof IInquiry] = requestBody[key as keyof IInquiry]?.id
      }
    })

    if (formValuesChanged.inquiry_items?.length) {
      requestBody.inquiry_items = formValuesChanged.inquiry_items.map((item: IInquiryItem) => ({
        status: item.status,
        category_id: typeof item.category_id === 'object' && item.category_id ? (item.category_id as unknown as {id: number}).id : item.category_id,
        characteristics_keys: item.characteristics_keys,
        characteristics_values: item.characteristics_values,
        qty_new: item.qty_new,
        id: item.id,
      })) as IInquiryItem[]
    }

    const dataToSend = {
      id: sidebarInitialData?.id,
      requestBody,
      type: 'inquiries',
      parentType: 'documents',
    }    

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }
  
  return <EditForm
    initialValues={sidebarInitialData as unknown as Record<string, unknown>}
    onSubmit={onFormSubmit}
    formData={sidebarInitialData?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new' ? {
      validationRules: {},
      fields: {
        info: [],
        statusWithActions: [],
        inquiry_items: [],
        activity: [],
        notes: [],
      },
    } : inquiriesRightPanelConfig(sidebarInitialData?.id  || rightPanelId === 'clone' ? 'edit' : 'create')}
    FormBlockWrapper={FormBlockWrapper as unknown as ReactNode}
    buttonsAreSticky
    withActions
    isReadOnly={sidebarInitialData?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new'}
  />
}

export default InquiriesRightPanel
