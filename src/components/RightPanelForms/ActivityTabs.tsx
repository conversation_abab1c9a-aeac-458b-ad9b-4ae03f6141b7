import FormBlockTabs from '../Form/FormBlockTabs'
import Comments from '../OrderForm/Comments'
import Attachments from './Attachments'
import { UpdateInput } from '../Form/interfaces'

const ActivityTabs = ({
  updateInput,
  type,
  isReadOnly,
  withTabsFieldsCustom,
}: {
  updateInput: UpdateInput
  isReadOnly: boolean
  type: string
  withTabsFieldsCustom?: string[]
}) => {
  return (
    <FormBlockTabs
      type={type}
      withTabsFieldsCustom={withTabsFieldsCustom || ['attachments', 'comments']}
      blockKey="activity"
      dropdownItems={[]}
      isLanguageTabs={false}
      getTabScreenCustom={(tab) => {
        switch (tab) {
          case 'comments':
            return <Comments updateInput={updateInput} isReadOnly={isReadOnly} />
          case 'attachments':
            return <Attachments isReadOnly={isReadOnly} type={type} updateInput={updateInput} blockKey="files" />
          default:
            return null
        }
      }}
      titleProps={{ type: 'button1', textTransform: 'none', fontWeight: 600 }}
    />
  )
}
export default ActivityTabs
