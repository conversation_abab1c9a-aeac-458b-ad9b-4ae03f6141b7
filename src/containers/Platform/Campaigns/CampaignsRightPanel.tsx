import merge from 'deepmerge'
import { object } from 'dot-object'
import { useNavigate } from 'react-router'
import { slugify } from 'transliteration'
import dayjs from 'dayjs'
import { useDispatch } from 'react-redux'
import { useMappedState } from '../../../hooks'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import { isObject } from '@aidsupply/components'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { campaignsRightPanelConfig } from './campaignsRightPanelConfig'
import { StateType } from '../../../components/Form/interfaces'
import { ITranslations } from '../../../locales'

interface ICampaignsRightPanel {
  state?: StateType
  translations?: ITranslations
  [key: string]: unknown
}

const CampaignsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const isReadOnly = false

  const itemInitial = useMappedState(selectSidebarInitialData)

  const onSubmit = (formValuesChanged: ICampaignsRightPanel) => {
    const requestBody: ICampaignsRightPanel = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key]) && (requestBody[key] as { id: number }).id) {
        requestBody[key] = (requestBody[key] as { id: number }).id
      }
    })
    
    if (requestBody?.translations?.en) {
      requestBody.name = requestBody.translations.en
    }

    if (requestBody.translations) {
      requestBody.translations = merge(itemInitial?.translations as ITranslations, requestBody.translations as ITranslations)

      if (formValuesChanged.translations?.en || !itemInitial?.slug) {
        requestBody.slug = slugify(requestBody.translations.en)
      }
    }

    if (requestBody.body) requestBody.body = merge(itemInitial?.body as ITranslations, requestBody.body as ITranslations)

    if (requestBody.deadline_at)
      requestBody.deadline_at = dayjs(new Date(requestBody.deadline_at as string)).format('YYYY-MM-DDTHH:mm:ss.SSS')

    const dataToSend = {
      id: itemInitial?.id,
      requestBody,
      type: 'campaigns',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} onSubmit={onSubmit} formData={campaignsRightPanelConfig} customType="campaigns" />
}

export default CampaignsRightPanel
