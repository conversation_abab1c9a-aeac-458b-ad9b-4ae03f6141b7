import clsx from 'clsx'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { pick } from 'dot-object'
import { Checkbox, DatePicker } from '@aidsupply/components'
import FormInput from './FormInput'
import { IFormFieldProps } from './interfaces'
import FormSelect from './FormSelect'
import { StyledInputWrapper, StyledLabel } from './styled'
import { useSelector } from 'react-redux'
import { IStore } from '../../configureStore.ts'
import { selectFormValueByKey, selectFormValuesWhole } from '../../redux-saga/selectors.ts'

const FormField = (props: IFormFieldProps) => {
  const {
    field,
    formError,
    formErrors,
    formFlexDirection,
    formSubmit,
    formValuesCustom,
    initialValues,
    isReadOnly,
    isSubmitOnBlur,
    labelKey,
    labelType,
    name,
    mainFormData,
    optionsData,
    sidebarFormId,
    submitByEnterPressed,
    type,
    updateCheckbox,
    updateInput,
    updateSelect,
    validateField,
    validationRules,
    withTabs,
  } = props

  const { t, i18n } = useTranslation(['forms', 'table', 'validation'])
  const lng = i18n.language

  const isSelect = field.component === 'dropdown'

  const formValues = formValuesCustom || useSelector((state: IStore) => selectFormValuesWhole(state, type))
  const formValue = formValuesCustom
    ? formValues[name] || pick(name, formValues)
    : useSelector((state: IStore) => selectFormValueByKey(state, type || '', name))
  const initialValue = initialValues && pick(name, initialValues)

  const isDisabled =
    (field.isReadOnly !== false && isReadOnly) ||
    (field.getDisabled && field.getDisabled(formValues, optionsData, initialValues))

  const disabled = field.disabled || field.isDisabled || isDisabled

  const isHidden = field.getIsHidden && field.getIsHidden(initialValues || {}, optionsData, formValues || {})

  const optionsFromFormValues =
    field.getOptionsFromFormValues &&
    optionsData &&
    field.getOptionsFromFormValues(formValues, optionsData, mainFormData)

  const valueNormalized = typeof formValue === 'number' ? formValue.toString(10) : formValue
  const value =
    (field.transformValue ? field.transformValue(valueNormalized as string, lng) : valueNormalized) ||
    field.defaultValue ||
    ''

  const labelTranslation =
    field.label && (i18n.exists(field.label, { ns: 'forms' }) ? t(field.label) : t(`table:${field.label}`))

  useEffect(() => {
    // TODO: fix for units in item content and stock items warehouse_id
    if (
      field.defaultValue &&
      ((formValue as { id: string })?.id !== (field.defaultValue as { id: string })?.id ||
        formValue !== field.defaultValue)
    ) {
      if (isSelect && updateSelect && field.key) {
        updateSelect(field.key)(field.defaultValue)
      } else if (updateInput) {
        updateInput({ target: { name: field.key as string, value: field.defaultValue as string } })
      }
    }
  }, [])

  const commonSelectAndInputProps = {
    className: clsx('formInput', isHidden! && 'isHidden'),
    type: field.type,
    name,
    field,
    formSubmit,
    isSubmitOnBlur,
    variant: field.variant,
    error:
      (formError && typeof formError === 'string' && formError !== 'success'
        ? t(
            `validation:${formError}`,
            field.validationTranslationKeys
              ? {
                  fields: field.validationTranslationKeys.reduce(
                    (acc, curr, i, arr) => `${acc}${t(curr)}${i < arr.length - 1 ? ', ' : ''}`,
                    ''
                  ),
                }
              : {}
          )
        : undefined) || field.error,
    success: formError === 'success',
    placeholder:
      field.placeholder &&
      (i18n.exists(field.placeholder, { ns: 'forms' })
        ? t(field.placeholder)
        : t(field.placeholder, { ns: 'table', nsSeparator: '|' })), //   default namespace separator is ":" which is not suitable for web site placeholder
    required: withTabs
      ? !!field.required &&
        Array.isArray(
          validationRules?.[
            name as string // @ts-ignore
          ]
        ) &&
        validationRules?.[name as string]?.includes('required')
      : !!field.required,
    initialValue: initialValue || field.initialValue || '',
    labelType: field.labelType || labelType,
    labelWidth: '100px',
    label: labelTranslation,
    t,
    withBorder: true,
    primaryFocusColor: true,
    fullWidth: true,
    isDisabled: disabled,
    isHidden,
    value,
    validateField,
  }

  if (typeof field.component === 'function') {
    return (
      <div className={clsx(formFlexDirection, field.className, isHidden! && 'isHidden')}>
        {labelTranslation && (
          <StyledLabel
            labelFontWeight={field.labelFontWeight}
            text={labelTranslation}
            variant={field.variant || 'primary'}
          />
        )}
        {field.component({
          ...commonSelectAndInputProps,
          errors: formErrors,
          formSubmit,
          isSubmitOnBlur,
          lng,
          optionsData,
          sidebarFormId,
          labelKey,
          optionsFromFormValues,
          updateSelect,
          updateInput,
        })}
      </div>
    )
  } else if (field.component === 'checkbox') {
    return (
      <Checkbox
        {...field}
        disabled={disabled}
        key={field.key}
        name={name}
        value={field.value}
        checked={formValue}
        variant={field.variant || 'primary'}
        label={labelTranslation}
        handleChange={(checked: boolean, e: InputEvent) => {
          if (validateField) {
            validateField(name, checked, formValues)
          }
          if (field.onChange) {
            field.onChange(checked, e as any)
          } else if (updateCheckbox) {
            updateCheckbox(checked, e as any)
          }
        }}
        labelType={field.labelType || 'body2'}
        className="formCheckbox"
      />
    )
  } else if (field.component === 'date') {
    return (
      <DatePicker
        onChange={(date: string) => {
          if (validateField) {
            validateField(name, date, formValues)
          }
          updateInput?.({ target: { name: name as string, value: date as string } }, field?.onInputValueChange)
        }}
        minDate={field.minDate}
        withIcon
        iconLeftProps={{ strokeWidth: 1, fill: 'black', name: 'calendarEdit', width: 20, height: 20 }}
        {...commonSelectAndInputProps}
      />
    )
  }

  return (
    <StyledInputWrapper
      width={field.width}
      className={clsx(formFlexDirection, field.className, isHidden! && 'isHidden')}
    >
      {field.component === 'dropdown' ? (
        <FormSelect
          {...commonSelectAndInputProps}
          formValues={formValues}
          labelKey={labelKey}
          lng={lng}
          optionsData={optionsData}
          optionsFromFormValues={optionsFromFormValues}
          updateSelect={updateSelect}
        />
      ) : (
        <FormInput
          formValues={formValues}
          submitByEnterPressed={submitByEnterPressed}
          updateInput={updateInput}
          hideButtons={field.hideButtons}
          {...commonSelectAndInputProps}
        />
      )}
    </StyledInputWrapper>
  )
}

export default FormField
