import { getPriceWithCurrency } from '@aidsupply/components'
import { getDate, getSelectCellValue } from '../../../components/config/columns'
import ReactTexty from '../../../lib/react-texty'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { getCurrencyObjectById } from '../../../utils/common'
import { ICurrency, IInvoices } from '../../../commonInterfaces'

export const invoicesColumnsConfig = (
  _lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    getNameByMode({
      t,
      dataKey: 'invoiceNumber',
      option: null,
      tableMode: tableMode || 'table',
    }),
    getDate(t, tableMode),
    {
      key: 'orderNumber',
      sortable: true,
      dataKey: 'order_id',
      labelKey: 'number',
      noTranslation: true,
      optionsKeys: ['orders'],
      width: 0,
      flexGrow: 1,
      cellRenderer: getSelectCellValue,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'issuer',
      sortable: true,
      dataKey: 'issuer_id',
      labelKey: 'name',
      noTranslation: true,
      cellRenderer: getSelectCellValue,
      optionsKeys: ['organizations'],
      width: 0,
      flexGrow: 0.8,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'payer',
      sortable: true,
      dataKey: 'payer_id',
      labelKey: 'name',
      noTranslation: true,
      cellRenderer: getSelectCellValue,
      optionsKeys: ['organizations'],
      width: 0,
      flexGrow: 0.8,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'sum',
      sortable: true,
      dataKey: 'total',
      width: 0,
      flexGrow: 0.8,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({
        rowData,
        cellData,
        container: {
          props: { cellProps },
        },
      }: {
        rowData: IInvoices
        cellData: number
        container: {
          props: { cellProps: { system: { currencies: ICurrency[] } } }
        }
      }) => {
        const currency = rowData.currency_id && getCurrencyObjectById(rowData.currency_id, cellProps.system?.currencies)

        return (
          <ReactTexty>{currency ? getPriceWithCurrency(cellData || 0, currency) : cellData}</ReactTexty>
        )
      },
    },
  ]
}
