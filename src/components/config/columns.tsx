import { Tooltip, Icon, ITEM_TO_API_KEYS, Tag, Typography } from '@aidsupply/components'
import { headerRenderer } from '../../utils/table'
import { getSelectLabel } from '../../utils/common'
import { formatDateToUserTimezone } from '../../utils/dates'
import ReactTexty from '../../lib/react-texty'
import { ITranslations, TLanguages } from '../../locales'
import { usersColumnsConfig } from '../../containers/Admin/Users/<USER>'
import { organizationsColumnsConfig } from '../../containers/Admin/Organizations/organizationsColumnsConfig'
import { TableModeType } from '../../redux-saga/reducers/data'
import { STATES_ICONS, STATUSES_ICONS } from './table'
import { brandsColumnsConfig } from '../../containers/System/Brands/brandsColumnsConfig'
import styled from 'styled-components'
import { categoriesColumnsConfig } from '../../containers/System/Categories/categoriesColumnsConfig'
import { itemsColumnsConfig } from '../../containers/System/Items/itemsColumnsConfig'
import { unitsColumnsConfig } from '../../containers/System/Units/unitsColumnsConfig'
import { attributesColumnsConfig } from '../../containers/System/Attributes/attributesColumnConfig'
import { warehousesColumnsConfig } from '../../containers/Inventory/Warehouses/warehousesColumnsConfig'
import { NameOptionType } from '../../commonTypes'
import { ordersColumnsConfig } from '../../containers/Documents/Orders/ordersColumnsConfig'
import { faqsColumnsConfig } from '../../containers/Platform/FAQs/faqsColumnsConfig'
import { reportsColumnsConfig } from '../../containers/Platform/Reports/reportsColumnsConfig'
import { invoicesColumnsConfig } from '../../containers/Documents/Invoices/invoicesColumnsConfig'
import { shipmentsColumnsConfig } from '../../containers/Documents/Shipments/shipmentsColumnsConfig'
import { pagesColumnsConfig } from '../../containers/Platform/Pages/pagesColumnsConfig.'
import { postsColumnsConfig } from '../../containers/Platform/Posts/postsColumnsConfig'
import { inquiriesColumnsConfig } from '../../containers/Documents/Inquiries/inquiriesColumnsConfig'
import { campaignsColumnsConfig } from '../../containers/Platform/Campaigns/campaignsColumnsConfig'
import { inventoriesColumnsConfig } from '../../containers/Inventory/Inventories/inventoriesColumnsConfig'
import { dashboardColumnsConfig } from '../../containers/Dashboard/dashboardColumnsConfig'

export const StyledStatusNameCell = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
  }
`
export interface IRowData {
  name?: string
  full_name?: string
  status?: string | { id: string }
  state?: string | {id: string }
  translations?: ITranslations
  number?: string
  recipient_id?: string
}

export const getOption = (rowData: IRowData)  => {
  const { status, state } = rowData
  if (['deleted', 'drafted'].includes(state as string)) return 'state';
  if (['confirmed', 'in_progress', 'pending', 'completed', 'canceled', 'shipped', 'paid', 'active', 'delivered'].includes(status as string)) return 'status';
};

const getIconName = (option: NameOptionType | null, rowData: IRowData, key: string) => {
  const iconName = (option || getOption(rowData)) === 'status' ?
    STATUSES_ICONS[key as string] :
    STATES_ICONS[key as string]

  return iconName
}

export const getOptionWithNameColumn = (
  t: (key: string) => string,
  tableMode: TableModeType = 'table',
  key: string,
  dataKey: string,
  option: NameOptionType | null,
  lng?: TLanguages
) => ({
  key: key,
  dataKey: dataKey,
  title: t(`${dataKey}`),
  sortable: true,
  width: 0,
  flexGrow: 2,
  cellRenderer: ({
    rowData,
    column,
  }: {
    rowData: IRowData
    column: { key: string }
  }) => {
    const rowOption = option ? rowData[option] : rowData[getOption(rowData) as NameOptionType]
    const key = typeof rowOption === 'object' ? rowOption?.id : rowOption;
    const iconName = getIconName(option, rowData, key as string)
    const translations = lng && rowData?.translations?.[lng]

    return (
      <StyledStatusNameCell>
        {iconName ? (
          <Tooltip
            left={tableMode === 'table' ? '8px' : undefined}
            right={tableMode === 'table' ? undefined : '8px'}
            text={t(`statuses:${key}`)}
            arrowPosition={tableMode === 'table' ? 'left' : 'right'}
          >
            <Icon
              key={column.key}
              name={iconName}
              width={16}
              height={16}
            />
          </Tooltip>
        ) : ''}
        <Typography as={ReactTexty}>
          {translations || rowData?.name || rowData?.full_name || rowData?.number || ''}
        </Typography>
      </StyledStatusNameCell>
    );
  },
  headerRenderer: headerRenderer(t, tableMode),
});

export const getStatusColumnProps = (
  t: (key: string) => string,
  iconProps: Record<string, unknown> = {},
  tableMode?: TableModeType,
) => ({
  key: 'status',
  dataKey: 'status',
  title: 'Status',
  width: 35,
  flexShrink: 0,
  resizable: false,
  sortable: true,
  cellRenderer: ({ cellData, column }: { cellData: string | { id: string }; column: { key: string } }) => {
    const key = typeof cellData === 'object' ? cellData?.id : cellData
    const iconName = STATUSES_ICONS[key]
    return iconName ? (
      <Tooltip
        left={tableMode === 'table' ? '8px' : undefined}
        right={tableMode === 'table' ? undefined : '8px'}
        text={t(`${cellData}`)}
        arrowPosition={tableMode === 'table' ? 'left' : 'right'}
      >
        <Icon
          key={column.key}
          className="stateIcon"
          onClick={() => {}}
          name={iconName}
          width={16}
          padding="10px"
          {...iconProps}
        />
      </Tooltip>
    ) : (
      ''
    )
  },
  headerRenderer: () =>
    tableMode !== 'list' ? <Icon title={t('status')} name="status" wrapperWidth="100%" /> : '',
})

export const getDate = (t: (key: string) => string, tableMode?: TableModeType, format?: string, key?: string, dataKey?: string) => ({
  key: key || 'created',
  sortable: true,
  dataKey: dataKey || 'created_at',
  width: 0,
  flexGrow: 1,
  cellRenderer: ({ cellData }: { cellData: string }) =>
    cellData ? <ReactTexty>{formatDateToUserTimezone(cellData, format || 'DD.MM.YYYY')}</ReactTexty> : '',
  headerRenderer: headerRenderer(t, tableMode),
})

export const getSelectCellValue = ({
  cellData: value,
  column,
  container,
  returnTextOnly,
}: {
  cellData: string
  column: {
    key: string
    optionsKeys: string[]
    labelKey: string
    valueKey: string
    isMulti: boolean
    noTranslation: boolean
  }
  container: {
    props: {
      cellProps: {
        system: Record<string, unknown>
        lng: TLanguages
      }
    }
  }
  returnTextOnly: boolean
}) => {
  const {
    props: {
      cellProps: { system, lng },
    },
  } = container
  const { key, optionsKeys, labelKey, valueKey, isMulti, noTranslation } = column

  const optionsCollection = optionsKeys?.reduce(
    (acc, curr) => (system[curr] ? { ...acc, ...system[curr] } : {}),
    {}
  )

  // @ts-ignore
  const collection: Record<string, unknown> =
    (optionsCollection && Object.keys(optionsCollection).length && optionsCollection) ||
    system[key] ||
    system[ITEM_TO_API_KEYS[key] as string]

  if (!collection || !value) {
    return
  }

  if ((isMulti && Array.isArray(value)) || Array.isArray(value)) {
    return (
      <ReactTexty key={column.key}>
        {value?.map((val) => (
          <Tag key={typeof val === 'object' ? val.id : val} colorKey="gray2">
            {getSelectLabel(collection, val, lng, noTranslation, labelKey, valueKey)}
          </Tag>
        ))}
      </ReactTexty>
    )
  } else {
    return returnTextOnly ? (
      getSelectLabel(collection, value, lng, noTranslation, labelKey, valueKey)
    ) : (
      <ReactTexty key={column.key}>
        {getSelectLabel(collection, value, lng, noTranslation, labelKey, valueKey)}
      </ReactTexty>
    )
  }
}

export const getRecipient = (t: (key: string) => string, tableMode: TableModeType) => ({
  key: 'recipient',
  sortable: true,
  dataKey: 'recipient_id',
  labelKey: 'name',
  noTranslation: true,
  cellRenderer: getSelectCellValue,
  optionsKeys: ['organizations'],
  width: 0,
  flexGrow: 0.8,
  headerRenderer: headerRenderer(t, tableMode),
})

export const getSupplier = (t: (key: string) => string, tableMode: TableModeType) => ({
  key: 'supplier',
  sortable: true,
  dataKey: 'supplier_id',
  labelKey: 'name',
  noTranslation: true,
  cellRenderer: getSelectCellValue,
  optionsKeys: ['organizations'],
  width: 0,
  flexGrow: 0.8,
  headerRenderer: headerRenderer(t, tableMode),
})

export const getWarehouse = (t: (key: string) => string, tableMode: TableModeType) => ({
  key: 'warehouse',
  dataKey: 'warehouse_id',
  width: 0,
  flexGrow: 1,
  labelKey: 'name',
  noTranslation: true,
  sortable: true,
  cellRenderer: getSelectCellValue,
  headerRenderer: headerRenderer(t, tableMode),
})

export const getColumns = (
  type: string,
  lng: TLanguages,
  tableMode?: TableModeType,
  t: (key: string) => string = () => '',
) => {
  // TODO: we will need lng later for translations dataKey construction
  // console.log(lng)
  const COLUMNS_CONFIG: Record<string, unknown> = {
    brands: brandsColumnsConfig(lng, t, tableMode),
    categories: categoriesColumnsConfig(lng, t, tableMode),
    users: usersColumnsConfig(t, tableMode),
    organizations: organizationsColumnsConfig(lng, t, tableMode),
    items: itemsColumnsConfig(lng, t, tableMode),
    units: unitsColumnsConfig(lng, t, tableMode),
    attributes: attributesColumnsConfig(lng, t, tableMode),
    warehouses: warehousesColumnsConfig(lng, t, tableMode),
    orders: ordersColumnsConfig(lng, t, tableMode),
    invoices: invoicesColumnsConfig(lng, t, tableMode),
    shipments: shipmentsColumnsConfig(lng, t, tableMode),
    faqs: faqsColumnsConfig(lng, t, tableMode),
    reports: reportsColumnsConfig(lng, t, tableMode),
    pages: pagesColumnsConfig(lng, t, tableMode),
    posts: postsColumnsConfig(lng, t, tableMode),
    inquiries: inquiriesColumnsConfig(lng, t, tableMode),
    campaigns: campaignsColumnsConfig(lng, t, tableMode),
    inventories: inventoriesColumnsConfig(lng, t, tableMode),
    dashboard: dashboardColumnsConfig(lng, t, tableMode),
  }

  return COLUMNS_CONFIG[type]
}
