import { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { NavigateFunction, useLocation, useNavigate } from 'react-router'
import { useTheme } from 'styled-components'
import { ErrorBoundary, Line, Filters } from '@aidsupply/components'
import { useMappedState } from '../../hooks'
import {
  selectAllSystemCollections,
  selectFiltersState,
  selectTableDataCount,
} from '../../redux-saga/selectors'
import { FILTERS_CONFIG } from '../../components/config/filters'
import { filtersAdd, filtersClearAll, filtersRemove } from '../../redux-saga/reducers/filters'
import { TLanguages } from '../../locales'
import { toggleLeftPanelExtended } from '../../redux-saga/reducers/common'

const LeftFiltersPanel = () => {
  const dispatch = useDispatch()
  const theme = useTheme()
  const { t, i18n } = useTranslation(['table', 'forms'])
  const navigate = useNavigate()
  const location = useLocation()
  const { search, pathname } = location

  const system = useMappedState(selectAllSystemCollections)
  const filters = useMappedState(selectFiltersState)
  const itemsCount = useMappedState(selectTableDataCount)

  useEffect(() => {
    if (pathname === '/') {
      dispatch(toggleLeftPanelExtended(false))
    }
  }, [search, pathname])

  return (
    <ErrorBoundary>
      <Line color={theme.color.general.gray1} margin="0" />
      <Filters
        variant="primary"
        buttonProps={{
          uppercase: false,
          withTextShadow: true,
          withLetterSpacing: false,
        }}
        className="filters"
        searchItemsLength={itemsCount}
        config={FILTERS_CONFIG}
        filters={filters}
        fnDispatch={dispatch}
        fnFilterAdd={(
          key: string,
          value: string,
          lng: TLanguages,
          locationSearch: string,
          navigate: NavigateFunction
        ) => dispatch(filtersAdd({ key, value, lng, locationSearch, navigate }))}
        fnFiltersClearAll={(key: string, value: string, lng: TLanguages) =>
          dispatch(filtersClearAll({ key, value, lng, locationSearch: search, navigate }))
        }
        fnFilterRemove={(
          key: string,
          value: string,
          lng: TLanguages,
          locationSearch: string,
          navigate: NavigateFunction
        ) => dispatch(filtersRemove({ key, value, lng, locationSearch, navigate }))}
        fnNavigate={navigate}
        locationSearch={search}
        padding="0"
        systemData={system}
        currentLng={i18n.language}
        fallbackLng="en"
        t={t}
        tagProps={{ colorKey: 'gray3' }}
        withBadge
      />
    </ErrorBoundary>
  )
}

export default LeftFiltersPanel
