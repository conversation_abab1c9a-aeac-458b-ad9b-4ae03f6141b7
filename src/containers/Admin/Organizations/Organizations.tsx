import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import OrganizationsTableCard from './OrganizationsTableCard'

const Organizations = () => {
  return (
    <GridLayout rightPanelComponent={<Outlet context={{}} />}>
      <TableBlockInfiniteScroll
        isMainTable
        tableCardHeight={88}
        TableCardContent={OrganizationsTableCard as unknown as React.JSX.Element}
        iconName='layers'
      />
    </GridLayout>
  )
}

export default Organizations
