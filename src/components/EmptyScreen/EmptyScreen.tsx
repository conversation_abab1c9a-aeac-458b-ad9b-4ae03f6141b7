import styled from 'styled-components'
import { Button, Icon, TextPanel, Typography } from '@aidsupply/components'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import React from 'react'

const StyledEmptyScreen = styled(TextPanel)`
  width: 100%;
  display: flex;
  height: 100%;
  justify-content: center;
  flex-direction: column;
  background: ${({ theme }) => theme.color.general.light};
`

interface IEmptyScreen {
  btnLabelType?: string
  onButtonClick?: () => void
  buttonProps?: Record<string, unknown>
  children?: React.ReactNode
  text?: string
  iconName?: string
  width?: string
  height?: string
  subText?: string
}

const EmptyScreen = ({
  btnLabelType,
  onButtonClick,
  buttonProps,
  children,
  text,
  subText,
  iconName,
  width: iconWidth,
  height: iconHeight,
}: IEmptyScreen) => {
  const { t } = useTranslation('loadingOrNoData')
  const navigate = useNavigate()

  return (
    <>
      <StyledEmptyScreen>
        <Icon name={iconName}
          width={iconWidth}
          height={iconHeight}
        />
        {text &&
          <Typography margin="20px 0 10px" type={subText ? 'h2' : 'h3'} textAlign="center">{text}</Typography>
        }

        {subText &&
          <Typography margin="0 0 30px" type="h4" textAlign="center">{t(subText, { itemType: t(btnLabelType || '') })}</Typography>
        }

        {btnLabelType && (
          <Button
            text={t('addFirstItem', { itemType: t(btnLabelType) })}
            onClick={onButtonClick ? onButtonClick : () => navigate('new')}
            variant="primary"
            style={{ textTransform: 'none' }}
            {...buttonProps}
          />
        )}
        {children}
      </StyledEmptyScreen>
    </>
  )
}

export default EmptyScreen
