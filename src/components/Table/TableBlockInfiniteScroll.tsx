import { useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import { useLocation, useNavigate, createSearchParams, useParams } from 'react-router'
import throttle from 'lodash/throttle'
import queryString from 'query-string'
import { useTheme } from 'styled-components'
import { Icon, Loader, ScreenContext, Table, Typography, usePrevious, VirtualizedList } from '@aidsupply/components'
import { dataFetchWithFacets } from '../../redux-saga/reducers/data'
import { selectAllSystemCollections, selectApiUrlParam, selectAreFiltersChosen, selectDataByType, selectDataTypeObject, selectMainDataInitialized, selectMainDataLoading, selectTableDataCount, selectUpsertInProgress, selectUserDetails } from '../../redux-saga/selectors'
import { useMappedState } from '../../hooks'
import { usePrevWithoutEmptyValuesSave } from '../../hooks/usePrevWithoutEmptyValuesSave'
import { sortObjectToUrlString } from '../../utils/table'
import { GAP, HEADER_HEIGHT, NAVBAR_HEIGHT_LG, NAVBAR_HEIGHT_SM, SCREEN_WIDTH, SEARCH_VALUE_MIN_LENGTH, TABLE_CARD_HEIGHT_INFINITE_SCROLL, TABLE_CARD_PADDING, TABLE_HEADER_HEIGHT, TABLE_ROW_GAP, TABLE_ROW_HEIGHT_INFINITE_SCROLL, TABLE_ROW_HEIGHT_LG, TABLE_ROW_HEIGHT_SM } from '../../constants'
import { TLanguages } from '../../locales'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import { RIGHT_PANEL_CREATE_ROUTES } from '../config'
import { getColumns } from '../config/columns'
import { ISubmenu } from '../NavMenu/config'
import EmptyScreen from '../EmptyScreen/EmptyScreen'
import { useTableModeAndSize } from './hooks/useTableModeAndSize'
import { useSorting } from './hooks/useSorting'
import { useWindowHeight } from './hooks/useWindowHeight'
import TableTitleBlock from './components/TableTitleBlock'
import NavBar from './NavBar'
import { IColumn, ITableBlock } from './TableBlock'
import { StyledContent, StyledLoadingLine } from './styled'

export const TableBlockInfiniteScroll = ({
  className,
  editable,
  emptyScreenChildren,
  isDeletionByState,
  isMainTable = true,
  isReadOnly,
  isTableWithTitle = true,
  TableCardContent,
  isPaginationDisabled,
  navBarProps = {},
  queryParams,
  rowKey,
  selectable,
  tableCardHeight,
  tableTitleChildren,
  textsChosenLng,
  iconName,
  ...rest
}: ITableBlock) => {
  const dispatch = useDispatch()
  const location = useLocation()
  const navigate = useNavigate()
  const {
    t,
    i18n: { language },
  } = useTranslation(['table', 'general'])
  const { width: screenWidth } = useContext(ScreenContext)

  const theme = useTheme()
  const [page, setPage] = useState(0);
  const gridRef = useRef<{
    resetAfterColumnIndex: (index: number) => void
    resetAfterRowIndex: (index: number) => void
  } | null>(null)
  const inProgress = useMappedState(selectMainDataLoading)
  const isDataInitialized = useMappedState(selectMainDataInitialized)
  const typeData = useMappedState(selectDataTypeObject) as ISubmenu
  const type = typeData?.key
  const prevType = usePrevWithoutEmptyValuesSave(type)
  const prevPage = usePrevWithoutEmptyValuesSave(page)
  const data = useMappedState(selectDataByType(type))
  const user = useMappedState(selectUserDetails) as ICurrentUser
  const system = useMappedState(selectAllSystemCollections)
  const selectedApiUrlParam = useMappedState(selectApiUrlParam) as string
  const totalItems = useMappedState(selectTableDataCount)
  const areFiltersChosen = useMappedState(selectAreFiltersChosen)

  const [tableData, setTableData] = useState(data)
  const [maxTableTypeHeight, setMaxTableTypeHeight] = useState(0)
  const [gridColCount, setGridColCount] = useState(0)
  const [maxTableRowsCount, setMaxTableRowsCount] = useState(0)
  const [isEmptyTable, setIsEmptyTable] = useState(false)
  const { sortState, onColumnSort } = useSorting(type)

  const params = useParams()
  const editedItemId = params?.rightPanelId
  const activeRowId = editedItemId
  const prevRowId = usePrevious(activeRowId)
  const searchParsed = queryString.parse(location.search)
  const prevSearchValue = usePrevious(location.search) || ''
  const upsertInProgress = useMappedState(selectUpsertInProgress) as boolean
  const prevUpsertInProgress = usePrevious(upsertInProgress)

  const tableHeaderHeight = TABLE_HEADER_HEIGHT
  const tableRowGap = TABLE_ROW_GAP
  const tableRowHeight = type === 'products' || type === 'inquiry_items' ?  TABLE_ROW_HEIGHT_LG : TABLE_ROW_HEIGHT_SM
  const isPageWithHeader = screenWidth && screenWidth < theme.breakpoints.xl
  const navbarHeight = screenWidth <= SCREEN_WIDTH ? NAVBAR_HEIGHT_LG : NAVBAR_HEIGHT_SM
  const topHeight = (isPageWithHeader ? HEADER_HEIGHT : 0) + (isTableWithTitle ? HEADER_HEIGHT : 0) + navbarHeight
  const isMobile = screenWidth && screenWidth < theme.breakpoints.lg
  const virtualizedListInnerPadding = 150

  useEffect(() => {
    setTableData(data)
  }, [data])

  const { tableMode, onTableResize, getMaxTableHeight, isDropdownSearch, onListResize } = useTableModeAndSize(
    {
      type,
      topHeight,
      gap: GAP,
      gridRef,
    }
  )
  const prevTableMode = usePrevious(tableMode)
  const prevMaxTableRowsCount = usePrevious(maxTableRowsCount)
  const maxTableHeight = getMaxTableHeight(
    tableHeaderHeight,
    tableMode === 'table' ? tableRowHeight : tableCardHeight || tableRowHeight)

  const tableDataWithoutDeleted = isDeletionByState
    ? tableData?.filter((item: Record<string, unknown>) => item.state !== 'deleted')
    : tableData

  useEffect(() => {
    if (tableMode === 'table') {
      setGridColCount(1)
    }
  }, [tableMode])

  const { height: screenHeight, prevHeight: prevScreenHeight } = useWindowHeight(tableMode === 'table' ? tableRowHeight : tableCardHeight, 200)

  useEffect(() => {
    const visibleDataMultiplyByValue = 2
    if (tableMode === 'cards') {
      const cards = Math.floor(((maxTableHeight as number) - TABLE_CARD_PADDING) / (tableCardHeight + GAP)) * gridColCount
      setMaxTableRowsCount(cards * visibleDataMultiplyByValue);
    }
    if (tableMode === 'table') {
      const rows = Math.floor((maxTableHeight as number) / (tableRowHeight + tableRowGap))
      setMaxTableRowsCount(rows * visibleDataMultiplyByValue);
    }
  }, [tableMode, gridColCount, tableRowHeight, tableCardHeight, maxTableHeight])

  useEffect(() => {
    if (prevType !== type || prevTableMode !== tableMode || maxTableRowsCount !== prevMaxTableRowsCount || (upsertInProgress !== prevUpsertInProgress)) {
      dispatch(
        dataFetchWithFacets({
          type,
          locationSearch: location.search,
          sortString: sortObjectToUrlString(sortState),
          pageLimit: maxTableRowsCount as number,
          pageOffset: page * (maxTableRowsCount as number),
          tableMode,
          query: [...(queryParams || [])],
          lng: language as TLanguages,
          apiUrlParam: selectedApiUrlParam,
          isLoadDataByInfiniteScroll: page > 0,
        })
      )
    }
  }, [type, maxTableRowsCount, tableMode, upsertInProgress, prevUpsertInProgress])

  useEffect(() => {
    if (((page !== prevPage && (maxTableRowsCount === prevMaxTableRowsCount)) || prevSearchValue !== location.search) && tableData?.length <= (totalItems as number)) {
      dispatch(
        dataFetchWithFacets({
          type,
          locationSearch: location.search,
          sortString: sortObjectToUrlString(sortState),
          pageLimit: maxTableRowsCount as number,
          pageOffset: page * (maxTableRowsCount as number),
          tableMode,
          query: [...(queryParams || [])],
          lng: language as TLanguages,
          apiUrlParam: selectedApiUrlParam,
          isLoadDataByInfiniteScroll: page > 0 && prevSearchValue === location.search,
        })
      )
    }
  }, [page, totalItems, tableData?.length, maxTableRowsCount, gridColCount, location.search, tableMode])

  useEffect(() => {
    if (prevSearchValue !== location.search || prevTableMode !== tableMode || (upsertInProgress !== prevUpsertInProgress)) {
      setPage(0)
    }
  }, [location.search, tableMode, prevTableMode, upsertInProgress, prevUpsertInProgress])

  useEffect(() => {
    if (screenHeight !== prevScreenHeight) {
      setPage(0)
    }
  }, [screenHeight, prevScreenHeight])

  useEffect(() => {
    const handleScroll = throttle((event: Event) => {
    const target = event.target as HTMLElement
    const height = tableMode === 'table' ? TABLE_ROW_HEIGHT_INFINITE_SCROLL : TABLE_CARD_HEIGHT_INFINITE_SCROLL
    const { scrollTop, scrollHeight, clientHeight } = target
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - height

    if (
      isNearBottom &&
      !inProgress &&
      tableData?.length < (totalItems as number)
    ) {
      setPage((prev) => prev + 1)
    }
  }, 500)

    const container = document.getElementById('scroll-container')
    if (container) {
      container.addEventListener('scroll', handleScroll);

      if (location.search !== prevSearchValue || (upsertInProgress !== prevUpsertInProgress)) {
        container.scrollTo({ top: 0, behavior: 'auto' })
      }
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [inProgress, tableData?.length, totalItems, setPage, location.search, upsertInProgress, prevUpsertInProgress]);

  useEffect(() => {
    if (totalItems) {
      setMaxTableTypeHeight((tableRowHeight || 50) * (totalItems as number))
    }
  }, [totalItems])

  useEffect(() => {
    const isEmptyTable =
    isDataInitialized &&
    !areFiltersChosen &&
    tableDataWithoutDeleted?.length === 0 &&
    !searchParsed?.searchValue &&
    inProgress === false

    setIsEmptyTable(isEmptyTable)
  }, [isDataInitialized, areFiltersChosen, tableDataWithoutDeleted?.length, searchParsed?.searchValue, inProgress])

  const cellProps = { system, lng: language as TLanguages, user, is_read_only: isReadOnly }
  const columnsFromConfig =
    (getColumns(type, language as TLanguages, tableMode, t) as IColumn[]) || ([] as IColumn[])

  const columns = columnsFromConfig?.filter(
    (column) => !column?.getIsHidden || !column?.getIsHidden({ role: user.role || '' })
  )

  const onRowClick = (data: { rowKey: string }) => {
    navigate({
      pathname: data.rowKey ? `${data.rowKey}` : type,
      search: createSearchParams(queryString.stringify(searchParsed)).toString().replace(/%2C/g, ','),
    })
  }

  const onSetGridColCount = (width: number) => {
    setGridColCount(width < 650 ? 1 : width < 1050 ? 2 : 3)
  }

  const isDataInProgress =
    !screenWidth ||
    (((!searchParsed.searchValue && !tableDataWithoutDeleted?.length) || !isDataInitialized) && inProgress)

  const maxCardTableHeight = ((tableCardHeight + GAP) * tableDataWithoutDeleted?.length) / gridColCount

  const getTableComponent = () => {
    if (isDataInProgress) {
      return <Loader size="60px" top={`calc(50% - 30px)`} left={`calc(50% - 30px)`} />
    }

    if (isEmptyTable && !searchParsed.searchValue) {
      return <EmptyScreen btnLabelType={typeData?.labelSingle} iconName={'noLoadingData'} width='300' height='300' text={t('forms:noLoadingData')} subText={t('forms:noLoadingDataSubtext')}>{emptyScreenChildren}</EmptyScreen>
    }

    if (!data?.length && searchParsed.searchValue && searchParsed.searchValue.length >= SEARCH_VALUE_MIN_LENGTH) {
      return <EmptyScreen iconName={'noSearchData'} width='300' height='300' text={t('noResults')}>{emptyScreenChildren}</EmptyScreen>
    }

    switch (tableMode) {
      case 'table': {
        return <Table
        cellProps={cellProps}
        columns={columns}
        data={tableDataWithoutDeleted}
        editable={!isReadOnly && editable}
        isReadOnly={isReadOnly}
        emptyText={t('general:notFound')}
        headerHeight={tableDataWithoutDeleted?.length === 0 ? maxTableHeight : tableHeaderHeight}
        maxHeight={maxTableTypeHeight}
        onColumnSort={onColumnSort}
        onResize={onTableResize}
        onRowClick={onRowClick}
        rowClassName={({ rowData }: { rowData: { status: string; id: number } }) => {
          const classes = []
            if (rowData.status === 'inactive') {
              classes.push('inactive')
            }
            const currentRowId = rowData.id.toString()
            const isHighlightedRow =
            activeRowId && !RIGHT_PANEL_CREATE_ROUTES.includes(activeRowId)
              ? activeRowId === currentRowId
              : // @ts-ignore
              prevRowId?.current === currentRowId

            if (isHighlightedRow) {
              classes.push('activeRow')
            }

              return classes.join(' ')
          }}
        rowHeight={tableRowHeight || 50}
        rowKey={rowKey || 'id'}
        selectable={selectable}
        sortState={sortState}
        textsChosenLng={textsChosenLng}
        {...(tableDataWithoutDeleted?.length === 0 && !type.endsWith('_items')
          ? { containerNotFound: true }
          : {})}
        blockKey={type}
        {...rest}
      />
      }
      case 'cards': {
        return (
          tableDataWithoutDeleted?.length ? <VirtualizedList
            activeRowId={activeRowId}
            cellProps={{ container: { props: { cellProps } } }}
            columns={columns}
            columnCount={gridColCount}
            editedItemId={editedItemId}
            maxHeight={maxCardTableHeight + virtualizedListInnerPadding}
            gridRef={gridRef}
            initialPageNumber={1}
            items={tableDataWithoutDeleted}
            itemsTotalCount={totalItems}
            onItemClick={onRowClick}
            onResize={onListResize}
            onSetColumnCount={onSetGridColCount}
            rowHeight={tableCardHeight}
            rowKey={rowKey || 'id'}
            selectable={selectable}
            sortState={sortState}
            t={t}
            TableCardContent={TableCardContent}
            type={type}
            variant={tableMode === 'cards' && 'grid'}
            {...rest}
          /> : 
        <>
            <Icon name="notFound" width={'70%'} height={'80%'} wrapperWidth={'80%'} wrapperHeight={'50%'} />
            <Typography margin="20px 0" textAlign="center" type="h2">
              {t('general:notFound')}
            </Typography>
          </>
        )
      }
    }
  }

  return (
    <>
      {isTableWithTitle && (
        <TableTitleBlock titleKey={typeData?.label} withMenuIcon iconName={iconName || type}>
          {!isMobile && tableTitleChildren}
        </TableTitleBlock>
      )}
      {!navBarProps.isHidden && isDataInitialized && !isDataInProgress && !isEmptyTable && (
        <NavBar
          isMainTable={isMainTable}
          rowsCount={maxTableRowsCount}
          isDropdownSearch={isDropdownSearch}
          onRowClick={onRowClick}
          tableMode={tableMode}
          isPaginationDisabled={true}
        />
      )}
      <StyledContent
        id="scroll-container"
        maxHeight={maxTableHeight as number}
        className={
          className || (tableDataWithoutDeleted?.length === 0 && tableMode === 'cards')
            ? 'notFoundSearch'
            : ''
        }
      >
        {getTableComponent()}
      </StyledContent>
      {data?.length && !isDataInProgress ? <StyledLoadingLine isLoading={inProgress && page > 0} tableMode={tableMode} /> : null}
    </>
  );
};
