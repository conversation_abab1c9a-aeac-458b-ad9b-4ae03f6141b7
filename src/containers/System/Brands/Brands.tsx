import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import BrandsTableCard from './BrandsTableCard'

const Brands = () => {
  return (
    <GridLayout
      rightPanelComponent={<Outlet context={{}} />}
    >
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={BrandsTableCard as unknown as React.JSX.Element}
        tableCardHeight={86}
        iconName='brand'
      />
    </GridLayout>
  )
}

export default Brands
