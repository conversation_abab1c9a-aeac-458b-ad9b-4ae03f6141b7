import { Input } from '@aidsupply/components'
import { IFormInputProps } from './interfaces'

type InputEvent = React.ChangeEvent<HTMLInputElement>

const FormInput = (props: IFormInputProps) => {
  const {
    field,
    formSubmit,
    isDisabled,
    isHidden,
    isSubmitOnBlur,
    multiline,
    name,
    submitByEnterPressed,
    t,
    updateInput,
    value,
    validateField,
  } = props

  const onChangeInput = (event: InputEvent) => {
    if (field.onChange && validateField && field.key) {
      validateField(field.key, event.target.value)
      field.onChange(event)
    } else if (updateInput) {
      updateInput(
        event.target ? event : { target: { name: name as string, value: event } },
        field.onInputValueChange
      )
    }
  }

  return (
    // @ts-ignore
    <>
      <Input
        {...props}
        type={field.type}
        onBlur={isSubmitOnBlur && formSubmit}
        onEnter={isSubmitOnBlur && !multiline && formSubmit}
        submitByEnterPressed={submitByEnterPressed}
        onChange={onChangeInput}
        multiline={field.multiline || false}
        disabled={isDisabled}
        min={field.min}
        max={field.max}
        step={field.step}
        value={value}
        primaryFocusColor
        iconRightProps={field.iconRightProps}
        iconLeftProps={field.iconLeftProps}
        variant="secondary"
        toolbarOptions={field.toolbarOptions}
      />
      {field.additionToInput}
      {!isHidden &&
        field.addElementToInput &&
        updateInput &&
        field.addElementToInput(updateInput, t, field.disabled)}
    </>
  )
}

export default FormInput
