import { Outlet } from 'react-router'
import GridLayout from '../../Layout/GridLayout'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import InvoicesTableCard from './InvoicesTableCard'

const Invoices = () => {
  return (
    <GridLayout
      isIconRightPanelWide={true}
      rightPanelComponent={
      <>
        <Outlet context={{}} />
      </>
    }
    >
    <TableBlockInfiniteScroll
      TableCardContent={InvoicesTableCard as unknown as React.JSX.Element}
      tableCardHeight={210}
      iconName='fileDollar'
    />
    </GridLayout>
  )
}

export default Invoices
