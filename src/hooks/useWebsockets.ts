import { useEffect } from 'react'
import useWebSocket from 'react-use-websocket'
import { useMappedState } from './index'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { selectDataTypeObject, selectSidebarInitialData, selectUserToken } from '../redux-saga/selectors'
import { popupAlertShow } from '../redux-saga/reducers/common'
import { DATA_TYPE_TO_SINGULAR_FORM } from '../constants'
import { sidebarFetchById, sidebarItemSet } from '../redux-saga/reducers/sideBar'
import { fileParseSuccess } from '../redux-saga/reducers/fileUpload'
import { ISubmenu } from '../components/NavMenu/config'

const WS_BASE_URL = process.env.REACT_APP_API_URL?.replace('https://', 'wss://').replace('http://', 'ws://')

interface ILastWSMessage {
  created_at: string
  notification_type: string
  details: {
    entity_id: number
    entity_type: string
    number: string
  }
  id: number
}

export const useWebsockets = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation(['notifications', 'table'])
  const typeData = useMappedState(selectDataTypeObject) as ISubmenu
  const type = typeData?.key
  const itemInitial = useMappedState(selectSidebarInitialData)
  const token = useMappedState(selectUserToken)
  const socketUrl = `${WS_BASE_URL}/ws/notifications?token=${token}`

  const { lastJsonMessage } = useWebSocket(
    socketUrl,
    {
      // eslint-disable-next-line no-console
      onOpen: () => console.log('opened'),
      shouldReconnect: (_closeEvent) => true,
    },
    !!token
  )

  const lastWSMessage = lastJsonMessage as ILastWSMessage
  useEffect(() => {
    if (lastWSMessage) {
      // @ts-ignore
      if (lastWSMessage.details.message === 'Parsing order items') {
        dispatch(
          popupAlertShow({

            contentKey: t('parsingOrderItems', { number: lastWSMessage.details.number }),
            type: 'success',
            withCloseButton: true,
            iconName: 'info',
            timeout: 5000,
          })
        )
      }

      if (
        lastWSMessage.notification_type === 'document_updated' &&
        DATA_TYPE_TO_SINGULAR_FORM[type] === lastWSMessage.details?.entity_type?.toLowerCase() &&
        lastWSMessage.details.entity_id === itemInitial?.id
      ) {
        dispatch(sidebarFetchById({ id: lastWSMessage.details.entity_id, type, reload: true}))
      }

      if (lastWSMessage.notification_type === 'document_updated') {
        dispatch(
          popupAlertShow({
            contentKey: t('yourOrderUploaded', { number: lastWSMessage.details.number }),
            type: 'success',
            withCloseButton: true,
            iconName: 'checkRounded',
          })
        )
        dispatch(fileParseSuccess({ entity_type: lastWSMessage.details.entity_type }))
        dispatch(sidebarItemSet({ ...itemInitial, is_being_processed: false }))
      }
    }
  }, [lastWSMessage])
}
