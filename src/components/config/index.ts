import { IOptionData } from '../Form/interfaces'

export const MENU_ICONS_BY_TYPES: Record<string, string> = {
  attributes: 'listBulleted',
  banners: 'banners',
  brands: 'brand',
  campaigns: 'moneyBox',
  carriers: 'deliveryTruck',
  categories: 'flowchart',
  contracts: 'contracts',
  organizations: 'layers',
  'my-organization': 'layers',
  enumerations: 'listNumber',
  faqs: 'fileQuestion',
  inquiries: 'fileQuestion',
  invoices: 'fileDollar',
  inventories: 'fileCheckList',
  items: 'warehouses',
  orders: 'fileCheck',
  pages: 'fileCheck',
  platforms: 'platforms',
  shipments: 'deliveryBox',
  'stocks-items': 'deliveryBoxes',
  warehouses: 'warehouse',
  units: 'ruler',
  users: 'users',
  reports: 'fileCheckList',
  posts: 'fileSimple',
}

export type HeaderKeys = 'dataKey' | 'translationKey' | 'invite' | 'topics';
export const RIGHT_PANEL_HEADERS: Record<string, Partial<Record<HeaderKeys, string>>> = {
  brands: {
    dataKey: 'general.name',
  },
  items: {
    dataKey: 'texts.title',
  },
  orders: {
    translationKey: 'orderInfo',
  },
  users: {
    invite: 'inviteUser',
  },
  posts: {
    topics: 'postTopics',
  },
}

export const RIGHT_PANEL_CREATE_ROUTES = ['invite', 'new', 'clone']

export const ORGANIZATION_ROLES: {
  [key: string]: IOptionData
} = {
  recipient: {
    id: 'recipient',
    label: { en: 'Recipient', uk: 'Одержувач' },
  },
  donor: {
    id: 'donor',
    label: { en: 'Donor', uk: 'Донор' },
  },
  merchant: {
    id: 'merchant',
    label: { en: 'Merchant', uk: 'Продавець' },
  },
}

export const PROVIDERS = [
  { id: 'google', label: { en: 'Google', uk: 'Google' } },
  { id: 'facebook', label: { en: 'Facebook', uk: 'Facebook' } },
  { id: 'email', label: { en: 'Email', uk: 'Email' } },
]

export const TAXES_TYPE = [
  {
    id: 'none',
    label: {
      en: 'None',
      uk: 'Відсутній',
    },
  },
  {
    id: 'vat_7',
    label: {
      en: 'VAT 7%',
      uk: 'ПДВ 7%',
    },
  },
  {
    id: 'vat_14',
    label: {
      en: 'VAT 14%',
      uk: 'ПДВ 14%',
    },
  },
  {
    id: 'vat_20',
    label: {
      en: 'VAT 20%',
      uk: 'ПДВ 20%',
    },
  },
]
