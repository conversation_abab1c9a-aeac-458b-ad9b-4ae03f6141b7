import { IFieldsProps, IFormConfig } from '../../../components/Form/interfaces'
import { ICurrentUser, UserRole } from '../../../redux-saga/reducers/user'

export const usersRightPanelConfig: IFormConfig = {
  withTabs: [],
  validationRules: {
    full_name: ['required', 'fullName'],
    email: ['required', 'email'],
    role: ['required'],
    organization_id: ['required'],
  },
  fields: [
    {
      key: 'status',
      label: 'status',
      optionsKeys: ['orgOrUsersStatuses'],
      component: 'dropdown',
      getIsHidden: (formValues?: {[key: string]: unknown, id?: string | number}) => !formValues?.id,
      getOptionsFromFormValues: (formValues: Record<string, unknown>, optionsData: Record<string, unknown>) => {
        if (optionsData.orgOrUsersStatuses) {
          if (formValues.status !== 'pending') {
            return Object.values(optionsData.orgOrUsersStatuses).filter(option => option.id !== 'pending')
          } else {
            return Object.values(optionsData.orgOrUsersStatuses)
          }
        }
      }
    },
    {
      key: 'email',
      label: 'email',
      required: true,
      getIsHidden: (formValues?: Record<string, unknown>) => !!formValues?.id && !!formValues.email,
    },
    {
      key: 'full_name',
      label: 'fullName',
      required: true,
    },
    {
      key: 'role',
      label: 'role',
      optionsKeys: ['userRoles'],
      component: 'dropdown',
      required: true,
      getOptions: (systemData: Record<string, unknown>) => {
        const currentRole = (systemData.user as ICurrentUser)?.role as string
        const allowedAssignRoles: Record<string, UserRole[]> = {
          system: ['administrator', 'organization_owner', 'organization_admin', 'organization_member', 'system'],
          administrator: ['organization_owner', 'organization_admin', 'organization_member', 'administrator'],
          organization_owner: ['organization_admin', 'organization_member', 'organization_owner'],
          organization_admin: ['organization_member', 'organization_admin'],
        }

        if (systemData.userRoles) {
          return Object.values(systemData.userRoles).filter((option) => allowedAssignRoles[currentRole]?.includes(option.id))
        }
      },
      getDisabled: (
        formValues?: Record<string, unknown>,
        optionsData?: Record<string, unknown>,
        initialValues?: Record<string, unknown>
      ) =>
        initialValues?.role === 'system' ||
        (optionsData?.user as ICurrentUser)?.id === formValues?.id
    },
    {
      key: 'organization_id',
      label: 'organization',
      noTranslation: true,
      labelKey: 'name',
      valueKey: 'id',
      optionsKeys: ['organizations'],
      component: 'dropdown',
      isClearable: true,
      required: true, 
      getDisabled: (_formValues: Record<string, unknown>, optionsData: Record<string, unknown>) => {
        const fieldsEditableRoles = ['administrator', 'system']
        const role = (optionsData.user as ICurrentUser)?.role as string
        return !fieldsEditableRoles.includes(role)
      },
    },
  ] as IFieldsProps[]
}
