import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { FilesType } from '../../components/Form/interfaces'

export interface IDraftFile {
  path: string
  preview: string
  relativePath: string
  lastModified: number
  lastModifiedDate: Date
  name: string
  size: number
  type: FilesType
  webkitRelativePath: string
}

export interface IFileUpload {
  errors: Record<string, IDraftFile[]>
  errorLatest: string | null
  files: Record<string, IDraftFile[]>
  inProgress: boolean
  filesInProgress: Record<string, IDraftFile[]>
  filesType: FilesType
}

interface IUploadAvatar {
  fileType: 'image',
  file: File,
  entityId?: number,
  entityType: string,
  fileGroup?: string
}

interface IInputFilesAdd {
  newFiles: IDraftFile[],
  fileGroup: string,
  filesType: FilesType
}

export interface IUploadedFile {
  file_type: FilesType
  id: number
  meta?: {
    file_name_original: string
    file_size: number
    mime_type: string
  }
  url: string
  fileGroup?: string
  filePreviewId?: string
}

const setNewFiles = (oldFiles: Record<string, IDraftFile[]>, newFiles: Record<string, IUploadedFile[]>) => {
  const filteredFiles = Object.keys(newFiles).reduce(
    (acc, fileGroup) => ({
      ...acc,
      [fileGroup]:
        oldFiles[fileGroup]?.filter(
          (file) => !newFiles[fileGroup].some((newFile) => newFile.filePreviewId === file.preview)
        ) || [],
    }),
    {}
  )

  return {
    ...oldFiles,
    ...filteredFiles,
  }
}

export const fileUploadSlice = createSlice({
  name: 'fileUpload',
  initialState:  {
    errors: {},
    errorLatest: null,
    files: {},
    inProgress: false,
    filesInProgress: {},
    filesType: 'photos' as FilesType,
  },
  reducers: {
    uploadAvatar(state: IFileUpload, _action: PayloadAction<IUploadAvatar>) {
      state.inProgress = true
    },
    uploadAvatarSuccess(state: IFileUpload) {
      state.inProgress = false
    },
    uploadAvatarError(state: IFileUpload) {
      state.inProgress = false
    },
    inputFilesAdd(state: IFileUpload, action: PayloadAction<IInputFilesAdd>) {
      const { fileGroup, newFiles, filesType } = action.payload
      state.files[fileGroup] = [...(state.files[fileGroup] || []), ...newFiles]
      state.filesType = filesType
    },
    inputFilesRemove(state: IFileUpload, action: PayloadAction<{id: string,     fileGroup: string}>) {
      const { id, fileGroup } = action.payload;
      state.files = {
        ...state.files,
        [fileGroup]: state.files[fileGroup].filter((file) => file.preview !== id),
      },
      state.filesInProgress = {
        ...state.filesInProgress,
        [fileGroup]: state.filesInProgress[fileGroup]?.filter((fileId) => id !== fileId.preview),
      }
      state.errors = {
        ...state.errors,
        [fileGroup]: state.errors[fileGroup]?.filter((errorFileId) => id !== errorFileId.preview)
      }
    },
    fileUploadSuccess(state: IFileUpload, action: PayloadAction<{files: Record<string, IUploadedFile[]>}>) {
      state.inProgress = false,
      state.files = setNewFiles(state.files, action.payload.files),
      state.filesInProgress = setNewFiles(state.filesInProgress, action.payload.files),
      state.errors = setNewFiles(state.errors, action.payload.files)
    },
    fileUploadFailure(state: IFileUpload, _action: PayloadAction<Record<string, string>>) {
      state.inProgress = false
    },
    deleteAvatar(state: IFileUpload, _action: PayloadAction<{
      entityType: string,
      entityId: number,
    }>) {
      state.inProgress = true
    },
    deleteAvatarSuccess(state: IFileUpload) {
      state.inProgress = false
    },
    deleteAvatarError(state: IFileUpload) {
      state.inProgress = false
    },
    deletePhoto(state: IFileUpload, _action: PayloadAction<{
      entityId: number,
      entityType: string,
      primaryPhotoIdKey: string
    }>) {
      state.inProgress = true;
    },
    deletePhotoSuccess(state: IFileUpload) {
      state.inProgress = false
    },
    deletePhotoError(state: IFileUpload) {
      state.inProgress = false
    },
    fileParse(state: IFileUpload, _action: PayloadAction<{
      entity_type: string,
      file: IDraftFile,
      id: number,
      entityTypeId: string,
      is_replace_existing: boolean
    }>) {
      state.inProgress = true
    },
    fileParseSuccess(state: IFileUpload, action: PayloadAction<{
      entity_type: string
    }>) {
      state.inProgress = false
      state.files = {
        ...state.files,
        [`${action.payload.entity_type}_items`]: [],
      }
    },
    fileParseError(state: IFileUpload, action: PayloadAction<{payload: {
      error: Record<string, unknown> | string,
      entity_type: string
    }}>) {
      state.inProgress = false
      state.files = {
        ...state.files,
        [`${action.payload.payload.entity_type}_items`]: [],
      }
    },
  }
})

export const {
  uploadAvatar,
  uploadAvatarSuccess,
  uploadAvatarError,
  inputFilesAdd,
  inputFilesRemove,
  fileUploadFailure,
  fileUploadSuccess,
  deleteAvatar,
  deleteAvatarSuccess,
  deleteAvatarError,
  deletePhoto,
  deletePhotoSuccess,
  deletePhotoError,
  fileParse,
  fileParseSuccess,
  fileParseError,
} = fileUploadSlice.actions
export default fileUploadSlice.reducer
