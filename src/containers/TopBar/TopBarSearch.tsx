import { Dispatch, SetStateAction, useContext, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Input, ScreenContext } from '@aidsupply/components'
import { StyledSearchField } from './styled'

const TopBarSearch = ({ isFullWidthSearch, handleFullWidthSearch }: {
  isFullWidthSearch: boolean
  handleFullWidthSearch: Dispatch<SetStateAction<boolean>>
}) => {
  const { t } = useTranslation('forms')
  const theme = useTheme()
  const { width: screenWidth } = useContext(ScreenContext) || {}
  const isMobile = screenWidth < theme.breakpoints.md

  const [searchValue, setSearchValue] = useState<string>('')

  const onSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value)
  }

  const onSearchCleanClick = () => {
    setSearchValue('')
  }

  const handleChangeWidth = () => {
    if (isMobile) {
      // TODO: add logic when setIsFullWidthSearch(false)
      handleFullWidthSearch(true)
    }
  }

  return(
    <StyledSearchField isFullWidthSearch={!isMobile || isFullWidthSearch} onClick={handleChangeWidth}>
      <Input
        className="searchInput"
        placeholder={t('search') + '...'}
        iconRightProps={searchValue.length ? {
          name: 'cross',
          onClick: onSearchCleanClick,
          strokeWidth: 1,
          fill: theme.color.general.gray3
        } : null}
        iconLeftProps={{
          name: 'search',
          strokeWidth: 2,
          width: 20,
          height: 20,
          fill: theme.color.general.gray3
        }}
        onChange={onSearchChange}
        value={searchValue}
        withBorder
      />
    </StyledSearchField>
  )
}

export default TopBarSearch
