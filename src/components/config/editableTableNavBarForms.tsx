import { isObjectEmpty } from '@aidsupply/components'
import { IFormConfig, IOptionData } from '../Form/interfaces'

export const EDITABLE_TABLE_NAVBAR_FORM_CONFIG: IFormConfig = {
  'organizations.supervised_organizations': {
    withTabs: [],
    validationRules: { organization: ['required'] },
    isAddOnSelect: true,
    isEmptyTextOnly: true,
    createNewOptions: true,
    headerHeight: 0,
    optionsFromValuesDependencies: ['status'],
    transformAddedResult: (
      resultObj: Record<string, unknown>) => {
      const organization = resultObj.organization as { id: string; name: string }
      return {
        organization_id: organization.id,
        status: resultObj.status || 'pending',
        name: organization.name,
        isDraft: true,
      }
    },
    fields: [
      {
        key: 'organization',
        placeholder: 'organization',
        component: 'dropdown',
        noTranslation: true,
        labelKey: 'name',
        isRequired: true,
        fullWidth: true,
        className: 'fullWidth',
        getOptionsFromFormValues: (_formValues: Record<string, unknown>, optionsData: Record<string, unknown>, mainFormData: Record<string, unknown>) => {
          return (
            optionsData.organizations &&
            Object.values(optionsData.organizations).filter(
              (org) =>
                // @ts-ignore
                !mainFormData.some((row) => row.organization_id === org.id) &&
                // @ts-ignore
                optionsData.user.organization_id !== org.id
            )
          )
        },
      },
    ],
  },
  'orders.order_items': {
    validationRules: { item: ['required'] },
    createNewOptions: false,
    tableRowKey: 'id',
    optionsFromValuesDependencies: ['currency_id', 'state'],
    transformAddedResult: (resultObj: Record<string, unknown>, additionalFormValues: Record<string, unknown>) => {
      if (!resultObj || isObjectEmpty(resultObj)) {
        return
      }
      return {
        // @ts-ignore
        brand_id: resultObj.brand?.id || (resultObj.item as { brand_id: string }).brand_id,
        item_id: (resultObj.item as { id: string }).id,
        currency_id: additionalFormValues.currency_id,
        state: additionalFormValues.state,
        quantity: 1,
      }
    },
    fields: [
      {
        key: 'brand',
        optionsKeys: ['brands'],
        placeholder: 'brand',
        noTranslation: true,
        component: 'dropdown',
        labelKey: 'name',
        fullWidth: true,
        isClearable: true,
        className: 'fullWidth',
        validationTranslationKeys: ['brands', 'items'],
         // @ts-ignore
        onSelectValueChange: (value, setFormValues) => {
           // @ts-ignore
          setFormValues((prev) => ({ ...prev, item: '' }))
        },
         // @ts-ignore
        getOptionsFromFormValues: (formValues, optionsData) => {
          return optionsData.brands && Object.values(optionsData.brands)
        },
      },
      {
        key: 'item',
        placeholder: 'item',
        component: 'dropdown',
        labelKey: 'translations',
        isRequired: true,
        fullWidth: true,
        className: 'fullWidth lastRow',
        validationTranslationKeys: ['brands', 'items'],
         // @ts-ignore
        getOptionsFromFormValues: (formValues, optionsData) => {
          const allItems = optionsData.items && Object.values(optionsData.items)
          return formValues.brand
           // @ts-ignore
            ? allItems.filter((item) => item.brand_id === formValues.brand.id)
            : allItems
        },
      },
    ],
  },
  'shipments.shipment_items': {
    validationRules: { item: ['required'] },
    createNewOptions: false,
    tableRowKey: 'id',
    optionsFromValuesDependencies: ['currency_id', 'order_id'],
    transformAddedResult: (resultObj: Record<string, unknown>, additionalFormValues: Record<string, unknown>) => {
      if (!resultObj || isObjectEmpty(resultObj)) {
        return
      }
      return {
        // @ts-ignore
        brand_id: resultObj.brand?.id || resultObj.item.brand_id,
        // @ts-ignore
        item_id: resultObj.item.id,
        currency_id: additionalFormValues.currency_id,
        quantity: 1,
      }
    },
    fields: [
      {
        key: 'brand',
        optionsKeys: ['brands'],
        placeholder: 'brand',
        noTranslation: true,
        component: 'dropdown',
        labelKey: 'name',
        fullWidth: true,
        isClearable: true,
        className: 'fullWidth',
        validationTranslationKeys: ['brands', 'items'],
         // @ts-ignore
        onSelectValueChange: (value, setFormValues) => {
           // @ts-ignore
          setFormValues((prev) => ({ ...prev, item: '' }))
        },
         // @ts-ignore
        getOptionsFromFormValues: (formValues, optionsData) => {
          return optionsData.brands && Object.values(optionsData.brands)
        },
      },
      {
        key: 'item',
        placeholder: 'item',
        component: 'dropdown',
        labelKey: 'translations',
        isRequired: true,
        fullWidth: true,
        className: 'fullWidth',
        validationTranslationKeys: ['brands', 'items'],
         // @ts-ignore
        getOptionsFromFormValues: (formValues, optionsData) => {
          const allItems = optionsData.items && Object.values(optionsData.items)
          return formValues.brand
           // @ts-ignore
            ? allItems.filter((item) => item.brand_id === formValues.brand.id)
            : allItems
        },
      },
    ],
  },
  'invoices.invoice_items': {
    validationRules: { item: ['required'] },
    createNewOptions: false,
    tableRowKey: 'id',
    optionsFromValuesDependencies: ['currency_id', 'order_id'],
    transformAddedResult: (resultObj: Record<string, unknown>, additionalFormValues: Record<string, unknown>) => {
      if (!resultObj || isObjectEmpty(resultObj)) {
        return
      }
      return {
        // @ts-ignore
        brand_id: resultObj.brand?.id || resultObj.item.brand_id,
        // @ts-ignore
        item_id: resultObj.item.id,
        currency_id: additionalFormValues.currency_id,
        quantity: 1,
      }
    },
    fields: [
      {
        key: 'brand',
        optionsKeys: ['brands'],
        placeholder: 'brand',
        noTranslation: true,
        component: 'dropdown',
        labelKey: 'name',
        fullWidth: true,
        isClearable: true,
        className: 'fullWidth',
        validationTranslationKeys: ['brands', 'items'],
         // @ts-ignore
        onSelectValueChange: (value, setFormValues) => {
           // @ts-ignore
          setFormValues((prev) => ({ ...prev, item: '' }))
        },
         // @ts-ignore
        getOptionsFromFormValues: (formValues, optionsData) => {
          return optionsData.brands && Object.values(optionsData.brands)
        },
      },
      {
        key: 'item',
        placeholder: 'item',
        component: 'dropdown',
        labelKey: 'translations',
        isRequired: true,
        fullWidth: true,
        className: 'fullWidth',
        validationTranslationKeys: ['brands', 'items'],
         // @ts-ignore
        getOptionsFromFormValues: (formValues, optionsData) => {
          const allItems = optionsData.items && Object.values(optionsData.items)
          return formValues.brand
           // @ts-ignore
            ? allItems.filter((item) => item.brand_id === formValues.brand.id)
            : allItems
        },
      },
    ],
  },
  'inquiries.inquiry_items': {
    validationRules: {},
    createNewOptions: false,
    tableRowKey: 'id',
    isDeletionByState: true,
  },
}

export const EDITABLE_TABLE_CHANGING_BLOCKS_OPTIONS = {
  'enumerations.options': {
    getDefaultValue: (data: Record<string, unknown>[], options: IOptionData[]) => {
      if (!data?.length) {
        return
      }
      if (data.some((item) => !!item?.icon)) {
        return options[1]
      }
      if (data.some((item) => !!item?.hex)) {
        return options[2]
      }
    },
    options: [
      {
        id: 'none',
        label: {
          en: 'Name only',
          uk: 'Тільки назва',
        },
      },
      {
        id: 'icon',
        label: {
          en: 'Icon and Name',
          uk: 'Іконка та назва',
        },
      },
      {
        id: 'hex',
        label: {
          en: 'Color and Name',
          uk: 'Колір і назва',
        },
      },
    ],
  },
}
