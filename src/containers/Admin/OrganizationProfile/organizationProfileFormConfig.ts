import { SOCIAL_NETWORKS } from '../../../components/config/forms'
import { IFormConfig, IOptionData, TranslateFn } from '../../../components/Form/interfaces'
import { ORGANIZATION_TYPES } from '../../../data'
import { DEFAULT_LANGUAGE, TLanguages } from '../../../locales'

export const organizationProfileFormConfig: IFormConfig = {
  withTabs: ['description'],
  validationRules: {
    name: ['required'],
    email: ['email'],
    type: ['required'],
    country_id: ['required'],
    year_founded: ['year'],
  },
  fields: {
    generalInfo: [
      {
        key: 'name',
        label: 'name',
        required: true,
      },
      {
        key: 'name_en',
        label: 'nameInEnglish',
      },
      {
        key: 'type',
        label: 'organizationType',
        options: ORGANIZATION_TYPES,
        component: 'dropdown',
        required: true,
        labelKey: 'translations',
        customGetOptionLabel: (option: IOptionData | Record<string, unknown>, _t: TranslateFn, lng?: TLanguages) => {
          // @ts-ignore
          return option?.label[lng] || option?.label[DEFAULT_LANGUAGE] || ''
        },
      },
      {
        key: 'year_founded',
        label: 'yearFounded',
        type: 'number',
        hideButtons: true,
      },
      {
        key: 'reg_number',
        label: 'registrationNumber',
      },
      {
        key: 'country_id',
        label: 'country',
        component: 'dropdown',
        optionsKeys: ['countries'],
        labelKey: 'translations',
        required: true,
      },
      {
        key: 'region_id',
        label: 'region',
        component: 'dropdown',
        isClearable: true,
        optionsKeys: ['country_subdivisions'],
        labelKey: 'translations',
      },
      {
        key: 'city',
        label: 'city',
      },
      {
        key: 'website',
        label: 'organizationWebsite',
        placeholder: 'https://domain.com',
      },
      {
        key: 'email',
        label: 'organizationEmail',
        placeholder: '<EMAIL>',
      },
      {
        key: 'video_url',
        label: 'introductionVideo',
        className: 'fullWidth',
      },
      { key: 'is_public', label: 'publicProfile', component: 'checkbox' },
    ],
    translations: [
      {
        key: 'description',
        label: 'description',
        type: 'richText',
      },
    ],
    fundraisingAccounts: [
      {
        key: 'wayforpay_url',
        label: 'wayforpayURL',
        placeholder: 'Profile URL',
      },
      {
        key: 'paypal_url',
        label: 'PayPal',
        placeholder: 'Profile URL',
      },
      {
        key: 'patreon_url',
        label: 'Patreon',
        placeholder: 'Profile URL',
      },
      {
        key: 'buymeacoffee_url',
        label: 'buyMeACoffee',
        placeholder: 'Profile URL',
      },
      {
        key: 'venmo_url',
        label: 'Venmo',
        placeholder: 'Profile URL',
      },
    ],
    socialProfiles: SOCIAL_NETWORKS,
  },
}
