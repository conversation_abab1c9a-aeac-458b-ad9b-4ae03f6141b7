{"family": "%FAMILY%", "executionRoleArn": "arn:aws:iam::895045525719:role/ecsTaskExecutionRole", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "1024", "networkMode": "awsvpc", "containerDefinitions": [{"image": "895045525719.dkr.ecr.eu-west-2.amazonaws.com/%IMAGE%:%VERSION%", "name": "%FAMILY%", "essential": true, "portMappings": [{"hostPort": 3000, "protocol": "tcp", "containerPort": 3000}], "environment": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/%FAMILY%", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs"}}}]}