import { RECIPIENT, STATE } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'

export const inquiriesRightPanelConfig = (mode: 'create' | 'edit'): IFormConfig => {
  return {
    withTabs: [''],
    validationRules: { recipient_id: ['required'] },
    fields: {
      noTitle: [
        {
          ...STATE,
        },
        {...RECIPIENT},
      ],
      ...(mode === 'edit' && {
        inquiry_items: [],
        activity: [],
        notes: [],
      })
    }
  }
}
