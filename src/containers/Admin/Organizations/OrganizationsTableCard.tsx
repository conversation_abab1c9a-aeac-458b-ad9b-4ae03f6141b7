import { Userpic, Typography, Tag, Tooltip } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { useMappedState } from '../../../hooks'
import { IMAGEKIT_URL } from '../../../constants'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'
import ReactTexty from '../../../lib/react-texty'
import { TableCardStyled } from '../../../components/Table/styled'
import { countryCodeToFlagEmoji, getAllItemsString, getFirstChart } from '../../../utils/common'
import { Status } from '../../../commonTypes'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { ORG_OR_USER_STATUSES_ICONS } from '../../../components/config/table'
import { Styled<PERSON><PERSON>r<PERSON><PERSON><PERSON>, Styled<PERSON>ontentWrapper, StyledCountryFlag, StyledCountryType } from './styled'

interface IOrganizationsTableCard {
  id: number
  logo_url: string
  name: string
  rating: number
  state: string
  type: string
  address: string
  status: Status
  country_id: string
}
const OrganizationsTableCard = ({ data, className }: { data: IOrganizationsTableCard, className?: string}) => {
  const { id, logo_url, name, type, status, country_id } = data

  const theme = useTheme()
  const system = useMappedState(selectAllSystemCollections)
  const { t } = useTranslation('table')

  const organizationRoles = Object.keys(system.organizationRoles || {})

  const countryCode = system?.countries?.[country_id as unknown as string]?.alpha2code
  const countryFlag = countryCode && countryCodeToFlagEmoji(countryCode)

  const allRolesString = getAllItemsString(organizationRoles, t)

  return (
    <TableCardStyled key={id} padding='20px 17px 20px 20px' className={className}>
      <Userpic
        height="46px"
        width="46px"
        imagekitUrl={IMAGEKIT_URL}
        src={logo_url}
        borderColor={theme.color.general.gray1}
        fullName={name}
      />
      <StyledContainerWrapper>
        <StyledContentWrapper>
            <Typography as={ReactTexty} type="h4" margin="0 0 2px 0">
              {name}
            </Typography>
            <IconWithTooltip text={t(`statuses:${status}`)} iconName={ORG_OR_USER_STATUSES_ICONS[status as Status]} arrowPosition='right'/>
        </StyledContentWrapper>
        <StyledContentWrapper>
          <StyledCountryType>
            <StyledCountryFlag>
              {countryFlag}
            </StyledCountryFlag>
            <Typography as={ReactTexty} type="button2" fontWeight="400">
              {type}
            </Typography>
          </StyledCountryType>
          <Tooltip
            textColor={theme.color.general.light}
            text={allRolesString}
            right="5px"
            arrowPosition="right"
            padding="15px 15px"
          >
            {organizationRoles.map((role) => (
              <Tag key={role} text={getFirstChart(role, t)} />
            ))}
          </Tooltip>
        </StyledContentWrapper>
      </StyledContainerWrapper>
    </TableCardStyled>
  )
}

export default OrganizationsTableCard
