import { object } from 'dot-object'
import { updateFormValuesChanged } from '../../../redux-saga/reducers/forms'
import { updateModificationCodeInSystem } from '../../../redux-saga/reducers/data'
import { Dispatch } from 'redux'
import { IColumn } from '../TableBlock'
import { IBrandItem } from '../../../commonInterfaces'

const useEditableTable = ({
  dispatch,
  isDeletionByState,
  rowKey,
  selectedRows,
  setTableData,
  tableData,
  type,
  updateTableRows,
}: {
  dispatch: Dispatch
  isDeletionByState?: boolean
  rowKey: string
  selectedRows: string[]
  setTableData?: (data: IBrandItem[]) => void
  tableData: any[]
  type: string
  updateTableRows?: () => (data: IBrandItem[], isEqualToInitial?: boolean) => void
}) => {
  if (!updateTableRows) {
    return {}
  }

  const onAddIconImage = () => {}

  const onBlurCellInput = (e: React.FocusEvent<HTMLInputElement>, column: IColumn) => {
    const { name, value } = e.target

    if (type === 'items.modifications') {
      dispatch(updateFormValuesChanged({ key: 'modifications', valuesChanged: [{ id: name.split('.')[0], code: value }] }))
      dispatch(updateModificationCodeInSystem({id: name.split('.')[0], code: value}))
    } else {
      const isNumber = column.type === 'number'
      const checkedValue = isNumber ? +value : value
      const deletedItems = tableData.filter((item) => item.state === 'deleted')

      const updatedData = tableData
        .filter((item) => item.state !== 'deleted')
        .map((row, index) => {
          if (index === +name.split('.')[0]) {
            const newValueObj = {
              [column.dataKey]: checkedValue,
            }
            return { ...row, ...newValueObj }
          } else {
            return row
          }
        })
      const isEqualToInitial = false

      const newData: IBrandItem[] = [...updatedData, ...deletedItems]

      updateTableRows()(newData, isEqualToInitial)
      setTableData?.(newData.map((item) => object(item) as IBrandItem))
    }
  }

  const onDeleteClick = () => {
    const filteredData = tableData.filter((row) => !selectedRows.includes(row[rowKey || 'id']))
    const mappedData = tableData.map((row) =>
      selectedRows.includes(row.id || 'id') ? { ...row, state: 'deleted' } : row
    )

    if (isDeletionByState) {
      updateTableRows()(mappedData, false)
    } else {
      updateTableRows()(filteredData)
    }
  }

  return { onAddIconImage, onBlurCellInput, onDeleteClick }
}

export default useEditableTable
