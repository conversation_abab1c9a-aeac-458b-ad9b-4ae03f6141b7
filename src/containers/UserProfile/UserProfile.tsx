import { useState } from 'react'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Button, Typography } from '@aidsupply/components'
import Form from '../../components/Form'
import { useIsPasswordShown } from '../../hooks/useIsPasswordShown'
import LanguageSelect from '../../components/LanguageSelect'
import ProfilePicture from '../../components/ProfilePicture'
import { useMappedState } from '../../hooks'
import { selectUserDetails } from '../../redux-saga/selectors'
import { ICurrentUser, signOut, userResetPassword, userUpdateName } from '../../redux-saga/reducers/user'
import CustomPasswordChecklist from '../IntroScreen/components/CustomPasswordChecklist'
import { userProfileConfig } from './config'
import { StyledUserProfile } from './styled'
import { useNavigate } from 'react-router'

const passwordsInitialValues = {
  password: '',
  new_password: '',
}

const UserProfile = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { t } = useTranslation(['forms', 'general', 'table'])
  const theme = useTheme()

  const [password, setPassword] = useState<string>('')
  const [newPassword, setNewPassword] = useState<string>('')
  const [isSubmitPressed, setIsSubmitPressed] = useState(false)
  const [isPasswordValid, setIsPasswordValid] = useState<boolean | undefined>(undefined)

  const formDataInitial = useMappedState(selectUserDetails) as ICurrentUser

  const onLogout = () => {
    dispatch(signOut())
  }

  const onPasswordChange = () => {
    dispatch(userResetPassword({ password_new: newPassword, password_old: password, navigate }))
  }

  const onFormSubmit = (values: { full_name: string }) => {
    dispatch(
      userUpdateName({
        id: formDataInitial.id,
        type: 'usersName',
        requestBody: values,
      })
    )
  }

  const isPassButtonDisabled = !(password && newPassword && password !== newPassword && isPasswordValid)

  const { type: passwordType, iconRightProps: passwordIconRight } = useIsPasswordShown()
  const { type: newPasswordType, iconRightProps: newPasswordIconRight } = useIsPasswordShown()

  return (
    <StyledUserProfile>
      <ProfilePicture />
      <Form
        initialValues={formDataInitial}
        validationRules={{
          full_name: ['required'],
        }}
        fields={[{ ...userProfileConfig[2] }, { ...userProfileConfig[3] }]}
        className="rowsForm"
        onSubmit={onFormSubmit as unknown as (values: Record<string, unknown>) => void}
        isSubmitOnBlur
        type="users"
      />
      <LanguageSelect isIntroScreensSelect theme={theme} isFullSize label={t('table:systemLanguage')} minWidth="100%" />
      <Typography type="h4" color={theme.color.general.gray4} text={t('forms:changePassword')} />
      <Form
        className="rowsForm passForm"
        initialValues={passwordsInitialValues}
        type="usersPassword"
        validationRules={{
          password: [
            {
              type: 'requiredIfFieldsNotEmpty',
              fields: ['password'],
              values: { password },
            },
          ],
          newPassword: [
            {
              type: 'requiredIfFieldsNotEmpty',
              fields: ['new_password'],
              values: { newPassword },
            },
            { type: 'password', isPasswordValid: !newPassword || isPasswordValid },
          ],
        }}
        fields={[
          {
            ...userProfileConfig[0],
            onInputValueChange: (value: string | number | null) => setPassword(value?.toString() || ''),
            type: passwordType,
            iconRightProps: passwordIconRight,
          },
          {
            ...userProfileConfig[1],
            onInputValueChange: (value: string | number | null) => setNewPassword(value?.toString() || ''),
            type: newPasswordType,
            iconRightProps: newPasswordIconRight,
          },
        ]}
        onSubmit={onPasswordChange}
        getFormButtons={(props: any) => (
          <Button
            {...props}
            variant="bordered"
            type="submit"
            className="submitFormButton"
            onClick={() => setIsSubmitPressed(true)}
            disabled={isPassButtonDisabled}
            uppercase={false}
          >
            {t('general:update')}
          </Button>
        )}
      />
      {newPassword && (
        <CustomPasswordChecklist
          password={newPassword}
          setIsPasswordValid={setIsPasswordValid}
          className={isSubmitPressed && newPassword && !isPasswordValid ? 'passError' : ''}
        />
      )}
      <Button className="logoutButton" iconName="logout" onClick={onLogout} variant="bordered">
        {t('Logout')}
      </Button>
    </StyledUserProfile>
  )
}

export default UserProfile
