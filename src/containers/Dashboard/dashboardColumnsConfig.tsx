import { TLanguages } from '../../locales'
import { TableModeType } from '../../redux-saga/reducers/data'
import { getName } from '../../utils/columns'
import { headerRenderer } from '../../utils/table'
import { changeZerosToText } from './Dashboard'

export const dashboardColumnsConfig = (
  _lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType
) => {
  return [
    {
      key: 'index',
      cellRenderer: ({ rowIndex }: { rowIndex: number }) => rowIndex + 1,
    },
    { ...getName(t, tableMode as TableModeType), dataKey: 'organization_name', key: 'organization'
    },
    {
      key: 'weight',
      // sortable: true,
      dataKey: 'total_weight',
      width: 0,
      flexGrow: 0.3,
      cellRenderer: ({ cellData }: { cellData: number }) => changeZerosToText(cellData),
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'saved_co2_kg',
      // sortable: true,
      dataKey: 'saved_co2_kg',
      width: 0,
      flexGrow: 0.5,
      cellRenderer: ({ cellData }: { cellData: number }) => changeZerosToText(cellData),
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'saved_water_kg',
      // sortable: true,
      dataKey: 'saved_water_kg',
      width: 0,
      flexGrow: 0.5,
      cellRenderer: ({ cellData }: { cellData: number }) => changeZerosToText(cellData),
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'items_count',
      // sortable: true,
      dataKey: 'items_count',
      width: 0,
      flexGrow: 0.3,
      // cellRenderer: ({ cellData }) => `${cellData}${t('kg')}`,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'recipients_count',
      // sortable: true,
      dataKey: 'recipients_count',
      width: 0,
      flexGrow: 0.3,
      // cellRenderer: ({ cellData }) => `${cellData}${t('kg')}`,
      headerRenderer: headerRenderer(t, tableMode),
    },
  ]
}
