import { useTranslation } from 'react-i18next'
import { Link as RouterLink } from 'react-router'

import { Icon, Typography, UiLink } from '@aidsupply/components'
import { INTRO_PAGES_IMG } from '../../constants'
import IntroScreenWrapper from '../IntroScreen/IntroScreenWrapper'
import { useTheme } from 'styled-components'
import { StyledContainer } from './styled'

const PasswordRecoverySent = () => {
  const { t } = useTranslation('signIn')
  const theme = useTheme()

  return (
    <IntroScreenWrapper
      type="passwordRecoverySent"
      theme={theme}
      imageUrl={INTRO_PAGES_IMG}
      text={t('checkYourMailbox')}
    >
      <Icon name="checkMailbox" margin="30px 0 0" />
      <div className="inscriptionWrapper">
        <Typography
          type="body2"
          color={theme.color.general.dark}
          margin="20px 0 0 0"
          className="inline"
          text={t('passRecoverSentText')}
        />
      </div>
      <StyledContainer>
        <Typography type="body1">{t('backTo')}</Typography>
        <Typography type="button1">
          <UiLink Link={RouterLink} to="/signin">
            {t('toSignIn')}
          </UiLink>
        </Typography>
      </StyledContainer>
    </IntroScreenWrapper>
  )
}

export default PasswordRecoverySent
