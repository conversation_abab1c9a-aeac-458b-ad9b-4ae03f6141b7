import React from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router'
import { useTheme } from 'styled-components'
import { ExpansionPanel, Label } from '@aidsupply/components'
import { UserRole } from '../../redux-saga/reducers/user'
import { isAccessAllowed } from '../../utils/roles'
import { IEntities, ISubmenu } from './config'
import MenuItem from './MenuItem'
import { StyledMenuItems } from './styled'

interface IMenuGroup {
  parentEntity: IEntities
  entitiesArr: ISubmenu[]
  children?: React.ReactNode
  isActiveParent: boolean
  isExtended: boolean
  isMobile: boolean
  textColor?: string
  onClick?: () => void
  role: UserRole
  withExpansionPanel: boolean
}

const MenuGroup = ({
  parentEntity,
  entitiesArr,
  children,
  isActiveParent,
  isExtended,
  isMobile,
  textColor,
  onClick,
  role,
  withExpansionPanel,
}: IMenuGroup) => {
  const { t } = useTranslation('menu')
  const theme = useTheme()
  const { pathname } = useLocation()

  const allowedSubmenuEntities = (entitiesArr || []).filter((entity) =>
    isAccessAllowed(entity?.accessLevel, role)
  )

  const getGroupList = () => {
    if (!allowedSubmenuEntities) return null

    const isActiveKey = (key: string) =>
      pathname && pathname !== '/' ? pathname.includes(`/${key.toLowerCase()}`) : key === 'dashboard'

    return (
      <StyledMenuItems
        onClick={onClick}
        textColor={textColor}
      >
        {allowedSubmenuEntities.map((entity: ISubmenu) => {
          const { key, route, iconName, iconProps, label, icon } = entity

          return (
            <MenuItem
              iconName={iconName || icon}
              iconProps={iconProps as Record<string, string>}
              key={key}
              text={label}
              route={route}
              isActive={isActiveKey(key)}
              isExtended={isExtended}
              isMobile={isMobile}
            />
          )
        })}
      </StyledMenuItems>
    )
  }
  const iconFill = isActiveParent ?
    theme.color.general.light :
    theme.color.general.dark

  return (
    <>
      {withExpansionPanel ? (
        <ExpansionPanel
          headerIconRightProps={{ fill: iconFill, strokeWidth: 1.4 }}
          className={isActiveParent ? 'active' : ''}
          isHidden={!parentEntity}
          header={
            parentEntity && (
              <MenuItem
                iconName={parentEntity.iconName}
                iconProps={parentEntity.iconProps as Record<string, string>}
                key={parentEntity.key}
                text={parentEntity?.label as string}
                isExtended={isExtended}
                isMobile={isMobile}
                className="parentMenuItem"
                isActiveParent={isActiveParent}
              />
            )
          }
        >
          {getGroupList()}
        </ExpansionPanel>
      ) : (
        <>
          {isExtended && parentEntity && <Label text={t(parentEntity.label as string)} />}
          {getGroupList()}
        </>
      )}

      {children}
    </>
  )
}

export default MenuGroup
