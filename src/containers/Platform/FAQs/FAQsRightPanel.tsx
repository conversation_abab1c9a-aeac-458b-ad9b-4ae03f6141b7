import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router'
import { object } from 'dot-object'
import merge from 'deepmerge'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { useMappedState } from '../../../hooks'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import { IFAQ } from '../../../commonInterfaces'
import { StateType } from '../../../components/Form/interfaces'
import { ITranslations } from '../../../locales'
import { faqsRightPanelConfig } from './faqsRightPanelConfig'

const FAQsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const itemInitial = useMappedState(selectSidebarInitialData)

  const onSubmit = (formValuesChanged: IFAQ) => {
    const requestBody: IFAQ = { ...object(formValuesChanged) }
    const { translations, state, organization_id } = requestBody

    if (state && typeof state === 'object' && 'id' in state) {
      requestBody.state = (state as {id: string}).id as StateType
    }

    if (organization_id && typeof organization_id === 'object' && 'id' in organization_id) {
      requestBody.organization_id = (organization_id as {id: number}).id as number
    }

    if (translations) {
      requestBody.translations = merge((itemInitial?.translations as ITranslations), translations)
      requestBody.name = translations.en
    }

    const dataToSend = {
      id: itemInitial?.id,
      requestBody,
      type: 'faqs',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm onSubmit={onSubmit} formConfig={faqsRightPanelConfig} customType="faqs" />
}

export default FAQsRightPanel
