import { useDispatch } from 'react-redux'
import { useMappedState } from '../../hooks'
import { useTranslation } from 'react-i18next'
import { Outlet } from 'react-router'
import { getUserAccessLevel } from '../../utils/roles'
import { DASHBOARD_CARDS } from '../../components/NavMenu/config'
import { useContext, useEffect, useState } from 'react'
import { dataFetchWithFacets } from '../../redux-saga/reducers/data'
import dayjs from 'dayjs'
import clsx from 'clsx'
import { TLanguages } from '../../locales'
import GridLayout from '../Layout/GridLayout'
import { Button, Dropdown, FlexRow, Icon, ScreenContext, Table, Typography } from '@aidsupply/components'
import { useTheme } from 'styled-components'
import { StyledCard, StyledChartHeader, StyledDashboardCards, StyledDashboardWrapper, StyledPanel } from './styled'
import { MENU_ICONS_BY_TYPES } from '../../components/config'
import {
  CartesianGrid,
  Legend,
  LineChart,
  XAxis,
  YAxis,
  Line,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Tooltip,
} from 'recharts'
import { selectDashboard, selectRole } from '../../redux-saga/selectors'
import DatePicker from '../../components/DatePicker/DatePicker'
import { getColumns } from '../../components/config/columns'
import { getTableSummary } from '../../utils/tableSummary'

const COLORS = ['#74748014', '#FFBB28', '#FF8042', '#0088FE', '#00C49F', 'orchid', 'tomato']

const RADIAN = Math.PI / 180

export const changeZerosToText = (number: number) => {
  const numberStringified = number.toString()

  if (numberStringified.endsWith('000000')) {
    return `${numberStringified.replace('000000', '')}M`
  }

  if (number > 1000 && number < 1000000) {
    if (numberStringified.endsWith('000')) {
      return `${numberStringified.replace('000', '')}K`
    }
    if (numberStringified.endsWith('00')) {
      return (number / 1000).toString() + 'K'
    }
  }
  if (
    number > 1000000 &&
    (numberStringified.endsWith('00000') ||
      numberStringified.endsWith('0000') ||
      numberStringified.endsWith('000'))
  ) {
    return (number / 1000000).toString() + 'M'
  }
  return numberStringified
}

// @ts-ignore
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
  const radius =
    percent * 100 < 5
      ? innerRadius + (outerRadius + innerRadius) * 1.15
      : innerRadius + (outerRadius - innerRadius) * 0.6
  const x = cx + radius * Math.cos(-midAngle * RADIAN)
  const y = cy + radius * Math.sin(-midAngle * RADIAN)

  return (
    <text x={x} y={y} fill="black" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  )
}

const getTodayISODate = () => {
  const today = new Date()
  today.setUTCHours(0, 0, 0, 0)
  return today.toISOString()
}

const getWeekAgoISO = () => {
  const today = new Date()
  today.setUTCHours(0, 0, 0, 0)
  const weekAgo = new Date(today)
  weekAgo.setUTCDate(today.getUTCDate() - 7)
  return weekAgo.toISOString()
}

const Dashboard = () => {
  const dispatch = useDispatch()
  const dashboard = useMappedState(selectDashboard)
  const role = useMappedState(selectRole)
  const { t, i18n } = useTranslation('menu')
  const theme = useTheme()

  // @ts-ignore
  const userAccessLevel = getUserAccessLevel(role)
  const cards = DASHBOARD_CARDS.filter((card) => card.accessLevel >= userAccessLevel)

  const [isChartMenuOpen, setIsChartMenuOpen] = useState(false)
  const [chartSelected, setChartSelected] = useState('saved_water_co2_dynamics') // recipients_stats, saved_co2_dynamics, saved_water_dynamics

  const { width, xl } = useContext(ScreenContext)
  const isTablet =  width && width <= xl

  const [dateRange, setDateRange] = useState([
    new Date(getWeekAgoISO()),
    new Date(getTodayISODate()),
  ])
  const [startDate, endDate] = dateRange

  useEffect(() => {
    if (startDate && endDate) {
      dispatch(
        dataFetchWithFacets({
          type: 'dashboard',
          query: [
            // @ts-ignore
            { key: 'date_from', value: dayjs(startDate).format('YYYY-MM-DDTHH:mm:ss.SSS') },
            // @ts-ignore
            { key: 'date_to', value: dayjs(endDate).format('YYYY-MM-DDTHH:mm:ss.SSS') },
          ],
          // locationSearch: !prevType && history.location.search,
          lng: i18n.language as TLanguages,
          // apiUrlParam: selectedApiUrlParam,
        })
      )
    }
  }, [startDate, endDate])

  const pieData =
    // @ts-ignore
    dashboard?.categories_stats && Array.isArray(dashboard.categories_stats)
       // @ts-ignore
      ? dashboard.categories_stats.map((categoryInfo) => ({
          name: categoryInfo.category_group || 'Unknown',
          value: categoryInfo.total_items,
        }))
      : []

  if (!dashboard) {
    return null
  }

  const onSelectDate = (update: [Date, Date]) => {
    const start = update[0]
      ? dayjs(update[0]).set('hour', 0).set('minute',
        // @ts-ignore
        0).set('second', 0).millisecond(0).$d
      : null
    const end = update[1]
      ? dayjs(update[1]).set('hour', 23).set('minute',
        // @ts-ignore
        59).set('second', 59).millisecond(0).$d
      : null
    setDateRange([start, end])
  }

  // @ts-ignore
  const mainChartData = [...dashboard?.daily_stats]?.sort((a, b) => new Date(a.date) - new Date(b.date))

  const summaryData =
  // @ts-ignore
  dashboard?.recipients_stats && getTableSummary('dashboard', t, dashboard?.recipients_stats, {})

  return (
    <GridLayout rightPanelComponent={<Outlet />}>
      <StyledDashboardWrapper>
        <FlexRow justifyContent={'space-between'}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Icon name="dashboard" />
            <Typography type="h3" color="#8E8E93" margin="0 0 0 10px">
              {t('reports')}
            </Typography>
            <DatePicker
              selectsRange={true}
              startDate={startDate}
              endDate={endDate}
              setDateRange={setDateRange}
              onChange={(update: [Date, Date]) => onSelectDate(update)}
              currLang={i18n.language}
              hasSelect
              theme={theme}
              t={t}
              startPeriod="thisWeek"
              dateRange={dateRange}
              withIcon
            />
          </div>
          {/* <Button
            // onClick={() => dispatch(toggleFiltersPanel())}
            variant="bordered"
            iconLeftProps={{ name: 'hamburgerMenu3', stroke: theme.color.general.gray5, height: 20 }}
            size="small"
            className="filters"
            text={t('filters')}
            uppercase={false}
          /> */}
          {/* <Button
            // onClick={() => dispatch(toggleFiltersPanel())}
            variant="primary"
            iconLeftProps={{ name: 'download', stroke: theme.color.general.light, height: 16 }}
            size="small"
            className="download"
            text={t('downloadReport')}
            uppercase={false}
          /> */}
        </FlexRow>
        <StyledDashboardCards>
          {cards?.map((value) => {
            // @ts-ignore
            const valueObject = dashboard?.stats?.[value.key]

            if (!valueObject) {
              return null
            }

            // function clsx(arg0: string, arg1: string | boolean): string | undefined {
            //   throw new Error('Function not implemented.')
            // }

            return (
              // <div className="gridItem" key={value.label}>
              <StyledCard
                percent={valueObject.dynamics_percentage}
                key={value.label}
                // as={Link}
                // to={value.src}
                // onMouseOver={() => setCardHovered(value.label)}
                // onMouseOut={() => setCardHovered('')}
                className={dashboard ? '' : 'inProgress'}
              >
                <Icon
                  borderRadius="6px"
                  fill={theme.color.primary.main}
                  wrapperColor={theme.color.primary.lightest}
                  wrapperHeight={44}
                  wrapperWidth={44}
                  width={16}
                  height={16}
                  name={value.iconName || MENU_ICONS_BY_TYPES[value.key]}
                />

                <div>
                  <Typography type="body1">
                    {t(value.label)}
                    {value.key === 'weight' ? `, ${t('kg')}` : ''} (
                    {valueObject.current_period_quantity ? '+' : ''}
                    {valueObject.current_period_quantity || 0})
                  </Typography>
                  <FlexRow alignItems="end">
                    <Typography type="h2" className="number" fontSize="26px">
                      {dashboard ? valueObject.total || valueObject.total_quantity || '0' : 'Load'}
                    </Typography>
                    <span className={clsx('percentage', !valueObject.dynamics_percentage && 'zero')}>
                      {valueObject.dynamics_percentage > 0 && (
                        <svg width="16" height="17" viewBox="0 0 16 17" fill="none">
                          <path
                            d="M10.6665 10.351L10.6038 5.89606M10.6038 5.89606L6.14886 5.83331M10.6038 5.89606L5.33317 11.1666"
                            stroke="#36B37E"
                            strokeWidth="1.4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      )}
                      {valueObject.dynamics_percentage < 0 && (
                        <svg width="17" height="17" viewBox="0 0 17 17" fill="none">
                          <path
                            d="M6.48235 11.1667L10.9373 11.1039M10.9373 11.1039L11 6.64904M10.9373 11.1039L5.66667 5.83335"
                            stroke="#FFAB00"
                            strokeWidth="1.4"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      )}
                      <span>{valueObject.dynamics_percentage || 0}%</span>
                    </span>
                  </FlexRow>
                </div>
              </StyledCard>
            )
          })}
        </StyledDashboardCards>
        <FlexRow margin="20px 0 0 0" gap="20px" className="charts" style={{ flexDirection: isTablet ? 'column' : 'row' }}>
          {/* @ts-ignore */}
          {(!!dashboard.recipients_stats?.length || !!mainChartData?.length) && (
            <StyledPanel className="main">
              <StyledChartHeader>
                <Dropdown
                  MenuButton={Button}
                  buttonProps={{
                    text: t(chartSelected),
                    size: 'small',
                    withIcon: true,
                    iconRightProps: {
                      name: 'chevronDown',
                      fill: 'black',
                      width: 10,
                      height: 10,
                    },
                    // onClick: toggleActions,
                  }}
                  onItemClick={(id: string) => setChartSelected(id)}
                  openDirection="right"
                  show={isChartMenuOpen}
                  onToggle={(isOpened: boolean) => setIsChartMenuOpen(isOpened)}
                  title={<Typography>chartSelected</Typography>}
                  dropdownItems={[
                    { id: 'saved_water_co2_dynamics', label: t('saved_water_co2_dynamics') },
                    { id: 'recipients_stats', label: t('recipients_stats') },
                  ]}
                />
                <div className="disclaimer">1L = 1kg</div>
              </StyledChartHeader>

              {/* @ts-ignore */}
              {chartSelected === 'recipients_stats' && dashboard.recipients_stats.length && (
                <>
                  <Table
                    className="recipientsStats"
                    rowKey="id"
                    blockKey=""
                    headerHeight={50}
                    maxHeight={500}
                    rowHeight={50}
                    // @ts-ignore
                    data={dashboard?.recipients_stats}
                    summaryData={summaryData}
                    columns={getColumns('dashboard', 'uk', 'table', t)}
                  ></Table>
                  {/* <TableSummary summaryData={summaryData} headerHeight={48} /> */}
                </>
              )}

              {chartSelected === 'saved_water_co2_dynamics' && (
                <ResponsiveContainer debounce={500} key="chartSelected">
                  <LineChart
                    includeHidden
                    // @ts-ignore
                    width="100%"
                    height={400}
                    data={mainChartData}
                    margin={{ top: 20, right: 0, left: -15, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      type="category"
                      allowDuplicatedCategory={false}
                      includeHidden
                      ticks={
                        mainChartData
                          ?.map((item) => item.date)
                          ?.filter(
                            (item, index) =>
                              dayjs(item).month() !== dayjs(mainChartData?.[index - 1]?.date).month()
                          ) || []
                      }
                      tickFormatter={(value) => {
                        return dayjs(value).format('MMMM')
                      }}
                    />
                    {/* @ts-ignore */}
                    <YAxis tickFormatter={(value) => (value ? changeZerosToText(value, t) : 0)} />
                    <Tooltip
                      formatter={(value, name) => {
                        // @ts-ignore
                        return [value, t(name)]
                      }}
                    />
                    <Legend formatter={(value) => t(value)} />
                    <Line type="monotone" dataKey="saved_co2_kg" stroke="#8884d8" />
                    <Line type="monotone" dataKey="saved_water_kg" stroke="#82ca9d" />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </StyledPanel>
          )}
          {!!pieData.length && (
            <StyledPanel className="pie">
              <div className="panelTitle">{t('productCategories')}</div>
              <ResponsiveContainer width="100%" height="80%" debounce={500} key="pie">
                <PieChart margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    // @ts-ignore
                    label={renderCustomizedLabel}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {/* @ts-ignore */}
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
              <FlexRow flexWrap="wrap" gap="10px" className="legend">
                {COLORS.map((color, i) =>
                  pieData[i] ? (
                    <FlexRow key={color}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <rect
                          x="8.00049"
                          y="2"
                          width="8"
                          height="8"
                          rx="2"
                          transform="rotate(45 8.00049 2)"
                          fill={color}
                        />
                      </svg>
                      <Typography text={t(pieData[i].name)} />
                    </FlexRow>
                  ) : null
                )}
              </FlexRow>
            </StyledPanel>
          )}
        </FlexRow>
      </StyledDashboardWrapper>
    </GridLayout>
  )
}

export default Dashboard
