import styled from 'styled-components'

export const StyledContentWrapper = styled.div`{
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}`

export const StyledContainerWrapper = styled.div`{
  display: flex;
  justify-content: space-between;
  width: 100%;
}`

export const StyledContainer = styled.div`{
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px;
  align-items: center;
}`

export const StyledBorderedContainerWrapper = styled.div`{
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  border-radius: 10px;
  flex-direction: column;
  margin: 6px 0 0 0;

  .verticalLine {
    width: 1px;
    height: 40px;
    background-color: ${({ theme }) => theme.color.general.gray2};
  }
}`

export const StyledContentLocationWrapper = styled.div`{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  gap: 10px;
  width: 100%;
  // border-bottom: 1px solid ${({ theme }) => theme.color.general.gray2};
}`
