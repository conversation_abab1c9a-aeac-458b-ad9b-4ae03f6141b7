import { useTranslation } from 'react-i18next'
import { Userpic, Typography, Tag, Tooltip } from '@aidsupply/components'
import { useTheme } from 'styled-components'
import ReactTexty from '../../../lib/react-texty/ReactTexty'
import { TableCardStyled } from '../../../components/Table/styled'
import { getRole } from '../../../utils/columns'
import { Status } from '../../../commonTypes'
import { USER_ROLES } from '../../../components/NavMenu/config'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { ORG_OR_USER_STATUSES_ICONS } from '../../../components/config/table'
import { StyledContainerWrapper, StyledContentWrapper, StyledUserCardName } from './styled'

interface IUsersTableCard {
  id: string
  full_name: string
  status: Status
  organization_name: string
  role: string
  profile_pic: string
}

const UsersTableCard = ({data, className}: {data: IUsersTableCard, className?: string}) => {
  const { id, full_name, status, organization_name, role, profile_pic } = data || {}

  const theme = useTheme()
  const { t } = useTranslation(['table', 'statuses'])

  return (
      <TableCardStyled key={id} className={className}>
        {profile_pic || full_name ? <Userpic
          height="46px"
          width="46px"
          fullName={full_name}
          backgroundColor={theme.color.general.gray4}
          color={theme.color.general.light}
          src={profile_pic}
        /> : <StyledUserCardName/>}
        <StyledContainerWrapper>
        <Typography as={ReactTexty} type="h4">
          {full_name}
        </Typography>
        <StyledContentWrapper>
          <Tooltip
            textColor={theme.color.general.light}
            text={USER_ROLES[role]?.label?.['en']}
            left="5px"
            arrowPosition="left"
            padding="15px 15px"
            position="bottom"
          >
            <Tag key={id} text={getRole(role)} backgroundColor={theme.color.primary.main}/>{' '}
          </Tooltip>
          <Typography as={ReactTexty}
            type="button2"
            fontWeight="400"
          >
            {role !== 'administrator' && role !== 'system' && organization_name}
          </Typography>
        </StyledContentWrapper>
        </StyledContainerWrapper>
        <div>
        <IconWithTooltip text={t(`statuses:${status}`)} iconName={ORG_OR_USER_STATUSES_ICONS[status as Status]} arrowPosition='right'/>
      </div>
    </TableCardStyled>
  )
}

export default UsersTableCard
