import { useSelector } from 'react-redux'
import { ReactNode } from 'react'
import { TFunction } from 'i18next'

import { Button } from '@aidsupply/components'

import { StyledFormButtons } from './styled'
import { useMappedState } from '../../hooks'
import {
  selectFormValuesChangedByType,
  selectFormValuesWhole,
  selectIsCurrentFilesInDraft,
} from '../../redux-saga/selectors.js'
import { IStore } from '../../configureStore.ts'
import { BreakpointsType } from '../../commonTypes.ts'

const FormButtons = ({
  buttonsAreSticky,
  checkErrorsAndSubmit,
  currentBreakpoint,
  customIsDraft,
  // customSetIsDraft,
  formReset,
  getFormButtons,
  isClone,
  inProgress,
  isReadOnly,
  t,
  type,
  withActions,
}: {
  buttonsAreSticky?: boolean
  checkErrorsAndSubmit: Function
  currentBreakpoint: BreakpointsType
  customIsDraft?: boolean
  formReset: Function
  getFormButtons?: (formValues: boolean | Record<string, unknown>) => ReactNode
  isClone: boolean
  inProgress?: boolean
  isReadOnly?: boolean
  t: TFunction
  type: string
  withActions?: boolean
}) => {
  const reduxFormValuesChanged = useMappedState(selectFormValuesChangedByType(type))
  const reduxFormValues = useSelector((state: IStore) => selectFormValuesWhole(state, type))
  const isCurrentFilesInDraft = useMappedState(selectIsCurrentFilesInDraft)
  const isDraft =
    customIsDraft ||
    (reduxFormValuesChanged && Object.keys(reduxFormValuesChanged).length > 0) ||
    isCurrentFilesInDraft ||
    isClone

  const formSubmit = (e: Event) => {
    if (e?.preventDefault) {
      e.preventDefault()
    }

    if (isReadOnly) {
      return
    }
    checkErrorsAndSubmit(e, reduxFormValuesChanged || {}, reduxFormValues)
  }

  return (
    <>
      {getFormButtons && getFormButtons({ isDraft, disabled: inProgress, onClick: formSubmit })}
      {!isReadOnly && withActions && isDraft && (
        <StyledFormButtons currentBreakpoint={currentBreakpoint} className={buttonsAreSticky ? 'sticky' : ''}>
          <div className={buttonsAreSticky ? 'stickyButtonsWrapper' : ''}>
            <Button variant="bordered" onClick={formReset} disabled={inProgress}>
              {t('Discard')}
            </Button>
            <Button fullWidth type="submit" variant="primary" disabled={inProgress} onClick={formSubmit}>
              {t('Save')}
            </Button>
          </div>
        </StyledFormButtons>
      )}
    </>
  )
}

export default FormButtons
