import { customAlphabet } from 'nanoid'
import { alphanumeric } from 'nanoid-dictionary'
import { IFormConfig, UpdateInput, TranslateFn } from '../../../components/Form/interfaces'
import { StyledAdditionToInputLink } from '../../../components/Form/styled'

export const itemsRightPanelConfig = (): IFormConfig => {
  return {
    validationRules: {
      category: ['required'],
      sku: [
        'required',
        {
          type: 'keyInListAbsent',
          customRuleName: 'modelAlreadyExists',
          withSuccess: true,
          dataForValidation: {
            listKey: 'items',
            filterListBy: 'brand.id',
            searchKey: 'sku',
          },
        },
      ],
      brand_id: ['required'],
      category_id: ['required'],
      'translations.en': ['required'],
    },
    withTabs: ['translations', 'description'],
    fields: {
      noTitle: [
        {
          key: 'state',
          label: 'state',
          optionsKeys: ['states'],
          component: 'dropdown',
        },
      ],
      general: [
        {
          key: 'sku',
          label: 'model',
          disabledWhenNotEmpty: true,
          addElementToInput: (updateInput: UpdateInput, t: TranslateFn, disabled?: boolean) => (
            <StyledAdditionToInputLink
              disabled={disabled}
              onClick={() =>
                updateInput({
                  target: { name: 'sku', value: customAlphabet(alphanumeric, 6)() },
                })
              }
              text={t('generateModelNumber')}
            />
          ),
        },
        {
          key: 'brand_id',
          label: 'brand',
          optionsKeys: ['brands'],
          required: true,
          noTranslation: true,
          component: 'dropdown',
          labelKey: 'name',
        },
        {
          key: 'category_id',
          label: 'category',
          required: true,
          component: 'dropdown',
          optionsKeys: ['categories'],
          labelKey: 'translations',
        },
        {
          key: 'weight',
          label: 'weight',
          type: 'number',
          hideButtons: true,
          min: 0,
        },
        {
          key: 'weight_unit_id',
          label: 'weightUnit',
          labelKey: 'translations',
          getOptions: (systemObject: Record<string, unknown>) =>
            systemObject.units &&
            Object.values(systemObject.units).filter(
              (unit: { magnitude: string }) => unit.magnitude === 'mass'
            ),
          component: 'dropdown',
        },
      ],
      translations: [
        {
          key: 'translations',
          label: 'title',
          placeholder: 'Your item title',
          required: true,
        },
        {
          key: 'description',
          label: 'description',
          type: 'richText',
        },
      ],
      photos: [],
    },
  }
}
