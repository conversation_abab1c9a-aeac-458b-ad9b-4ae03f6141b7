import { getWarehouse } from '../../../components/config/columns'
import { getSupplier } from '../../../components/config/columns'
import { getRecipient } from '../../../components/config/columns'
import { getDate, getSelectCellValue } from '../../../components/config/columns'
import { TLanguages } from '../../../locales'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { TableModeType } from '../../../redux-saga/reducers/data'

export const shipmentsColumnsConfig = (
  _lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => {
  return [
    getNameByMode({
      t,
      dataKey: 'shipmentNumber',
      option: null,
      tableMode: tableMode || 'table',
    }),
    getDate(t, tableMode),
    {
      key: 'orderNumber',
      sortable: true,
      noTranslation: true,
      dataKey: 'order_id',
      labelKey: 'number',
      optionsKeys: ['orders'],
      width: 0,
      flexGrow: 1,
      cellRenderer: getSelectCellValue,
      headerRenderer: headerRenderer(t, tableMode),
    },
    getRecipient(t, tableMode || 'table'),
    getSupplier(t, tableMode || 'table'),
    getWarehouse(t, tableMode || 'table'),
  ]
}
