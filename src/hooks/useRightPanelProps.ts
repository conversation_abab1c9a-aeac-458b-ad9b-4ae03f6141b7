import { useLocation, useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'

const useRightPanelProps = (currentDocumentNumber?: string) => {
  const { pathname } = useLocation()
  const navigate = useNavigate()
  const theme = useTheme()
  const { t } = useTranslation('forms')

  const titleRules = [
    { condition: () => pathname.includes('new'), title: t('create') },
    { condition: () => pathname.includes('comments'), title: t('comments') },
    { condition: () => pathname.includes('add'), title: t('addInventory') },
    {
      condition: () => pathname.includes('clone') && pathname.includes('order') && currentDocumentNumber,
      title: t('creationFromOrder', { number: currentDocumentNumber }),
    },
    {
      condition: () => pathname.includes('clone') && pathname.includes('shipment') && currentDocumentNumber,
      title: t('createFromShipment', { number: currentDocumentNumber }),
    },
    {
      condition: () => pathname.includes('clone') && pathname.includes('invoice') && currentDocumentNumber,
      title: t('createFromInvoice', { number: currentDocumentNumber }),
    },
    {
      condition: () => pathname.includes('clone') && pathname.includes('inquiries') && currentDocumentNumber,
      title: t('createFromInquiry', { number: currentDocumentNumber }),
    },
    {
      condition: () => pathname.includes('clone') && pathname.includes('inventory') && currentDocumentNumber,
      title: t('createFromInventory', { number: currentDocumentNumber }),
    },
    {
      condition: () => (pathname.includes('clone') || pathname.includes('order')) && !currentDocumentNumber,
      title: t('edit'),
    },
  ]
  
  const title = titleRules.find(rule => rule.condition())?.title

  const handleGoBack = () => navigate(-1)

  const needsBackIcon = title === t('comments') || pathname.includes('add')
  const backIconProps = needsBackIcon
    ? {
        withBackIcon: true,
        backIconProps: {
          width: 24,
          height: 24,
          wrapperWidth: 32,
          wrapperHeight: 32,
          name: 'arrowLeft',
          fill: theme.color.general.gray4,
          strokeWidth: 1.4,
          onClick: handleGoBack,
        },
      }
    : {}

  return {
    title,
    titleProps: {
      style: {
        fontSize: '18px',
        fontWeight: 700,
      },
    },
    ...backIconProps
  }
}

export default useRightPanelProps
