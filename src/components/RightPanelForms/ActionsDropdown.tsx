import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { useLocation, useNavigate } from 'react-router'
import { Label, Icon, Button } from '@aidsupply/components'
import { IOrder } from '../../commonInterfaces'
import { instanceCreate, sidebarItemClone } from '../../redux-saga/reducers/sideBar'
import { selectUserDetails } from '../../redux-saga/selectors'
import { useMappedState } from '../../hooks'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import { StyledActions } from './styled'

const ActionsDropdown = ({ selectedItem, type }: { selectedItem: IOrder, type: string }) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const theme = useTheme()
  const { search } = useLocation()
  const { t } = useTranslation(['table'])
  const details = useMappedState(selectUserDetails) as ICurrentUser

  const handleActionClick = (action: string) => {
    if (!selectedItem) return
    
    const { id, recipient_id, supplier_id, warehouse_id, currency_id, order_items } = selectedItem

    const requestBody = { order_id: id, status: 'new', state: 'drafted' }

    if (action === 'clone') {
      navigate({ pathname: '../clone', search })
      dispatch(sidebarItemClone({ id: selectedItem.id as number, type }))
      return
    }
    if (action === 'invoice') {
      const invoiceRequestBody = {
        ...requestBody,
        payer_id: recipient_id,
        status: 'pending',
        ...(currency_id && { currency_id }),
        issuer_id: details.organization_id,
        invoice_items: order_items?.map((item) => ({
          item_id: item.item_id,
          order_item_id: item.id,
          quantity: item.quantity,
          state: item.state || 'drafted',
          price: item.price || 0,
          tax: item.tax || 0,
          total: item.total || 0,
        })) || []
      }

      dispatch(instanceCreate({ type: 'invoices', requestBody: invoiceRequestBody}))
    }

    if (action === 'shipment') {
      const shipmentRequestBody = {
        ...requestBody,
        recipient_id: recipient_id,
        supplier_id: supplier_id,
        warehouse_id: warehouse_id,
        status: 'pending',
        shipment_items: order_items?.map((item) => ({
          item_id: item.item_id,
          order_item_id: item.id,
          quantity: item.quantity,
          state: item.state || 'drafted',
        }))
      }
  
      dispatch(instanceCreate({ type: 'shipments', requestBody: shipmentRequestBody }))
    }
  }

  const dropdownItems = [
    {
      id: 'header',
      label: (
        <Label
          text={t('selectAction')}
          type="uppercase"
          fontSize="10px"
          fontWeight={600}
          color="rgba(60, 60, 67, 0.6)"
        />
      ),
      disabled: true,
    },
    {
      id: 'clone',
      label: (
        <>
          <Icon name="linkSign" style={{ transform: 'rotate(45deg)', marginRight: '5px' }} />
          <Label text={t('clone')} />
        </>
      ),
    },
    ...(selectedItem?.order_items && selectedItem?.state !== 'deleted'
      ? [
          {
            id: 'invoice',
            label: (
              <>
                <Icon name="currency" style={{ marginRight: '5px' }} />
                <Label text={t('createInvoice')} />
              </>
            ),
          },
          {
            id: 'shipment',
            label: (
              <>
                <Icon name="deliveryBoxes" style={{ marginRight: '5px' }} />
                <Label text={t('createShipment')} />
              </>
            ),
          },
        ]
      : []),
  ]

  return (
    <StyledActions
      MenuButton={Button}
      buttonProps={{
        text: t('actions'),
        variant: 'primary',
        uppercase: false,
        iconRightProps: {
          name: 'chevronLeft',
          className: 'iconChevron',
          stroke: theme.color.general.light,
          width: 20,
          height: 20,
        },
      }}
      dropdownItems={dropdownItems}
      onItemClick={(id: string) => handleActionClick(id)}
      openDirection="left"
    />
  )
}

export default ActionsDropdown
