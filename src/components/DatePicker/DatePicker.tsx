import clsx from 'clsx'
import { uk } from 'date-fns/locale/uk'
import { useState } from 'react'
import Calendar, { CalendarContainer, registerLocale } from 'react-datepicker'

registerLocale('uk', uk)

import { useEffect } from 'react'
import { Input, Select } from '@aidsupply/components'
import { StyledDatePicker } from './styled'
import { rangeDate, rangeOption } from '../../utils/datePickerRangeOptions'

// @ts-ignore
const DatePicker = (props) => {
  // const [startDate, setStartDate] = useState(new Date())
  const MyContainer = ({ className, children }: { className: string, children: React.ReactNode }) => {
    return (
      <CalendarContainer className={className}>
        <div className="myContainer">
          <Select
            options={props.selectOptions || rangeOption(props.t)}
            fullWidth
            valueKey="id"
            labelKey="period"
            variant="primary"
            withoutValidation
            // @ts-ignore
            value={{ id: selectValue.id, period: props.t(selectValue.period) }}
            onChange={onSelectChange}
            className="datePickerSelect"
            iconLeftProps={{ name: 'calendar', fill: props.theme.color.general.light }}
            t={props.t}
          />
          {children}
        </div>
      </CalendarContainer>
    )
  }

  const isSameRange =
    // @ts-ignore
    props?.dateRange?.[0]?.toISOString() === rangeDate[props.startPeriod]?.[0]?.toISOString() &&
    // @ts-ignore
    props?.dateRange?.[1]?.toISOString() === rangeDate[props.startPeriod]?.[1]?.toISOString()

  const [selectValue, setSelectValue] = useState(
    props.startPeriod ? { id: 'thisWeek', period: props.t('thisWeek') } : ''
  )
  useEffect(() => {
    setSelectValue(isSameRange ? { id: 'thisWeek', period: props.t('thisWeek') } : '')
  }, [isSameRange])

  const onSelectChange = ({ id, period }: { id: string, period: string }) => {
    // @ts-ignore
    props.setDateRange(rangeDate[id])
    setSelectValue({ id, period })
  }

  return (
    <StyledDatePicker className={clsx(props.isClearable && 'isClearable', 'datePickerContainer')}>
      <Calendar
        {...props}
        selected={props.value}
        onChange={props.onChange}
        dateFormat="dd/MM/yyyy"
        // onChange={(date) => setStartDate(date)}
        minDate={props.minDate}
        maxDate={props.maxDate}
        showDisabledMonthNavigation
        isClearable={props.isClearable}
        customInput={
          props.withIcon && (
            <Input
              iconRightProps={{ name: 'chevronDown', width: 10, height: 10, fill: 'black' }}
              {...props}
            />
          )
        }
        locale={props.currLang || 'en'}
        calendarStartDay={1} //to start from Monday
        calendarContainer={props.hasSelect && MyContainer}
        placeholderText={props.placeholder}
      />
    </StyledDatePicker>
  )
}

export default DatePicker
