import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import FAQsTableCard from './FAQsTableCard'

const FAQs = () => {
  return (
    <GridLayout
      rightPanelComponent={
        <>
          <Outlet context={{}} />
        </>
      }
    >
      <TableBlockInfiniteScroll
        TableCardContent={FAQsTableCard as unknown as React.JSX.Element}
        tableCardHeight={90}
        iconName='fileQuestion'
      />
    </GridLayout>
  )
}

export default FAQs
