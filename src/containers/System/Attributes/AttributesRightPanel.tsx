import merge from 'deepmerge'
import { useNavigate } from 'react-router'
import { object } from 'dot-object'
import { useDispatch } from 'react-redux'
import { isObject } from '@aidsupply/components'
import { slugify } from 'transliteration'
import { useMappedState } from '../../../hooks'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { attributesRightPanelConfig } from './attributesRightPanelConfig'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import { ITranslations } from '../../../locales'

interface IAttributesRightPanel {
  attribute_type?: string
  id?: number 
  is_characteristic?: boolean
  is_photo_group?: boolean
  name?: string
  translations?: ITranslations
  [key: string]: unknown
}

const AttributesRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const isReadOnly = false

  const attributesInitial = useMappedState(selectSidebarInitialData)

  const onFormSubmit = (formValuesChanged: IAttributesRightPanel) => {
    const requestBody: IAttributesRightPanel = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key) => {
      const value = requestBody[key]
      if (isObject(value as string | number | Record<string, unknown>) && (value as Record<string, unknown>).id) {
        requestBody[key] = (value as Record<string, unknown>).id
      }
    })

    if (formValuesChanged.name) {
      requestBody.slug = slugify(formValuesChanged.name)
    }

    if (requestBody.translations) {
      requestBody.translations = merge((attributesInitial?.translations as ITranslations), (requestBody.translations as ITranslations))
    }

    const dataToSend = {
      id: attributesInitial?.id,
      requestBody,
      type: 'attributes',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }
  return <EditForm isReadOnly={isReadOnly} onSubmit={onFormSubmit} formData={attributesRightPanelConfig()}/>
}

export default AttributesRightPanel
