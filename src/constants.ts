export const INTRO_PAGES_IMG =
  'https://goodzyk-crm.s3.eu-west-2.amazonaws.com/platforms/6228aa462daeed00093fa50b/images/intro_pages.jpg'

export const IMAGEKIT_URL = `https://ik.imagekit.io/${process.env.REACT_APP_IMAGEKIT_ID}`

export const AIDSUPPLY_MINI_LOGO_DARK_LINK = `${IMAGEKIT_URL}/_public/miniLogoDark.png/tr:w-100,h-100?ik-sdk-version=react-1.1.1`

export const AIDSUPPLY_MINI_LOGO_LIGHT_LINK = `${IMAGEKIT_URL}/_public/miniLogoLight.png/tr:w-200,h-200?ik-sdk-version=react-1.1.1`

export const AIDSUPPLY_LOGO_LINK = `${IMAGEKIT_URL}/_public/textLogo.png/tr:w-520,h-100?ik-sdk-version=react-1.1.1`

export const UNITS_PIECES_ID = '55081b1be4b0896e3115b865'

export const HEADER_HEIGHT = 52
export const TOP_BAR_HEIGHT = 56
export const TOP_BAR_HEIGHT_XL = 60
export const TOP_BAR_BORDER_BOTTOM = 1
export const GAP = 20
export const TABLE_ROW_HEIGHT_LG = 355
export const TABLE_ROW_HEIGHT_SM = 44
export const TABLE_HEADER_HEIGHT = 44
export const TABLE_ROW_GAP = 4
export const TABLE_CARD_PADDING = 20
export const NAVBAR_HEIGHT_LG = 123
export const NAVBAR_HEIGHT_SM = 66
export const SCREEN_WIDTH = 415
export const TABLE_ROW_HEIGHT_INFINITE_SCROLL = 150
export const TABLE_CARD_HEIGHT_INFINITE_SCROLL = 250
export const SEARCH_VALUE_MIN_LENGTH = 2

export const TABLE_WIDTH_LG = 850
export const TABLE_WIDTH_SM = 474

export const COLLAPSED_FORM_BLOCKS = ['admin', 'comments', 'info']

export const MIN_RIGHT_PANEL_WIDTH = '280px'

export const NAV_MENU_WIDTH = {
  desktop: { extended: '254px', condensed: '60px' },
  mobile: { extended: '100%', condensed: '100%' },
}

export const FILE_TYPES = {
  images: {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/*': ['.ico'],
    'image/svg+xml': ['.svg'],
    'image/webp': ['.webp'],
  },
  files: {
    'application/pdf': ['.pdf'],
    'application/vnd.ms-excel': ['.xls', '.xlsx'],
    'application/msword': ['.doc', '.docx'],
  },
}

export const FILE_TYPES_ICONS = {
  'application/pdf': 'pdfFileType',
  'text/plain': 'txtFileType',
  'application/vnd.ms-excel': 'xlsFileType',
  'application/msword': 'docFileType',
}

export const DATA_TYPE_TO_SINGULAR_FORM: Record<string, string> = {
  attributes: 'attribute',
  banners: 'banner',
  brands: 'brand',
  carriers: 'carrier',
  categories: 'category',
  clients: 'client',
  countries: 'country',
  currencies: 'currency',
  enumerations: 'enumeration',
  inquiries: 'inquiry',
  invoices: 'invoice',
  items: 'item',
  inventories: 'inventory',
  orders: 'order',
  organizations: 'organization',
  pages: 'page',
  platforms: 'platform',
  posts: 'post',
  reports: 'report',
  roles: 'role',
  shipments: 'shipment',
  'stock-items': 'stock-item',
  stocks: 'stock',
  warehouses: 'warehouse',
  modifications: 'modification',
  units: 'unit',
  users: 'user',
  campaigns: 'campaign',
}

export const STOCKS_ITEM_DRAFTED_ERROR =
  'Internal error: [item_oid=64a2c94e40d913f1ebb52c95 in [items] has state [drafted].  Only [posted] allowed.]'
