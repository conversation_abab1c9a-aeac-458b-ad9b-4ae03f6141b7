import { IPopupAlert } from '../redux-saga/reducers/common'

export const buildPopupAlerts = (popupAlerts: IPopupAlert[], data: IPopupAlert) => {
  const updatedPopupAlerts = popupAlerts
  const MAX_SIZE_ALERTS = 3
  const { id, type, content, timeout = 3000, stacked, contentKey } = data

  if (stacked) {
    const similarItemIndex = popupAlerts.findIndex(
      (alert) => type === alert.type && content === alert.content
    )

    if (similarItemIndex !== -1) {
      updatedPopupAlerts.splice(similarItemIndex, 1)
    }
  }

  const newAlert = {
    timeout,
    id: id || Date.now(),
    type,
    content,
    contentKey,
  }

  if (updatedPopupAlerts.length === MAX_SIZE_ALERTS) {
    const result = [newAlert, ...updatedPopupAlerts].slice(-MAX_SIZE_ALERTS);
    return result
  }

  return [...updatedPopupAlerts, newAlert]
}
