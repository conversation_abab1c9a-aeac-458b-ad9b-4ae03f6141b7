import dayjs from 'dayjs'
import { IFieldsProps, IOptionData, TranslateFn } from '../Form/interfaces'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import { QuillOption } from '../../commonTypes'

export const FIELDS_TO_EXCLUDE_WHEN_CLONING = [
  'meta',
  'system',
  '_id',
  'user',
  'id',
  'dateCreated',
  'dateModified',
  'number',
  'status',
  'state',
  'platform',
  'platformId',
  'updated_at',
  'created_at',
  'comments',
  'open_demands',
  'closed_demands',
  'in_progress_demands',
  'comment',
  'qty_cancelled',
  'qty_completed',
  'qty_in_progress',
  'qty_items_cancelled',
  'qty_items_completed',
  'qty_items_in_progress',
  'qty_items_new',
  'qty_new',
  'name',
  'title',
  'email',
  'translations',
  'description',
]

export const COUNTRY: IFieldsProps = {
  key: 'country_id',
  label: 'country',
  labelKey: 'translations',
  required: true,
  optionsKeys: ['countries'],
  component: 'dropdown',
}

export const STATE: IFieldsProps = {
  key: 'state',
  label: 'state',
  optionsKeys: ['states'],
  component: 'dropdown',
}

export const SUPPLIER: IFieldsProps = {
  key: 'supplier_id',
  label: 'supplier',
  labelKey: 'name',
  optionsKeys: ['organizations'],
  noTranslation: true,
  component: 'dropdown',
  required: true,
  getOptionsFromFormValues: (_formValues: Record<string, unknown>, optionsData?: Record<string, unknown>) => {
    return (
      (optionsData?.organizations &&
      Object.values(optionsData.organizations).filter((org) => org.state === 'posted')) as Record<string, unknown>
    )
  },
}

export const WAREHOUSE: IFieldsProps = {
  key: 'warehouse_id',
  label: 'warehouse',
  optionsKeys: ['warehouses'],
  noTranslation: true,
  component: 'dropdown',
  labelKey: 'name'
}

export const RECIPIENT: IFieldsProps = {
  key: 'recipient_id',
  label: 'recipient',
  labelKey: 'name',
  optionsKeys: ['organizations'],
  noTranslation: true,
  component: 'dropdown',
  required: true,
  getOptionsFromFormValues: (_formValues: Record<string, unknown>, optionsData?: Record<string, unknown>) => {
    return (
      (optionsData?.organizations &&
      Object.values(optionsData.organizations).filter(
        (org) => org.state === 'posted'
      )) as Record<string, unknown>
    )
  }
}

export const CURRENCY: IFieldsProps = {
  key: 'currency_id',
  label: 'currency',
  labelKey: 'code',
  optionsKeys: ['currencies'],
  noTranslation: true,
  required: true,
  component: 'dropdown',
}

export const INQUIRY_WITH_DATE: IFieldsProps = {
  key: 'inquiry_id',
  label: 'inquiry',
  optionsKeys: ['inquiries'],
  noTranslation: true,
  labelKey: 'number',
  component: 'dropdown',
  isClearable: true,
  customGetOptionLabel: (option: Record<string, unknown> | IOptionData, t: TranslateFn) => {
    // @ts-ignore
    return `№ ${option?.number || option.id} ${t('from')} ${
      // @ts-ignore
      option.created_at ? dayjs(option.created_at as string)?.format('DD/MM/YYYY') : ''
    }`
  },
}

export const ORDER_WITH_DATE: IFieldsProps = {
  key: 'order_id',
  label: 'order',
  optionsKeys: ['orders'],
  noTranslation: true,
  labelKey: 'number',
  component: 'dropdown',
  customGetOptionLabel: (option: Record<string, unknown> | IOptionData, t: TranslateFn) => {
    // @ts-ignore
    return `№ ${option.number || option.id} ${t('from')} ${
      // @ts-ignore
      option.created_at ? dayjs(option.created_at as string)?.format('DD/MM/YYYY') : ''
    }`
  },
}

export const ISSUER: IFieldsProps = {
  key: 'issuer_id',
  label: 'issuer',
  labelKey: 'name',
  optionsKeys: ['organizations'],
  noTranslation: true,
  component: 'dropdown',
  required: true,
  getIsHidden: (_formValues?: Record<string, unknown>, optionsData?: Record<string, unknown>) => !['system', 'administrator'].includes((optionsData?.user as ICurrentUser)?.role as string),
  getOptionsFromFormValues: (_formValues: Record<string, unknown>, optionsData?: Record<string, unknown>) => {
    const options = optionsData?.organizations &&
    Object.values(optionsData.organizations).filter((org) => org.state === 'posted')
    return options as Record<string, unknown>[] | Record<string, unknown>
  }
}

export const PAYER: IFieldsProps = {
  key: 'payer_id',
  label: 'payer',
  labelKey: 'name',
  optionsKeys: ['organizations'],
  noTranslation: true,
  component: 'dropdown',
  required: true,
  getOptionsFromFormValues: (_formValues: Record<string, unknown>, optionsData?: Record<string, unknown>) => {
    const options =  optionsData?.organizations &&
    Object.values(optionsData.organizations).filter((org) => org.state === 'posted')
    return options as Record<string, unknown>[] | Record<string, unknown>
  }
}

export const SOCIAL_NETWORKS = [
  {
    key: 'facebook_url',
    label: 'Facebook',
    placeholder: 'Profile URL',
  },
  {
    key: 'instagram_url',
    label: 'Instagram',
    placeholder: 'Profile URL',
  },
  {
    key: 'twitter_url',
    label: 'Twitter',
    placeholder: 'Profile URL',
  },
  {
    key: 'linkedin_url',
    label: 'LinkedIn',
    placeholder: 'Profile URL',
  },
  {
    key: 'youtube_url',
    label: 'YouTube',
    placeholder: 'Profile URL',
  },
  {
    key: 'tiktok_url',
    label: 'TikTok',
    placeholder: 'Profile URL',
  },
]

export const TOOLBAR_CONFIG: QuillOption = [
  [{ header: [1, 2, 3, false] }],
  ['bold', 'italic', 'underline', 'strike'],
  [{ list: 'ordered' }, { list: 'bullet' }],
  ['link', 'image'],
  ['clean'],  
]
