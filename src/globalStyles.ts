import { createGlobalStyle } from 'styled-components'

const GlobalStyle = createGlobalStyle`
    body {
      margin: 0;
      line-height: 1;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-family: ${({ theme }) => theme.font.family.primary};
    }

    // ReactTexty styles
    [data-texty] {
      display: block !important;
    }

    [data-texty-tooltip] {
      background-color: ${({ theme }) => theme.color.general.dark};
      max-width: 60vw;
      padding: 7px 10px;
      color: ${({ theme }) => theme.color.general.light};

      span {
        color: ${({ theme }) => theme.color.general.light};
      }

      &.invisible {
        opacity: 0;
      }
    }

    [data-texty-arrow] {
      border-color: ${({ theme }) => theme.color.general.dark} transparent transparent;
    }

    [data-tooltip]:before {
      /* needed - do not touch */
      content: attr(data-tooltip);
      position: absolute;
      opacity: 0;
      z-index: 50000;

      /* customizable */
      transition: all 0.15s ease;
      padding: 10px;
      border-radius: 10px;
    }

    [data-tooltip]:hover:before {
      /* needed - do not touch */
      opacity: 1;

      /* customizable */
      background: ${({ theme }) => theme.color.general.dark};
      color: ${({ theme }) => theme.color.general.light};

      .table & {
        margin-bottom: 0;
      }
    }

    [data-tooltip]:not([data-tooltip-persistent]):before {
      pointer-events: none;
    }

    button {
      color: inherit;
      border: none;
      padding: 0;
      cursor: pointer;
    }

    button, input, optgroup, select, textarea {
      font-family: inherit;
    }

    figure {
      margin: 0;
    }

    input::-moz-focus-inner {
      border: 0;
      padding: 0;
      margin: 0;
    }

    ul, ol, dd {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 0;
      font-size: inherit;
      font-weight: inherit;
    }

    p {
      margin: 0;
    }

    fieldset {
      border-width: 0;
      padding: 0;
      margin: 0;
    }

    :focus {
      outline: 0;
    }

    ::placeholder {
      color: ${({ theme }) => theme.color.general.gray3} !important;
    }

    .react-select__placeholder {
      color: ${({ theme }) => theme.color.general.gray3} !important;
    }

    *, :after, :before {
      box-sizing: border-box;
    }

    .inline {
      display: inline;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
      background: ${({ theme }) => theme.color.general.gray2};
      border-radius: 10px;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
      background: ${({ theme }) => theme.color.general.gray2};
    }

    .ql-tooltip.ql-editing {
      z-index: 10000;
      transform: translateY(0);
      left: -10px !important;
      top: auto !important;
      bottom: -10px;
      border-radius: 6px;
      white-space: initial;
    }
`
export default GlobalStyle
