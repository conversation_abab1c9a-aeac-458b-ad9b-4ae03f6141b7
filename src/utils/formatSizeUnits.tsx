export const formatSizeUnits = (bytes: string | number) => {
  if (bytes as number >= 1073741824) {
    bytes = (bytes as number / 1073741824).toFixed(2) + ' GB'
  } else if (bytes as number >= 1048576) {
    bytes = (bytes as number / 1048576).toFixed(2) + ' MB'
  } else if (bytes as number >= 1024) {
    bytes = (bytes as number / 1024).toFixed(2) + ' KB'
  } else if (bytes as number > 1) {
    bytes = bytes + ' bytes'
  } else if (bytes === 1) {
    bytes = bytes + ' byte'
  } else {
    bytes = '0 bytes'
  }
  return bytes
}
