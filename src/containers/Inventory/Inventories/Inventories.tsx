import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import InventoriesTableCard from './InventoriesTableCard'

const Inventories = () => {
  return (
    <GridLayout rightPanelComponent={<Outlet context={{}} />}>
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={InventoriesTableCard as unknown as React.JSX.Element}
        tableCardHeight={170}
        iconName='inventory'
      />
    </GridLayout>
  )
}

export default Inventories
