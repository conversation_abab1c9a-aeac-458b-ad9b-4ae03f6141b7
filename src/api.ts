import Cookies, { CookieGetOptions } from 'universal-cookie'
import { put, select } from 'redux-saga/effects'

import { API_METHODS, API_METHODS_TYPE, CRM_TYPES_WITH_POSTED_STATE, GET_CRM_API_URLS } from './apiConstants'
import { doRefreshToken } from './redux-saga/sagas/auth'
import { popupAlertShow } from './redux-saga/reducers/common'
import { selectUserDetails, selectUserToken } from './redux-saga/selectors'
import { signOut } from './redux-saga/reducers/user'

export const buildURL = ({
  type,
  id = '',
  queryParams = [],
  operation,
  withFacets,
  apiUrlParam,
  apiMethod,
}: {
  type: string
  id?: string | number
  queryParams?: { key: string; value: string | number }[]
  operation?: string
  withFacets?: boolean
  apiUrlParam?: string | number
  apiMethod?: API_METHODS_TYPE
}) => {
  const apiUrlsObjectOrString: string = GET_CRM_API_URLS(apiUrlParam as string)?.[type]

  const urlPath: string =
    typeof apiUrlsObjectOrString === 'string'
      ? apiUrlsObjectOrString
      : apiUrlsObjectOrString?.[operation || 'read'] || ''

  if (urlPath === undefined) {
    throw new Error(`No API present for given key: ${type}`)
  }

  let url: string = `${process.env.REACT_APP_API_URL}${urlPath}${
    withFacets ? (urlPath.endsWith('/') ? '' : '/') + 'facets' : ''
  }`

  if (id !== '') {
    url += `${urlPath.endsWith('/') ? '' : '/'}${id}`
  }

  if ((operation === 'read' || apiMethod === 'GET') && !withFacets && !id) {
    if (CRM_TYPES_WITH_POSTED_STATE.includes(type)) {
      queryParams.push({ key: 'state', value: 'posted' })
    }
  }

  if (queryParams.length) {
    const filters =
      queryParams
        .filter(
          (param) =>
            ![
              'sort',
              'limit',
              'skip',
              'columns',
              'searchValue',
              'searchFields',
              'date_from',
              'date_to',
            ].includes(param.key)
        )
        .map((val) => `${val.key}=${val.value}`) || []

    const getParamValue = (key: string) => queryParams.find((param) => param.key === key)?.value

    const searchFields = getParamValue('searchFields')
    const searchValueParsedToFilters = searchFields
      ? [`${searchFields}__like=${getParamValue('searchValue')}`]
      : []

    const filtersAll = filters.concat(searchValueParsedToFilters).join(',')

    const urlParamsForPostgresTable = {
      filters: filtersAll,
      sort: (getParamValue('sort') as string)?.replace('_asc', '=asc').replace('_desc', '=desc'),
      limit: getParamValue('limit'),
      skip: getParamValue('skip'),
      date_from: getParamValue('date_from'),
      date_to: getParamValue('date_to'),
    }
    const urlParamsForPostgresFacets = {
      filters: filtersAll,
      columns: getParamValue('columns'),
    }

    const urlParams: Record<string, string | number | undefined> = withFacets
      ? urlParamsForPostgresFacets
      : urlParamsForPostgresTable

    const urlParamsFiltered = Object.keys(urlParams).filter(
      (key) => !!urlParams[key] || urlParams[key] === 0 || !!(urlParams[key] as string)?.length
    )

    if (urlParamsFiltered.length) {
      url += `?${urlParamsFiltered.map((urlParam) => `${urlParam}=${urlParams[urlParam]}`).join('&')}`
    }
  }

  return url
}

export function* verifyToken(tokenArg?: string, exp?: string): Generator<unknown> {
  const cookies = new Cookies()
  const access_token = yield select(selectUserToken)
  const details = yield select(selectUserDetails)

  const expFromCookies = cookies.get('tokenExp', { path: '/' } as CookieGetOptions)

  const expirationDate = exp || details?.exp || expFromCookies
  const tokenVerified = tokenArg || access_token

  const isTokenExpired = !tokenVerified || !expirationDate || (expirationDate && Date.now() > +expirationDate)

  if (isTokenExpired) {
    const newAccessToken: string = yield doRefreshToken()
    return newAccessToken
  }
  return tokenVerified
}

export function* buildRequest({
  id,
  requestBody,
  operation,
  queryParams,
  apiUrlParam,
  apiMethod,
  token: tokenArg,
  exp: tokenExpArg,
  type,
  withFacets,
}: {
  id?: string | number
  requestBody?: Record<string, unknown> | FormData
  operation?: string
  queryParams?: { key: string; value: string | number }[]
  apiUrlParam?: string | number
  apiMethod?: API_METHODS_TYPE
  token?: string
  exp?: string
  type: string
  withFacets?: boolean
}): Generator<unknown> {
  let token: string = ''
  if (type !== 'refreshToken' && type !== 'signIn' && type !== 'signUp') {
    token = yield verifyToken(tokenArg, tokenExpArg)
  }

  const isMultipartFormData = (type.includes('filesUpload') && !id) || type === 'ordersParseFile' || type === 'shipmentsParseFile' || type === 'inventoriesParseFile'

  const headers: Record<string, string> = {
    Origin: window.location.origin,
  }

  if (token) {
    headers.Authorization = `Bearer ${token}`
  }

  if (!isMultipartFormData) {
    headers['Content-Type'] = 'application/json'
  }

  const options: Record<string, unknown> = {
    headers: new Headers(headers),
    method: apiMethod || API_METHODS[operation as string],
  }

  if (requestBody) {
    options['body'] = isMultipartFormData ? requestBody : JSON.stringify(requestBody)
  }

  return new Request(
    buildURL({ type, id, queryParams, operation, withFacets, apiUrlParam, apiMethod }),
    options
  )
}

export function* fetchData(request: Request) {
  const response: Response = yield fetch(request)
  const result: unknown = yield response.json()

  if (response.status === 401) {
    yield put(signOut())
  }

  return result
}

export function* showError(
  response: {
    detail?: string | { message: string }
  },
  withoutPopups?: boolean
) {
  if (!response || (response?.detail && Object.keys(response).length === 1)) {
    if (response?.detail) {
      const regEx = new RegExp(/^(Auth0:\d\d\d\s+[\s\S]*)$/)
      const { detail } = response
      const errorText: string = typeof detail === 'string' ? detail : detail?.message

      let currentError = typeof errorText === 'string' && errorText
      if (regEx.test(errorText)) {
        const authText = errorText.split(' ', 1)[0]
        currentError = errorText.replace(`${authText} `, '')
      }

      if (errorText === 'Missing bearer token') {
        yield put(signOut())
      }

      if (!withoutPopups) {
        yield put(
          popupAlertShow({
            contentKey: currentError || 'somethingWentWrong',
            type: 'error',
            timeout: 10000,
          })
        )
      }
    }
    throw new Error((response?.detail as string) || 'Something went wrong')
  }
}
