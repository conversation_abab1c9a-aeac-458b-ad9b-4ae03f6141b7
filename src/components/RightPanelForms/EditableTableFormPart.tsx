import { TFunction } from 'i18next'
import { useTheme } from 'styled-components'
import { ScreenContext, Select } from '@aidsupply/components'
import { useContext, useEffect, useState } from 'react'
import {
  EDITABLE_TABLE_CHANGING_BLOCKS_OPTIONS,
  EDITABLE_TABLE_NAVBAR_FORM_CONFIG,
} from '../config/editableTableNavBarForms'
import { getEditableTableColumns } from '../config/columnsEditableTable'
import { IFieldsProps, IFormConfig, IFormFieldsProps, IOptionData } from '../Form/interfaces'
import FormBlockTabs from '../Form/FormBlockTabs'
import Table from '../Table/Table'
import { IBrandItem } from '../../commonInterfaces'
import { TLanguages } from '../../locales'
import { useMappedState } from '../../hooks'
import { selectFormValueInitialByKey } from '../../redux-saga/selectors.ts'

interface IEditableTableFormPartProps {
  blockKey: string
  cellProps: Record<string, unknown>
  columns: Record<string, unknown>[]
  compoundTypeKey: string
  fields: IFieldsProps[]
  formFieldsProps?: IFormFieldsProps
  getPanelHeader: (blockKey: string) => string
  id: number
  isNavbarHidden: boolean
  language: string
  optionsData: Record<string, unknown>
  t: TFunction
  typeData: { key: string }
  updateTableRows: (value: Record<string, unknown> | string) => void
  validationRules: Record<string, unknown>
}

const EditableTableFormPart = ({
  blockKey,
  cellProps,
  columns,
  compoundTypeKey,
  fields,
  formFieldsProps,
  id,
  isNavbarHidden,
  language,
  optionsData,
  t,
  typeData = { key: '' },
  updateTableRows,
  validationRules,
}: IEditableTableFormPartProps) => {
  const theme = useTheme()
  const { currentBreakpoint } = useContext(ScreenContext) || {}
  const isReadOnly = cellProps.is_read_only as boolean

  const type: string = typeData.key || ''
  const initialData = useMappedState(selectFormValueInitialByKey(type?.split('.')[0], blockKey)) || []

  const changingBlocksConfig = !isReadOnly
    ? EDITABLE_TABLE_CHANGING_BLOCKS_OPTIONS[type as keyof typeof EDITABLE_TABLE_CHANGING_BLOCKS_OPTIONS]
    : undefined
  const changingBlocksOptions = changingBlocksConfig?.options
  const changingBlocksDefaultValue =
    (changingBlocksConfig?.getDefaultValue &&
      changingBlocksConfig.getDefaultValue(
        initialData as Record<string, unknown>[],
        changingBlocksOptions as IOptionData[]
      )) ||
    changingBlocksOptions?.[0]

  const [changingBlocksDependencyValue, setChangingBlocksDependencyValue] = useState(changingBlocksDefaultValue)

  useEffect(() => {
    if (!isReadOnly) {
      setChangingBlocksDependencyValue(changingBlocksDefaultValue)
    }
  }, [changingBlocksConfig, changingBlocksDefaultValue, changingBlocksOptions, initialData, isReadOnly])

  const formConfig =
    (!isNavbarHidden && !isReadOnly && (EDITABLE_TABLE_NAVBAR_FORM_CONFIG[type] as unknown as IFormConfig)) || {}

  const editableNavbarFields =
    formConfig && changingBlocksDependencyValue
      ? // @ts-ignore
        [...formConfig.changingBlocks[changingBlocksDependencyValue.id], ...formConfig.fields]
      : // @ts-ignore
        formConfig.fields

  // @ts-ignore
  const withTabs = !!formConfig?.withTabs?.length

  const getTabContent = (tabKey: string) => {
    const columnsWithTabs =
      tabKey &&
      getEditableTableColumns(EDITABLE_TABLE_NAVBAR_FORM_CONFIG[type] ? type : compoundTypeKey, tabKey as TLanguages, t)

    // @ts-ignore
    const headerHeight = typeof formConfig?.headerHeight === 'undefined' ? 48 : formConfig.headerHeight
    return (
      <>
        {!!formConfig?.changingBlocksDependency && !!changingBlocksOptions && (
          <Select
            className="changingBlocksEditableTableSelect"
            value={changingBlocksDependencyValue}
            onChange={(option: IOptionData) => setChangingBlocksDependencyValue(option)}
            withBorder
            labelKey={`${formConfig.labelKey || 'translations'}.${language}`}
            options={changingBlocksOptions}
            withTranslation
            margin="20px 0 0 0"
          />
        )}
        <Table
          key={tabKey}
          blockKey={blockKey}
          formId={id}
          editableTableConfig={formConfig}
          isDeletionByState={formConfig.isDeletionByState}
          rowKey={formConfig?.tableRowKey || 'id'}
          navbarHidden={isReadOnly || formConfig?.navbarHidden}
          cellProps={cellProps}
          createNewOptions={formConfig?.createNewOptions}
          optionsData={optionsData}
          changingBlocksDependencyValue={changingBlocksDependencyValue as Record<string, unknown>}
          editableNavbarFields={editableNavbarFields as Record<string, unknown>}
          updateTableRows={updateTableRows}
          isReadOnly={isReadOnly}
          editable={!isReadOnly}
          typeData={typeData}
          columns={tabKey ? columnsWithTabs : columns}
          className="editableTable"
          tableMode="table"
          data={initialData as Record<string, unknown>[]}
          gap={theme.grid[currentBreakpoint as keyof typeof theme.grid].columns.gapWidth}
          isMainTable={false}
          validationRules={validationRules}
          // useRowKeyInEditCellName={type === 'items.modifications'}
          {...(!blockKey.endsWith('_items') ? { containerNotFound: true } : {})}
        />
      </>
    )
  }

  return withTabs ? (
    <FormBlockTabs
      // @ts-ignore
      type={type}
      blockKey={blockKey}
      formFieldsProps={formFieldsProps}
      fields={fields}
      getTabScreenCustom={getTabContent}
      // @ts-ignore
      defaultTabsValues={formConfig?.defaultTabsValues}
      // @ts-ignore
      withTabsFieldsCustom={formConfig?.withTabs}
      // @ts-ignore
      isLanguageTabs={formConfig?.isLanguageTabs}
    />
  ) : (
    getTabContent(language)
  )
}

export default EditableTableFormPart
