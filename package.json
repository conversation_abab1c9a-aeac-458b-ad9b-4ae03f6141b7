{"name": "goodzyk-crm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b --noEmit && vite build", "lint": "eslint . --no-warn-ignored", "preview": "vite preview", "deploy": "aws s3 sync build/ s3://goodzyk-crm"}, "dependencies": {"@aidsupply/components": "^2.24.8", "@redux-devtools/extension": "^3.3.0", "@reduxjs/toolkit": "^2.6.1", "@sentry/react": "^9.6.1", "dot-object": "^2.1.5", "lodash.clonedeep": "^4.5.0", "nanoid-dictionary": "^5.0.0", "query-string": "^9.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-i18next": "^15.4.1", "react-password-checklist": "^1.8.1", "react-popper": "^2.3.0", "react-redux": "^9.2.0", "react-router": "^7.3.0", "react-use-websocket": "^4.13.0", "recharts": "^3.2.0", "redux": "^5.0.1", "redux-saga": "^1.3.0", "styled-components": "^5.3.3", "universal-cookie": "^8.0.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/dot-object": "^2.1.6", "@types/i18next": "^13.0.0", "@types/lodash.clonedeep": "^4.5.9", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/styled-components": "^5.1.34", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.25.1", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-plugin-commonjs": "^0.10.4"}}