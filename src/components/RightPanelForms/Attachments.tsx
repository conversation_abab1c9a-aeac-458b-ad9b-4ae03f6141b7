import { isObjectEmpty } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'

import { StateType, UpdateInput } from '../Form/interfaces'
import { IFileDrafted } from '../UploadFiles/Thumbnail'
import FilesForm from './FilesForm'
import EmptyScreen from '../EmptyScreen/EmptyScreen'
import { useMappedState } from '../../hooks'
import { IStore } from '../../configureStore.ts'
import { selectFormValuesChangedByType, selectFormValuesWhole } from '../../redux-saga/selectors.ts'

const Attachments = ({
  isReadOnly,
  type,
  updateInput,
  blockKey,
}: {
  isReadOnly: boolean
  type: string
  updateInput: UpdateInput
  blockKey: 'photos' | 'files'
}) => {
  const { t } = useTranslation('forms')
  const formValuesChanged = useMappedState(selectFormValuesChangedByType(type))
  const formValues = useSelector((state: IStore) => selectFormValuesWhole(state, type))

  if (isReadOnly && isObjectEmpty(formValues.files)) {
    return <EmptyScreen text={t('noAttachments')} iconName="noItem" width="150px" height="150px" />
  }

  return (
    <FilesForm
      changedPhotos={formValuesChanged?.photos as Record<string, IFileDrafted[]>}
      changedFilesStates={formValuesChanged?.changedFilesStates as Record<string, StateType>}
      isReadOnly={isReadOnly}
      itemCharacteristics={formValues.characteristics as Record<string, unknown>}
      id={formValues.id as number}
      values={formValues[blockKey] as Record<string, { active: IFileDrafted[]; disabled?: IFileDrafted[] }>}
      updateInput={updateInput as unknown as UpdateInput}
      primaryPhotoId={formValues.photo_id as number}
      primaryLogoId={formValues.logo_photo_id as number}
      primaryBannerId={formValues.banner_photo_id as number}
      filesType={blockKey}
      withTabsHidden={type === 'reports'}
    />
  )
}

export default Attachments
