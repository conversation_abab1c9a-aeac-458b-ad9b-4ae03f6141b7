import { useEffect, useContext } from 'react'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import Cookies from 'universal-cookie'
import { DefaultTheme } from 'styled-components'
import { ScreenContext, Select } from '@aidsupply/components'
import { LANGUAGES_FULL_NAME, TLanguages } from '../../locales'
import { IIconProps } from '../../assets/icons/interfaces'
import { StyledSelect } from './styled'

const LanguageSelect = ({ theme, minWidth, iconLeftProps, isFullSize, isIntroScreensSelect, label }: {
  theme: DefaultTheme
  minWidth?: string
  iconLeftProps?: IIconProps
  isIntroScreensSelect?: boolean
  isFullSize?: boolean
  label?: string
}) => {
  const { width, sm } = useContext(ScreenContext) || {}
  const isMobile = width && width < sm

  const {
    i18n: {
      language: currentLng,
      changeLanguage,
      // @ts-ignore
      options: { languages },
    },
  } = useTranslation()

  useEffect(() => {
    const cookies = new Cookies();
    const lngFromCookies = cookies.get('lng')
    if (!lngFromCookies) {
      cookies.set('lng', currentLng, { path: '/' })
    }
  }, []);

  const onChange = ( lang: Record<string, TLanguages>) => {
    changeLanguage(lang.id)
    const cookies = new Cookies()
    cookies.set('lng', lang.id, { path: '/' })
  }

  const getLanguagesList = () => {
    return languages?.map((lang: TLanguages) => ({
      id: lang,
      label: !isFullSize ? LANGUAGES_FULL_NAME[lang].slice(0, 3) : LANGUAGES_FULL_NAME[lang],
    }))
  }

  const value = {
    id: currentLng,
    label: !isFullSize ? LANGUAGES_FULL_NAME[currentLng as TLanguages].slice(0, 3) : LANGUAGES_FULL_NAME[currentLng as TLanguages]
  }

  if (isIntroScreensSelect) {
    return (
      <StyledSelect minWidth={minWidth} className={clsx(isFullSize && 'isFullSize')}>
        <Select
          label={label}
          iconLeftProps={!isMobile && isFullSize && { name: 'planet', fill: theme.color.general.dark }}
          onChange={onChange}
          options={getLanguagesList()}
          value={value}
          variant="primary"
          withBorder
          withoutValidation
          isSearchable={false}
        />
      </StyledSelect>
    )
  }

  return (
    <Select
      label={label}
      iconLeftProps={iconLeftProps}
      onChange={onChange}
      options={getLanguagesList()}
      value={value}
      variant="primary"
      withoutValidation
      fullWidth
    />
  )
}

export default LanguageSelect
