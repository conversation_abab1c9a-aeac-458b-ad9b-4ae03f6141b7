import clsx from 'clsx'
import { AidSupplyLogo } from '../../assets/icons/LogoAnimated.tsx'
import { StyledLoadingNavigationWrap } from './styled.ts'

const LoadingNavigation = ({ className }: { className?: string }) => {
  return (
    <StyledLoadingNavigationWrap className={clsx(className, 'loadingScreen')}>
      <AidSupplyLogo width={100} height={100} />
    </StyledLoadingNavigationWrap>
  )
}

export default LoadingNavigation
