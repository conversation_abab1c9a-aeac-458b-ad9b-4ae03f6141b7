import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react'
import { useTheme } from 'styled-components'
import { useDispatch } from 'react-redux'
import { FlexRow, Icon } from '@aidsupply/components'
import { useLocation, useNavigate, useParams } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useMappedState } from '../../hooks'
import { selectDataByType, selectDataTypeObject, selectSidebarInitialData } from '../../redux-saga/selectors'
import { sidebarFetchById, sidebarItemSet } from '../../redux-saga/reducers/sideBar'
import useRightPanelProps from '../../hooks/useRightPanelProps'
import { TLanguages } from '../../locales'
import { ISubmenu } from '../NavMenu/config'
import RightPanelCard from './RightPanelCard'
import { getRightPanelCardProps } from './rightPanelCardConfig'
import { StyledRightPanel, StyledTitle } from './styled'

interface RightPanel {
  children: React.ReactNode
  absolutePositioned?: boolean
  width?: string
  minWidth?: string
  openedValue?: boolean
  currentBreakpoint?: string
  inProgress?: boolean
  rightPanelInProgress?: boolean
  rightPanelProps?: Record<string, unknown>
  toggleDrawer?: Dispatch<SetStateAction<boolean>>
  isIconRightPanelWide?: boolean
  isRightPanelExtendedWider?: boolean
}

const RightPanel = ({
  children,
  width,
  minWidth,
  openedValue,
  inProgress,
  toggleDrawer,
  rightPanelInProgress,
  rightPanelProps,
  absolutePositioned,
  isIconRightPanelWide,
  isRightPanelExtendedWider
}: RightPanel) => {
  const dispatch = useDispatch()
  const theme = useTheme()
  const params = useParams()
  const { rightPanelId, action } = params
  const navigate = useNavigate()
  const { search, pathname } = useLocation()
  const panelRef = useRef<HTMLDivElement>(null)
  const itemInitial = useMappedState(selectSidebarInitialData) as Record<string, unknown>
  const typeData = useMappedState(selectDataTypeObject) as ISubmenu
  const type = typeData?.key
  const data = useMappedState(selectDataByType(type))

  const [currentDocumentNumber, setCurrentDocumentNumber] = useState<string>('')

  const basePanelProps = useRightPanelProps(currentDocumentNumber)

  const {
    t, i18n: { language },
  } = useTranslation(['forms', 'notifications', 'menu', 'table'])

  const mergedRightPanelProps = {
    ...basePanelProps,
    ...rightPanelProps,
    withRightPanelWideIcon:
      isIconRightPanelWide &&
      basePanelProps.title != t('comments') &&
      basePanelProps.title != t('addInventory'),
    isRightPanelWideProps: { width: 20, height: 20, wrapperWidth: 20, wrapperHeight: 20 },
  }

  useEffect(() => {
    if (itemInitial && !itemInitial.id && panelRef.current) {
      panelRef.current.scrollTo(0, 0)
    }
  }, [itemInitial])

  useEffect(() => {
    if (type && rightPanelId !== 'clone') {
      if (!rightPanelId) {
        dispatch(sidebarItemSet({ items: []}))
      } else if (!isNaN(+rightPanelId) || ['new', 'invite'].includes(rightPanelId)) {
        dispatch(sidebarFetchById({ id: rightPanelId, type, reload: rightPanelId === 'new' ? null : true}))
      }
    }
  }, [rightPanelId, type])

  useEffect(() => {
    if (params.rightPanelId) {
      const currentDocumentNumber = data?.find((item: Record<string, unknown>) => item.id === Number(params.rightPanelId))?.number
      setCurrentDocumentNumber(currentDocumentNumber)
    }
  }, [])

  const onCloseRightPanel = () => {
    toggleDrawer?.(!openedValue)
    if (rightPanelId || action) {
      navigate({ pathname: '.', search })
    }
  }

  const getTopCard = () => {
    return <RightPanelCard
      data={getRightPanelCardProps(language as TLanguages, type, itemInitial, data)}
      isRightPanelExtendedWider={isRightPanelExtendedWider}
    />
  }

  const getHeader = () => {
    return(
      <StyledTitle>
        {mergedRightPanelProps?.withBackIcon && <Icon {...mergedRightPanelProps.backIconProps} />}
        {mergedRightPanelProps?.title}
      </StyledTitle>
    )
  }

  const isRightPanelInnerPage = pathname.includes('comments') || pathname.includes('add')
  
  return(
    <StyledRightPanel
      isCommentsRightPanel={isRightPanelInnerPage}
      isRightPanelExtendedWider={isRightPanelExtendedWider}
      closeIconProps={{
        width: 18,
        height: 18,
        wrapperWidth: 32,
        wrapperHeight: 32,
        fill: theme.color.general.gray4,
        strokeWidth: 1.4,
        className: 'topCardClose'
      }}
      openedValue={openedValue}
      title={
        <FlexRow
          className="editPanelTitleRow"
          justifyContent={rightPanelId != 'notifications' ? 'start' : 'space-between'}
          {...(mergedRightPanelProps?.titleProps || {})}
        >
          { mergedRightPanelProps?.title ? getHeader() : getTopCard()}
        </FlexRow>
      }
      width={width}
      minWidth={minWidth}
      ref={panelRef}
      onClose={onCloseRightPanel}
      className={inProgress || rightPanelInProgress ? 'rightPanel inProgress' : 'rightPanel'}
      id="rightPanel"
      absolutePositioned={absolutePositioned}
    >
      {children}
    </StyledRightPanel>)
}

export default RightPanel
