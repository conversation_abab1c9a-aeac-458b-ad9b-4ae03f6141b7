import { IFormConfig } from '../../../components/Form/interfaces'

export const faqsRightPanelConfig: IFormConfig = {
  withTabs: ['translations', 'body'],
  validationRules: {
    'translations.en': ['required'],
    'body.en': ['required'],
    organization_id: ['required'],
  },
  fields: {
    noTitle: [
      {
        key: 'state',
        label: 'state',
        optionsKeys: ['states'],
        component: 'dropdown',
      },
    ],
    general: [
      {
        key: 'organization_id',
        label: 'organization',
        noTranslation: true,
        labelKey: 'name',
        valueKey: 'id',
        optionsKeys: ['organizations'],
        component: 'dropdown',
        isClearable: true,
        required: true,
        getDisabled: () => true
      },
    ],
    translations: [
      {
        type: 'translations',
        key: 'translations',
        label: 'question',
        required: true,
      },
      {
        type: 'richText',
        key: 'body',
        label: 'answer',
        required: true,
      },
    ],
  },
}
