import { useTranslation } from 'react-i18next'
import { IMAGEKIT_PARAMS_CONFIG, Typography, Image, NoImageAvailable } from '@aidsupply/components'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { IMAGEKIT_URL } from '../../../constants'
import { StateType } from '../../../components/Form/interfaces'
import { ITranslations } from '../../../locales'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StyledStateWrapper } from './styled'

interface IItemsTableCard {
  id: number
  name: string
  translations: ITranslations
  state: StateType
  sku: string
  photo_url: string
}

const ItemsTableCard = ({ data, className }: {data: IItemsTableCard, className?: string}) => {
  const { t } = useTranslation('table')
  const { name, id, state, photo_url, sku } = data
  
  return (
    <TableCardStyled key={id} direction='column' gap={1} className={className}>
      <>
      <StyledStateWrapper>
        <Typography as={ReactTexty} type="h4">
          {name}
        </Typography>
        <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />
      </StyledStateWrapper>
      <Typography as={ReactTexty} type="button2" fontWeight="400">
        {sku}
      </Typography>
      {photo_url ? (
          <Image
            className="brandLogo"
            src={photo_url}
            height={172}
            width={'100%'}
            maxWidth={'100%'}
            imagekitParams={IMAGEKIT_PARAMS_CONFIG.crm.thumbnail}
            imagekitUrl={IMAGEKIT_URL}
            alt="photo item"
          />
        ) : (
          <NoImageAvailable height={172} width={'100%'} className="brandLogo"/>
        )}
      </>
    </TableCardStyled>
  )
}

export default ItemsTableCard
