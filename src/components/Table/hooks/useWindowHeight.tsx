import { useEffect, useState, useRef } from "react";
import debounce from "lodash/debounce";

export function useWindowHeight(threshold: number = 50, delay: number = 200) {
  const [height, setHeight] = useState(() => window.innerHeight);
  const [prevHeight, setPrevHeight] = useState(() => window.innerHeight);
  const lastHeight = useRef(window.innerHeight);

  useEffect(() => {
    const handleResize = debounce(() => {
      const newHeight = window.innerHeight;
      if (Math.abs(newHeight - lastHeight.current) >= threshold) {
        setPrevHeight(lastHeight.current); 
        setHeight(newHeight);            
        lastHeight.current = newHeight;
      }
    }, delay);

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [threshold, delay]);

  return { height, prevHeight };
}
