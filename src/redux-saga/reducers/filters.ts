import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { NavigateFunction } from 'react-router'
import { TLanguages } from '../../locales'

export interface IFacets {
  count: number
  id: number | string
  slug: string
  label: string | { en: string; uk: string }
}

export interface IFiltersState {
  active: Record<string, string[]>
  apiQuery: string
  labels: string[]
  facets: Record<string, IFacets[]>
  facetsInitial: Record<string, unknown>
  filtersInitialized: boolean
  filtersPanelOpened: boolean
  facetJustSelected: Record<string, string>
}

export interface IFiltersAction {
  key: string
  value: string
  lng: TLanguages
  locationSearch: string
  navigate: NavigateFunction
}

const buildFilter = (key: string, value: string) => `${key}:${value}`

const buildLabels = (active: Record<string, string[]>) => {
  const labels: string[] = []

  if (active) {
    Object.keys(active).forEach((key: string) => {
      active[key].forEach((value) => labels.push(buildFilter(key, value)))
    })
  }

  return labels
}

export const filtersSlice = createSlice({
  name: 'filters',
  initialState: {
    active: {},
    apiQuery: '',
    labels: [],
    facets: {},
    facetsInitial: {},
    filtersInitialized: false,
    filtersPanelOpened: false,
    facetJustSelected: {},
  },
  reducers: {
    filtersSetOnFirstLoad(state: IFiltersState, action: PayloadAction<{active: Record<string, string[]>}>) {
      state.active = action.payload.active,
      state.labels = buildLabels(action.payload.active)
    },
    filtersSetFacets(state: IFiltersState, action: PayloadAction<Record<string, any>>) {
      const incomingFacets = action.payload.facets || {}

      const shouldMergeWithExisting = state.filtersInitialized

      const getFilteredFacetsForInitialLoad = (facets: typeof state.facets) => {
        return Object.keys(facets).reduce((acc, facetKey) => {
          const facetOptions = facets[facetKey]
          const hasActiveFilters = state.active[facetKey]?.length > 0
          const hasMultipleOptions = facetOptions?.length > 0

          // Exclude facets that have only one option, but no active filters
          if (hasActiveFilters || hasMultipleOptions) {
            return { ...acc, [facetKey]: facetOptions }
          }
          return acc
        }, {})
      }

      const getMergedFacetsForUpdate = (
        currentFacets: typeof state.facets,
        newFacets: typeof state.facets
      ) => {
        return Object.keys(currentFacets).reduce((acc, facetKey) => {
          const currentOptions = currentFacets[facetKey]
          const newOptions = newFacets[facetKey]

          // if (facetKey === state.facetJustSelected.key) {
          if (currentOptions?.length <= 10) {
            // no sorting for these options
            const mappedOptions = currentOptions.map((currentOption: any) => {
              return newOptions.find((newOption: any) => newOption.id === currentOption.id) || currentOption
            })
            return { ...acc, [facetKey]: mappedOptions }
          } else {
            // Sort the options by count (just take new options from api)
            // Exclude facets that have only one option, but no active filters
            const facetOptions = newFacets[facetKey]
            const hasActiveFilters = state.active[facetKey]?.length > 0
            const hasMultipleOptions = facetOptions?.length > 0

            if (hasActiveFilters || hasMultipleOptions) {
              return { ...acc, [facetKey]: newOptions }
            } else {
              return acc
            }
          }
        }, {})
      }

      state.facets = shouldMergeWithExisting
        ? getMergedFacetsForUpdate(state.facets, incomingFacets)
        : getFilteredFacetsForInitialLoad(incomingFacets || {}),
      state.filtersInitialized = true
    },
    filtersAdd(state: IFiltersState, action: PayloadAction<IFiltersAction>) {
      const { key, value } = action.payload
      const currentValues = (state.active[key] as string[]) || []

      if (!currentValues.includes(value)) {
        state.active = {
          ...state.active,
          [key]: [...currentValues, value],
        }
      } else {
        return
      }

      state.labels = [...state.labels, ...[buildFilter(action.payload.key, action.payload.value)]]
    },
    filtersRemove(state: IFiltersState, action: PayloadAction<IFiltersAction>) {
      const newActiveFilters = state.active[action.payload.key].filter((val) => val !== action.payload.value)
      state.active = {
          ...state.active,
          [action.payload.key]: newActiveFilters,
        },
      state.labels = state.labels.filter((val) => val !== buildFilter(action.payload.key, action.payload.value))
    },
    filtersClearAll(state: IFiltersState, _action: PayloadAction<IFiltersAction>) {
      state.active = {},
      state.labels = []
    },
    filtersSetFacetGroupJustSelected(
      state: IFiltersState,
      action: PayloadAction<{ key: string; value: string }>
    ) {
      state.facetJustSelected = action.payload
    },
    filterBackToInitialState(state: IFiltersState) {
      state.active = {},
      state.labels = [],
      state.filtersInitialized = false
    }
  }
})

export const {
  filtersSetOnFirstLoad,
  filtersAdd,
  filtersRemove,
  filtersClearAll,
  filtersSetFacets,
  filtersSetFacetGroupJustSelected,
  filterBackToInitialState,
} = filtersSlice.actions
export default filtersSlice.reducer
