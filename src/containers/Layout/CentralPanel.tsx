import { DefaultTheme } from 'styled-components'
import { MIN_RIGHT_PANEL_WIDTH } from '../../constants'
import { StyledMain } from './styled'

interface ICentralPanel {
  children: React.ReactNode
  rightPanelWidth?: string
  isRightPanelExtendedWider?: boolean
  currentBreakpoint?: keyof DefaultTheme['grid']
}

const CentralPanel = ({children, rightPanelWidth, isRightPanelExtendedWider, currentBreakpoint}: ICentralPanel) => {
  let rightPanelWidthValue = rightPanelWidth

  if (currentBreakpoint === 'lg') {
    rightPanelWidthValue = isRightPanelExtendedWider ? rightPanelWidth : MIN_RIGHT_PANEL_WIDTH
  }
  return(
    <StyledMain rightPanelWidth={rightPanelWidthValue}>
      {children}
    </StyledMain>)
}

export default CentralPanel
