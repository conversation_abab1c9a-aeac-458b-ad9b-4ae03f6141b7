import { getAvailableTranslation } from '@aidsupply/components'
import { MAGNITUDES, ORGANIZATION_TYPES } from '../../data'
import { DEFAULT_LANGUAGE, ITranslations, TLanguages } from '../../locales'
import { IOptionData, StateType } from '../Form/interfaces'
import { Status } from '../../commonTypes'

export interface IRightPanelCard {
  title: string
  subTitle: string
  profilePic?: string
  visited?: string
  created?: string
  withNoPic?: boolean
  children?: React.ReactNode
  translations?: ITranslations
  state?: StateType
  status?: Status
  createdDateWithTime?: boolean
}

export const getRightPanelCardProps = (
  lng: TLanguages,
  type: string,
  itemInitial: Record<string, unknown>,
  rowData: Record<string, unknown>[]
) => {
  const data = rowData?.find((item: Record<string, unknown>) => item?.id === itemInitial?.id)

  const config: Record<string, unknown> = {
    users: {
      title:
      getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.full_name,
      subTitle: itemInitial?.email,
      profilePic: itemInitial?.profile_pic,
      withNoPic: false,
      created: itemInitial?.created_at,
      visited: itemInitial?.last_logged_at,
      translations: itemInitial?.translations,
    },
    organizations: {
      title:
        getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.name,
      subTitle: (
        ORGANIZATION_TYPES.find((option: IOptionData) => {
          return option.id === itemInitial?.type
        })?.label as ITranslations
      )?.[lng],
      withNoPic: false,
      profilePic: itemInitial?.logo_url,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      translations: itemInitial?.translations,
    },
    brands: {
      title:
        getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.name,
      subTitle: itemInitial?.slug,
      withNoPic: false,
      profilePic: itemInitial?.photo_url,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      translations: itemInitial?.translations,
    },
    items: {
      title:
        getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.name,
      withNoPic: true,
      subTitle: itemInitial?.sku,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      translations: itemInitial?.translations,
    },
    categories: {
      title:
        getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.name,
      subTitle: itemInitial?.slug,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      translations: itemInitial?.translations,
    },
    attributes: {
      title:
        getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.name,
      subTitle: itemInitial?.slug,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      translations: itemInitial?.translations,
    },
    units: {
      title:
        getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.name,
      subTitle:
        (
          MAGNITUDES.find((option: IOptionData) => {
            return option.id === itemInitial?.magnitude
          })?.label as ITranslations
        )?.[lng] || 'to_be_defined',
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
    },
    warehouses: {
      title:
        getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
        itemInitial?.name,
      subTitle: data?.organization_name,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      // TODO: remove component if it will not be used in the final version
      // children: <WarehousesCard t={t} data={data} />
    },
    orders: {
      title: itemInitial?.number,
      subTitle: itemInitial?.inquiry_number,
      withNoPic: true,
      createdDateWithTime: true,
      state: itemInitial?.state,
      status: itemInitial?.status,
      created: itemInitial?.created_at,
    },
    shipments: {
      title: itemInitial?.number,
      subTitle: itemInitial?.order_number,
      withNoPic: true,
      createdDateWithTime: true,
      state: itemInitial?.state,
      status: itemInitial?.status,
      created: itemInitial?.created_at,
    },
    inquiries: {
      title: itemInitial?.number,
      withNoPic: true,
      createdDateWithTime: true,
      state: itemInitial?.state,
      status: itemInitial?.status,
      created: itemInitial?.created_at,
    },
    faqs: {
      title: getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
      itemInitial?.name,
      subTitle: data?.organization_name,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      state: itemInitial?.state,
      translations: itemInitial?.translations
    },
    reports: {
      title: getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
      itemInitial?.name,
      subTitle: data?.organization_name,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
    },
    invoices: {
      title: itemInitial?.number,
      subTitle: itemInitial?.order_number,
      withNoPic: true,
      createdDateWithTime: true,
      state: itemInitial?.state,
      status: itemInitial?.status,
      created: itemInitial?.created_at,
    },
    pages: {
      title: getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
      itemInitial?.name,
      subTitle: data?.organization_name,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
      state: itemInitial?.state,
      translations: itemInitial?.translations
    },
    posts: {
      title: getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
      itemInitial?.name,
      subTitle: data?.organization_name,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
    },
    campaigns: {
      title: getAvailableTranslation(itemInitial?.translations as ITranslations, DEFAULT_LANGUAGE, lng) ||
      itemInitial?.name,
      subTitle: data?.organization_name,
      withNoPic: true,
      created: itemInitial?.created_at,
      visited: itemInitial?.updated_at,
    },
    inventories: {
      title: itemInitial?.number,
      subTitle: itemInitial?.warehouse_name,
      withNoPic: true,
      createdDateWithTime: true,
      created: itemInitial?.created_at,
      state: itemInitial?.state,
    },
  }
  return config[type] as IRightPanelCard
}
