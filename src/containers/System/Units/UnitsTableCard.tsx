import { Icon, Typography } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { ITranslations, TLanguages } from '../../../locales'
import { StateType } from '../../../components/Form/interfaces'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { Styled<PERSON>ontainerWrapper, StyledContentWrapper, StyledStateWrapper } from './styled'

interface ICategoriesTableCard {
  id: number
  translations: ITranslations
  state: StateType
  code: string
  coefficient: number
  magnitude: React.JSX.Element
  name: string
}

const UnitsTableCard = ({ data, className }: {data: ICategoriesTableCard, className?: string}) => {
  const { t, i18n: { language: lng } } = useTranslation('table')
  const { id, state, translations, code, coefficient, magnitude, name } = data
  
  return (
    <TableCardStyled key={id} gap={1} direction='column' className={className}>
      <>
        <StyledStateWrapper>
          <Typography as={ReactTexty} type="h4">
            {translations[lng as TLanguages] || name}
          </Typography>
          <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />
        </StyledStateWrapper>
        <Typography as={ReactTexty} type="button1" fontWeight="400">
          {magnitude}
        </Typography>
        <StyledContainerWrapper>
          <StyledContentWrapper>
            <Icon
              onClick={() => {}}
              name="education"
              width={16}
              height={16}
            />
            <Typography type="button1" fontWeight="400">
              {code}
            </Typography>
          </StyledContentWrapper>
          <div className='verticalLine'></div>
          <StyledContentWrapper>
            <Icon
              onClick={() => {}}
              name="percent"
              width={16}
              height={16}
            />
            <Typography type="button1" fontWeight="400">
              {coefficient}
            </Typography>
          </StyledContentWrapper>
        </StyledContainerWrapper>
      </>
    </TableCardStyled>
  )
}

export default UnitsTableCard
