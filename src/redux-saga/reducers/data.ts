import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { convertArrayToObject } from '@aidsupply/components'
import { ISubmenu } from '../../components/NavMenu/config'
import { TLanguages } from '../../locales'
import { ICategory, ICurrency, IOrganization, ISystemStatuses } from '../../commonInterfaces'
import { ICurrentUser } from './user'

export type TableModeType = 'table' | 'cards' | 'list'

export interface ISystem {
  countries?: Record<string, { translations?: string, alpha2code?: string }>
  country_subdivisions?: Record<string, { translations?: string }>
  currencies?: Record<number, ICurrency>
  states?: Record<string, unknown>
  userRoles?: Record<string, unknown>
  // TODO: fill in the types
  organizations?: Record<string, IOrganization>
  brands?: Record<string, unknown>
  categories?: Record<number, ICategory>
  attributes?: Record<string, unknown>
  organizationTypes?: Record<string, unknown>
  organizationRoles?: Record<string, unknown>
  modifications?: Record<string, { code: string }>
  units?: Record<string, unknown>
  orderStatuses?: ISystemStatuses
  inquiryStatuses?: ISystemStatuses
  invoiceStatuses?: ISystemStatuses
  shipmentStatuses?: ISystemStatuses
  campaignStatuses?: ISystemStatuses
  inventoryStatuses?: ISystemStatuses
}

export interface IData {
  inProgress: boolean
  type: ISubmenu
  error: string | unknown
  totalItems: null | number
  system: ISystem
  apiUrlParamsCurrent: string[] | unknown
  isSystemDataLoaded: boolean
  systemInProgress: boolean
  isDataInitialized: boolean
  [type: string]: unknown
  route?: string
}

export interface IDataWithFacets {
  type: string
  locationSearch: string
  sortString: string
  pageLimit: number | false
  pageOffset: number
  query: { key: string, value: number }[]
  lng: TLanguages
  facetsToInclude?: Record<string, unknown>
  filtersFromUrl?: string
  tableMode?: TableModeType
  apiUrlParam?: string
  isLoadDataByInfiniteScroll?: boolean
}

export const dataSlice = createSlice({
  name: 'data',
  initialState: {
    inProgress: false,
    type: {} as ISubmenu,
    error: false,
    totalItems: null,
    system: {},
    apiUrlParamsCurrent: [],
    isSystemDataLoaded: false,
    systemInProgress: false,
    isDataInitialized: false,
  },
  reducers: {
    setDataType(state: IData, action: PayloadAction<{data: ISubmenu}>) {
      state.type = action.payload.data
      state.isSystemDataLoaded = false
    },
    dataFetchWithFacets(state: IData, _action: PayloadAction<IDataWithFacets>) {
      state.inProgress = true
    },
    dataFetchWithFacetsSuccess(state: IData, action: PayloadAction<{data: {
      type: string
      items: ICurrentUser[]
      total_items: number
      queryParams?: string[] | undefined
      isLoadDataByInfiniteScroll?: boolean
    }}>) {
      const { type, items, total_items, queryParams, isLoadDataByInfiniteScroll } = action.payload.data

      // @ts-ignore
      if (type === 'dashboard') {
        state[type] = action.payload.data.items
      } else {
        const prevItems = Array.isArray(state[type]) ? [...state[type]] : []
        const newItems = [...prevItems, ...items]
        // @ts-ignore
        state[type] = isLoadDataByInfiniteScroll ? newItems : items
      }
      state.inProgress = false
      state.error = false
      state.totalItems = total_items
      state.isDataInitialized = true
      state.apiUrlParamsCurrent = queryParams
    },
    dataFetchWithFacetsError(state: IData, action: PayloadAction<{error: unknown, type: string}>) {
      const { type } = action.payload
      state[type] = {}
      state.inProgress = false
      state.error = action.payload.error
      state.isDataInitialized = true
    },
    dataFetchSystemData(state: IData) {
      state.systemInProgress = true
    },
    dataFetchSystemDataSuccess(state: IData) {
      state.systemInProgress = false
      state.isSystemDataLoaded = true
    },
    dataFetchSystemDataError(state: IData) {
      state.systemInProgress = false
      state.isSystemDataLoaded = false
    },
    dataSetSystemData(state: IData, action: PayloadAction<{ key: string, data: unknown}>) {
      state.systemInProgress = false
      state.system = {
        ...state.system,
        [action.payload.key.replace('OpenAPI', '')]: Array.isArray(action.payload.data)
          ? convertArrayToObject(
              action.payload.data,
              'id'
            )
          : action.payload.data
      }
    },
    updateSystemData(_state: IData, _action: PayloadAction<Record<string, unknown>>) {
    },
    updateModificationCodeInSystem(state: IData, action: PayloadAction<{ id: string, code: string }>) {
      state.system = {
        ...state.system,
        modifications: {
          ...state.system.modifications,
          [action.payload.id]: {
            ...state.system.modifications?.[action.payload.id],
            code: action.payload.code,
          },
        },
      }
    }
  }
})

export const {
  setDataType,
  dataFetchWithFacets,
  dataFetchWithFacetsError,
  dataFetchWithFacetsSuccess,
  dataFetchSystemData,
  dataFetchSystemDataSuccess,
  dataFetchSystemDataError,
  dataSetSystemData,
  updateSystemData,
  updateModificationCodeInSystem
} = dataSlice.actions

export default dataSlice.reducer
