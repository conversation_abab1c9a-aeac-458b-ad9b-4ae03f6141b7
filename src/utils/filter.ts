import queryString from 'query-string'
import { SEARCH_FIELDS } from '../apiConstants'
import { getColumns } from '../components/config/columns'
import { IColumn } from '../components/Table/TableBlock'
import { FILTERS_CONFIG } from '../components/config/filters'
import { TLanguages } from '../locales'
import { isObjectEmpty } from '@aidsupply/components'
import { IFacets } from '../redux-saga/reducers/filters'

const SAME_GROUP_FILTER_SEPARATOR = '|'
const FILTER_GROUP_SEPARATOR = ','

export const getSearchValueApiQuery = (searchValue: string, type: string) => {
  const tableSearchField = SEARCH_FIELDS[type]

  return [`searchValue=${searchValue}`, `searchFields=${tableSearchField || 'translations'}`]
}

export const getSortParams = (sortQuery: string, type: string, lng: TLanguages) => {
  const sortArr = sortQuery.split('_')
  const sortOrder = sortArr[1] || 'asc'
  const apiSortValue = ((getColumns(type, lng) as IColumn[]) || ([] as IColumn[])).find(
    (col) => col?.key === sortArr[0]
  )?.dataKey

  return {
    value: { key: sortArr[0], order: sortOrder },
    apiSortString: `sort=${apiSortValue}_${sortOrder}`,
  }
}

export const mapParamsToUrl = (
  type: string,
  search: string,
  lng: TLanguages,
  filters?: Record<string, string[]>,
  facets?: Record<string, IFacets[]>
) => {
  let apiQueryPairs: string[]
  const urlQueryPairs: string[] = []
  const urlFilterPairs: string[] = []
  let apiQuery: { key: string; value: string }[] = []
  let urlQuery = ''
  const searchParsed = queryString.parse(search)

  if (searchParsed?.searchValue) {
    urlQueryPairs.push(`searchValue=${searchParsed?.searchValue}`)
    apiQueryPairs = getSearchValueApiQuery(searchParsed.searchValue as string, type)
  } else {
    apiQueryPairs = []
  }

  if (searchParsed?.sort) {
    urlQueryPairs.push(`sort=${searchParsed?.sort}`)
    const { apiSortString } = getSortParams(searchParsed?.sort as string, type, lng)
    apiQueryPairs.push(apiSortString)
  }

  // filters
  if (filters && !isObjectEmpty(filters)) {
    Object.keys(filters).forEach((key) => {
      const value = filters?.[key].join(FILTER_GROUP_SEPARATOR)
      const valueBySlug = filters?.[key].map((filter) => {
        const facetValue = facets?.[key] && facets[key].find((facet) => facet.id === filter)
        return facetValue?.slug || facetValue?.id || filter
      })

      if (value && value.length > 0) {
        apiQueryPairs.push(`${FILTERS_CONFIG[key]?.apiKey}=${value}`)
        urlFilterPairs.push(`${key}=${valueBySlug.join(SAME_GROUP_FILTER_SEPARATOR)}`)
      }
    })
  }

  if (apiQueryPairs.length > 0) {
    const hasActiveFilters = urlFilterPairs.length
    if (hasActiveFilters) {
      urlQueryPairs.push(urlFilterPairs.join(FILTER_GROUP_SEPARATOR))
    }

    apiQuery = apiQueryPairs.map((pair) => {
      const pairSplit = pair.split('=')
      return { key: pairSplit[0], value: pairSplit[1] }
    })
    urlQuery = `${urlQueryPairs.join('&')}`
  }

  return { apiQuery: apiQuery || [], urlQuery }
}

export const mapUrlToParams = (
  search: string,
  type: string,
  systemData: Record<string, unknown>,
  lng: TLanguages
) => {
  const filters: Record<string, string[]> = {}
  let apiQueryPairs: string[]
  let apiQuery: { key: string; value: string }[] = []
  let searchValue
  let sortValue

  const match = search.match(/searchValue=([^&]*)/)
  let matchValue = match ? match[1] : ''
  if (matchValue.includes('%')) {
    matchValue = decodeURIComponent(matchValue)
  }
  const encodedValue = encodeURIComponent(matchValue)
  const query = queryString.stringify({ searchValue: encodedValue })
  const searchParsed = { ...queryString.parse(search), ...queryString.parse(query) }

  const firstFilterUrlKey =
    searchParsed &&
    Object.keys(searchParsed).filter(
      (key) => key !== 'sort' && key !== 'searchValue' && key !== 'refresh' && key !== 'page'
    )[0]

  const firstFilterConfigObj = FILTERS_CONFIG[firstFilterUrlKey]

  // search
  if (searchParsed?.searchValue) {
    searchValue = searchParsed.searchValue?.toString() || ''
    apiQueryPairs = getSearchValueApiQuery(searchValue, type)
  } else {
    apiQueryPairs = []
  }

  // sorting
  if (searchParsed?.sort) {
    const sort = searchParsed.sort?.toString() || ''
    const { value, apiSortString } = getSortParams(sort, type, lng)
    sortValue = value
    apiQueryPairs.push(apiSortString)
  }

  // filters
  if (search && firstFilterConfigObj) {
    const urlFilters = `${firstFilterUrlKey}=${searchParsed[firstFilterUrlKey]}`
    const urlQueryPairs = urlFilters.split(FILTER_GROUP_SEPARATOR)
    urlQueryPairs.forEach((pair) => {
      const [key, slugValue] = pair.split('=')

      const systemDataKey =
        FILTERS_CONFIG[key]?.systemDataKeyMapping?.[type] || FILTERS_CONFIG[key]?.key || key

      const idValueArray = slugValue
        .split(SAME_GROUP_FILTER_SEPARATOR)
        .map(
          (slug) =>
            Object.values(systemData[systemDataKey] || FILTERS_CONFIG[key].options || []).find(
              (systemItem) =>
                systemItem?.slug === slug ||
                systemItem?.key === slug ||
                systemItem?.id === slug ||
                systemItem?.id === +slug
            )?.id || slug
        )
        .filter((id) => id)

      filters[key] = idValueArray

      apiQueryPairs.push(
        `${FILTERS_CONFIG[key]?.apiKey || key}=${
          idValueArray?.length ? idValueArray.join(SAME_GROUP_FILTER_SEPARATOR) : slugValue
        }`
      )
    })
  }

  if (apiQueryPairs.length > 0) {
    apiQuery = apiQueryPairs.map((pair) => {
      const pairSplit = pair.split('=')
      return { key: pairSplit[0], value: pairSplit[1] }
    })
  }

  return { filters, apiQuery: apiQuery || [], searchValue, sortValue }
}
