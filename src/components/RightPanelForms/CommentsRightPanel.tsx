import Comments from '../OrderForm/Comments'

const containerStyles: Record<string, string | Record<string, string>> = {
  position: 'relative',
  overflowY: 'auto',
  maxHeight: 'calc(100vh - 100px)',
  '@media (min-width: 550px) and (max-width: 1023px)': {
    maxHeight: 'calc(100vh - 200px)',
  },
  '@media (min-width: 1024px)': {
    maxHeight: 'calc(100vh - 121px)',
  },
}

const inputStyles: Record<string, string | number> = {
  position: 'sticky',
  bottom: '0px',
  backgroundColor: '#fff',
  zIndex: 3,
}

const CommentsRightPanel = () => {
  return <Comments showAll={true} containerStyles={containerStyles} inputStyles={inputStyles} />
}

export default CommentsRightPanel
