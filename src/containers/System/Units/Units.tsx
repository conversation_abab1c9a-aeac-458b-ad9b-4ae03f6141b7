import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import UnitsTableCard from './UnitsTableCard'

const Units = () => {

  return (
    <GridLayout rightPanelComponent={<Outlet context={{}} />}>
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={UnitsTableCard as unknown as React.JSX.Element}
        tableCardHeight={136}
        iconName='ruler'
      />
    </GridLayout>
  )
}

export default Units
