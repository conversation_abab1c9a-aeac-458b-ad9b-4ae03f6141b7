import { FORM_TABLE_OPTIONS } from '../../data'
import { IOptionData } from '../Form/interfaces'

export const FRONTEND_TABLE_ACTIONS: Record<string, { sort?: boolean; search: boolean }> = {
  'stock-items': { search: false },
  'orders.items': { sort: true, search: false },
  'invoices.order_items': { sort: true, search: false },
  'shipments.shipment_items': { sort: true, search: false },
  'items.modifications': { sort: true, search: false },
  'inventories.inventory_items': { sort: true, search: false },
}

export const STATES_ICONS: Record<string, string> = {
  drafted: 'stateDrafted',
  posted: 'statePosted',
  deleted: 'stateDeleted',
}

export const STATES_OPTIONS: Record<string, IOptionData> = {
  drafted: {
    id: 'drafted',
    label: {
      en: 'Draft',
      uk: 'Чернетка',
    },
    iconName: STATES_ICONS.drafted,
  },
  posted: {
    id: 'posted',
    label: { en: 'Posted', uk: 'Опубліковано' },
    iconName: STATES_ICONS.posted,
  },
  deleted: {
    id: 'deleted',
    label: { en: 'Deleted', uk: 'Видалено' },
    iconName: STATES_ICONS.deleted,
  },
}

export const STATUSES_ICONS: Record<string, string> = {
  new: 'statusNew',
  paid: 'paid',
  shipped: 'shipped',
  completed: 'completed',
  canceled: 'canceled',
  in_progress: 'inProgress',
  pending: 'pending',
  active: 'completed',
  inactive: 'canceled',
  delivered: 'completed',
}

export const ORG_OR_USER_STATUSES_ICONS: Record<string, string> = {
  pending: 'pending',
  active: 'completed',
  inactive: 'canceled',
}

export const STATUSES_OPTIONS: Record<string, IOptionData> = {
  new: {
    id: 'new',
    label: {
      en: 'Created',
      uk: 'Створено',
    },
    iconName: STATUSES_ICONS.new,
  },
  confirmed: {
    id: 'confirmed',
    label: { en: 'Confirmed', uk: 'Підтверджено' },
    iconName: STATUSES_ICONS.confirmed,
  },
  paid: { id: 'paid', label: { en: 'Paid', uk: 'Сплачено' }, iconName: STATUSES_ICONS.paid },
  shipped: {
    id: 'shipped',
    label: {
      en: 'Shipped',
      uk: 'Відправлено',
    },
    iconName: STATUSES_ICONS.shipped,
  },
  completed: {
    id: 'completed',
    label: { en: 'Completed', uk: 'Завершено' },
    iconName: STATUSES_ICONS.completed,
  },
  delivered: {
    id: 'delivered',
    label: { en: 'delivered', uk: 'Доставлено' },
    iconName: STATUSES_ICONS.delivered,
  },
  canceled: {
    id: 'canceled',
    label: { en: 'Canceled', uk: 'Скасовано' },
    iconName: STATUSES_ICONS.canceled,
  },
  in_progress: {
    id: 'in_progress',
    label: { en: 'In progress', uk: 'В роботі' },
    iconName: STATUSES_ICONS.in_progress,
  },
  pending: {
    id: 'pending',
    label: { en: 'Pending', uk: 'Очікує' },
    iconName: STATUSES_ICONS.pending,
  },
}

export const ORG_OR_USER_STATUSES_OPTIONS: Record<string, IOptionData> = {
  pending: {
    id: 'pending',
    label: {
      en: 'Pending',
      uk: 'Очікує підтвердження',
    },
    iconName: ORG_OR_USER_STATUSES_ICONS.pending,
  },
  active: {
    id: 'active',
    label: {
      en: 'Active',
      uk: 'Активний',
    },
    iconName: ORG_OR_USER_STATUSES_ICONS.active,
  },
  inactive: {
    id: 'inactive',
    label: {
      en: 'Inactive',
      uk: 'Неактивний',
    },
    iconName: ORG_OR_USER_STATUSES_ICONS.inactive,
  },
}

export const CAMPAIGNS_STATUSES_OPTIONS: Record<string, IOptionData> = {
  in_progress: STATUSES_OPTIONS.in_progress,
  completed: STATUSES_OPTIONS.completed,
  canceled: STATUSES_OPTIONS.canceled,
}

export const INQUIRY_STATUSES_OPTIONS: Record<string, IOptionData> = {
  in_progress: STATUSES_OPTIONS.in_progress,
  completed: STATUSES_OPTIONS.completed,
  canceled: STATUSES_OPTIONS.canceled,
}

export const INVENTORY_STATUSES_OPTIONS: Record<string, IOptionData> = {
  in_progress: STATUSES_OPTIONS.in_progress,
  completed: STATUSES_OPTIONS.completed,
  canceled: STATUSES_OPTIONS.canceled,
}

export const INVOICE_STATUSES_OPTIONS: Record<string, IOptionData> = {
  pending: STATUSES_OPTIONS.pending,
  paid: STATUSES_OPTIONS.paid,
  completed: STATUSES_OPTIONS.completed,
  canceled: STATUSES_OPTIONS.canceled,
}

export const ORDER_STATUSES_OPTIONS: Record<string, IOptionData> = {
  in_progress: STATUSES_OPTIONS.in_progress,
  completed: STATUSES_OPTIONS.completed,
  canceled: STATUSES_OPTIONS.canceled,
}

export const SHIPMENT_STATUSES_OPTIONS: Record<string, IOptionData> = {
  pending: STATUSES_OPTIONS.pending,
  shipped: STATUSES_OPTIONS.shipped,
  delivered: STATUSES_OPTIONS.delivered,
  canceled: STATUSES_OPTIONS.canceled,
}

export const TABLE_MODES_BY_TYPES: Record<string, Array<'table' | 'cards'>> = {
  items: ['table', 'cards'],
  brands: ['table', 'cards'],
  orders: ['table', 'cards'],
  'stock-items': ['table', 'cards'],
  warehouses: ['table', 'cards'],
  users: ['table', 'cards'],
  categories: ['table', 'cards'],
  products: ['cards'],
  inquiry_items: ['cards'],
  inquiries: ['table', 'cards'],
  invoices: ['table', 'cards'],
  shipments: ['table', 'cards'],
  posts: ['table', 'cards'],
  reports: ['table', 'cards'],
  campaigns: ['table', 'cards'],
  faqs: ['table', 'cards'],
  organizations: ['table', 'cards'],
  attributes: ['table', 'cards'],
  units: ['table', 'cards'],
  pages: ['table', 'cards'],
  inventories: ['table', 'cards'],
}

export const SUPERVISION_STATUSES_OPTIONS = {
  pending: { ...ORG_OR_USER_STATUSES_OPTIONS.pending },
  confirmed: {
    id: 'confirmed',
    label: { en: 'Confirmed', uk: 'Підтверджено' },
    iconName: STATUSES_ICONS.confirmed,
  },
  revoked: {
    id: 'revoked',
    label: { en: 'Revoked', uk: 'Відкликано' },
    iconName: STATUSES_ICONS.revoked,
  },
}

const TWO_STATES_ONLY = Object.values(STATES_OPTIONS).filter((opt) => opt.id !== 'drafted')

export const TABLE_BULK_OPERATIONS = {
  orderItems: FORM_TABLE_OPTIONS,
  enumOptions: FORM_TABLE_OPTIONS,
  categories: TWO_STATES_ONLY,
  banners: TWO_STATES_ONLY,
  'organizations.supervised_organizations': Object.values(SUPERVISION_STATUSES_OPTIONS),
}

export const TYPES_WITHOUT_STATE = ['users', 'organizations', 'organizations.supervised_organizations']
