import { CURRENCY, ISSUER, ORDER_WITH_DATE, PAYER, STATE } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'

export const invoicesRightPanelConfig = (mode: 'create' | 'edit'): IFormConfig => {
  return {
    withTabs: ['translations'],
    validationRules: {
      currency_id: ['required'],
      issuer_id: ['required'],
      payer_id: ['required'],
      subtotal: ['required'],
    },
    fields: {
      general: [
        {
          ...STATE,
          getIsHidden: (formValues) => !formValues?.id,
        },
        {
          ...ORDER_WITH_DATE,
          getDisabled: (formValues) => formValues?.id,
          onSelectValueChange: (
            val: string | Record<string, unknown>,
            setValuesChanged?: (prev: Record<string, unknown>) => Record<string, unknown>) => {
            // @ts-ignore
            setValuesChanged((prev) => ({
              ...prev,
              // @ts-ignore§
              subtotal: val?.total - (val?.tax || 0),
              // @ts-ignore§
              tax: val?.tax,
              // @ts-ignore§
              payer_id: val?.recipient_id || '',
            }))
          },
        },
        ISSUER,
        PAYER,
        CURRENCY,
        {
          key: 'subtotal',
          label: 'subtotal',
          type: 'number',
          hideButtons: true,
          required: true,
        },
        {
          key: 'tax',
          label: 'tax',
          hideButtons: true,
          type: 'number',
        }
      ],
      ...(mode === 'edit' && {
        invoice_items: [],
        activity: [],
        notes: [],
      })
    }
  }
}
