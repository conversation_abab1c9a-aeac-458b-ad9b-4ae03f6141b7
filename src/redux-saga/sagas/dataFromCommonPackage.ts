import { all, call, put, select } from 'redux-saga/effects'
import { isObjectEmpty, getFilter<PERSON>abe<PERSON>, getFilterSlug } from '@aidsupply/components'
import { FILTERS_CONFIG, FILTERS_KEY_MAPPINGS } from '../../components/config/filters'
import { buildRequest, fetchData } from '../../api'
import { CRM_DATA_TYPES_WITH_NO_FACETS } from '../../apiConstants'
import {
  dataFetchSystemData,
  dataFetchSystemDataError,
  dataFetchSystemDataSuccess,
  dataSetSystemData,
} from '../reducers/data'
import { mapStateToProps, selectAllSystemCollections } from '../selectors'
import { IResponse } from './auth'
import { filtersSetFacets, IFacets } from '../reducers/filters'

function* readAPIData({
  queryParams,
  type,
}: {
  queryParams: { key: string; value: string | number }[]
  type: string
}) {
  try {
    const request: Request = yield buildRequest({
      operation: 'read',
      type,
      queryParams,
    })
    const { items } = yield fetchData(request)

    return { type, items }
  } catch (error) {
    return { error }
  }
}

function* readSystemData(systemDataKeysToRead: string[], constantsData: Record<string, unknown>) {
  const collections = systemDataKeysToRead
  const system: Record<string, unknown> = {}
  let collectionsToFetch: string[] = []

  const systemDataState: Record<string, unknown> = yield select(selectAllSystemCollections)

  if (collections === undefined || collections.length === 0) {
    yield put(dataFetchSystemDataSuccess())

    return
  }

  if (typeof window !== 'undefined') {
    collections.forEach((collection) => {
      if (constantsData && constantsData[collection]) {
        system[collection] = constantsData[collection]
      } else if (!systemDataState[collection]) {
        collectionsToFetch.push(collection)
      }
    })
  }

  if (collectionsToFetch.length > 0) {
    yield put(dataFetchSystemData())

    const result: {
      type: string
      items: unknown[]
    }[] = yield all(
      collectionsToFetch.map((collection) => {
        const queryParams = [{ key: 'limit', value: 100000 }]

        return call(readAPIData, {
          type: collection,
          queryParams,
        })
      })
    )

    if (result.findIndex((res: IResponse) => res.error) !== -1) {
      yield put(dataFetchSystemDataError())
    }

    result
      .filter((collection) => !!collection)
      .forEach(({ type, items }) => {
        if (items) {
          system[type] = items
        }
      })
  }

  try {
    yield all(
      Object.keys(system).map((key) => {
        return put(dataSetSystemData({ key, data: system[key] }))
      })
    )

    yield put(dataFetchSystemDataSuccess())
  } catch (error) {
    console.log(error)
    yield put(dataFetchSystemDataError())
  }
}

function* transformApiFacets(
  facetsFromApi: Record<string, unknown[]>,
  facetsToInclude?: Record<string, unknown>
) {
  const { data } = yield select(mapStateToProps)
  let facets: Record<string, IFacets[]>

  // Filter all facetsFromApi if facetsToInclude exists
  if (!facetsToInclude || !facetsFromApi) {
    facets =
      Object.keys(facetsFromApi)?.reduce((acc, curr) => {
        const mappedCurr = FILTERS_KEY_MAPPINGS[curr] || curr

        return { ...acc, [mappedCurr]: facetsFromApi[curr] }
      }, {}) || {}
  } else {
    facets = Object.keys(facetsFromApi)?.reduce((acc, curr) => {
      const mappedCurr = FILTERS_KEY_MAPPINGS[curr] || curr

      if (facetsToInclude[mappedCurr]) {
        return { ...acc, [mappedCurr]: facetsFromApi[curr] }
      }

      return acc
    }, {})
  }

  // Add labels to filters
  Object.keys(facets).forEach((key) => {
    facets[key].forEach((val, idx) => {
      const reduxDataKey =
        FILTERS_CONFIG[key]?.systemDataKeyMapping?.[data.type.key] || FILTERS_CONFIG[key]?.key || key

      const allSupportData = data.system[reduxDataKey] || {}

      facets[key][idx] = {
        ...val,
        label: getFilterLabels(FILTERS_CONFIG[key]?.options || allSupportData, val.id),
        slug: getFilterSlug(FILTERS_CONFIG[key]?.options || allSupportData, val.id),
      }
    })
  })

  return facets
}

function* fetchFacets({
  type,
  queryParams,
  facetsToInclude,
  apiUrlParam,
}: {
  type: string
  queryParams: {
    key: string
    value: string | number
  }[]
  facetsToInclude?: Record<string, unknown>
  apiUrlParam?: string
}) {
  if (CRM_DATA_TYPES_WITH_NO_FACETS.includes(type)) {
    return
  }

  try {
    const requestFacets: Request = yield buildRequest({
      operation: 'read',
      withFacets: true,
      type,
      queryParams,
      apiUrlParam,
    })

    let { facets } = yield fetchData(requestFacets)

    if (facets && !isObjectEmpty(facets)) {
      const facetsWithLabels: Record<string, unknown[]> = yield transformApiFacets(facets, facetsToInclude)

      yield put(filtersSetFacets({ facets: facetsWithLabels }))
    }
  } catch (error) {
    console.log(error)
  }
}

export { readSystemData, fetchFacets }
