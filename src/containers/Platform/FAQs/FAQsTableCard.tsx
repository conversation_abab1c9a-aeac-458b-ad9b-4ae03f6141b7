import { useTranslation } from 'react-i18next'
import { Typography } from '@aidsupply/components'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty/ReactTexty'
import { IFAQ } from '../../../commonInterfaces'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { TLanguages } from '../../../locales'
import { StyledStateWrapper } from '../styled'

interface IFAQsTableCard {
  data: IFAQ
  className?: string
}

const FAQsTableCard = ({ data, className }: IFAQsTableCard) => {
  const { t, i18n: { language: lng }  } = useTranslation('table')
  const { id, state, organization_id, translations } = data

  return (
    <TableCardStyled key={id} direction='column' gap={1} className={className}>
      <StyledStateWrapper>
        <Typography as={ReactTexty} type="h4">
          {translations?.[lng as TLanguages] || translations?.en || ''}
        </Typography>
        {state && <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />}
      </StyledStateWrapper>
        <Typography as={ReactTexty} type="button2" fontWeight="400">
          {organization_id || ''}
        </Typography>
    </TableCardStyled>
  )
}

export default FAQsTableCard
