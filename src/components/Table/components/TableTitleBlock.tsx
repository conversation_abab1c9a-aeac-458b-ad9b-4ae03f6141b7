import { useTranslation } from 'react-i18next'
import { Badge, FlexRow, Typography, HeaderMenuIcon } from '@aidsupply/components'
import { useTheme } from 'styled-components'
import { useMappedState } from '../../../hooks'
import { selectTableDataCount } from '../../../redux-saga/selectors'
import { StyledTableTitleRow } from './styles'

interface ITableTitleBlock {
  title?: string
  titleKey: string
  iconName: string
  withMenuIcon: boolean
  withBadge?: boolean
  children?: React.ReactNode
}

const TableTitleBlock = ({
  title,
  titleKey,
  iconName,
  withMenuIcon = true,
  withBadge = true,
  children
}: ITableTitleBlock) => {
  const { t } = useTranslation('menu')
  const theme = useTheme()
  const totalItems = useMappedState(selectTableDataCount)

  return (
    <FlexRow className="tableTitleRow">
      <StyledTableTitleRow>
        {withMenuIcon && <HeaderMenuIcon className="menuIcon"
          width={20}
          fill={theme.color.general.gray5}
          iconName={iconName} />
        }
        <Typography type="h3">{title || t(`menu:${titleKey}`)}</Typography>
        {withBadge && <Badge returnBadgeOnly badgeContent={totalItems || 0} className="itemsCountBadge" />}
      </StyledTableTitleRow>
      {children}
    </FlexRow>
  )
}

export default TableTitleBlock