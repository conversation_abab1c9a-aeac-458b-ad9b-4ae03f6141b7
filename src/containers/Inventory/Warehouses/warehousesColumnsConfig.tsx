import { getSelectCellValue } from '../../../components/config/columns'
import AddressTableCell from '../../../components/Table/components/AddressTableCell'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getOrganization } from '../../../utils/columns'
import { getNameByMode, headerRenderer } from '../../../utils/table'

export const warehousesColumnsConfig = (lng: TLanguages, t: (key: string) => string, tableMode?: TableModeType) => {
  const isSortable = true
  return [
    getNameByMode({
      t,
      dataKey: 'name',
      option: 'state',
      tableMode: tableMode || 'table',
      lng
    }),
    {
      key: 'items',
      dataKey: 'stocks_count',
      sortable: true,
      width: 80,
      headerRenderer: headerRenderer(t, tableMode),
    },
    {
      key: 'currency',
      optionsKeys: ['currencies'],
      dataKey: 'currency_id',
      labelKey: 'code',
      valueKey: 'id',
      sortable: true,
      noTranslation: true,
      width: 80,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: getSelectCellValue,
    },
    {
      key: 'type',
      dataKey: 'type',
      optionsKeys: ['warehouseTypes'],
      isMulti: true,
      labelKey: 'label',
      sortable: true,
      width: 0,
      flexGrow: 1.5,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: getSelectCellValue,
    },
    {
      key: 'address',
      labelKey: `translations.${lng}`,
      dataKey: 'city',
      width: 0,
      flexGrow: 1.5,
      sortable: true,
      headerRenderer: headerRenderer(t, tableMode),
      cellRenderer: ({ rowData }: { rowData: Record<string, unknown> }) => {
        return <AddressTableCell rowData={rowData} />
      },
    },
    getOrganization(t, isSortable, tableMode),
  ]
}
