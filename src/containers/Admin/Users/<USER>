import { getDate } from '../../../components/config/columns'
import { TableModeType } from '../../../redux-saga/reducers/data'
import { getOrganization } from '../../../utils/columns'
import { getNameByMode, headerRenderer } from '../../../utils/table'

export const usersColumnsConfig = (t: (key: string) => string, tableMode?: TableModeType) => {
  const withRoles = true
  const isSortable = false
  return [
    getNameByMode({
      t,
      dataKey: 'full_name',
      option: 'status',
      key: 'fullName',
      tableMode: tableMode || 'table'
    }),
    {
      key: 'email',
      title: 'E-mail',
      dataKey: 'email',
      sortable: true,
      width: 0,
      flexGrow: 2,

      headerRenderer: headerRenderer(t, tableMode),
    },
    getOrganization(t, isSortable, tableMode || 'table', withRoles),
    getDate(t, tableMode),
    {
      ...getDate(t, tableMode),
      key: 'lastLoggedIn',
      title: 'Last login',
      dataKey: 'last_logged_at',
      width: 0,
      flexGrow: 1,
    },
  ] 
}
