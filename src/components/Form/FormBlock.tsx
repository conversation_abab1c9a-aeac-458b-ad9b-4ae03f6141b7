import { useState } from 'react'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
import { Typography } from '@aidsupply/components'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import { COLLAPSED_FORM_BLOCKS } from '../../constants'
import { EDITABLE_TABLE_NAVBAR_FORM_CONFIG } from '../config/editableTableNavBarForms'
import InfoPanel from '../InfoPanel'
import FormBlockTabs, { DEFAULT_TABS } from './FormBlockTabs'
import FormFields from './FormFields'
import { IFieldsProps, IFormBlockProps } from './interfaces'
import { StyledExpansionPanel } from './styled'
import { selectIsEveryFieldHidden } from '../../redux-saga/selectors.ts'
import { useMappedState } from '../../hooks'
import FormBlockPanelHeader from './FormBlockPanelHeader.tsx'

const INFO_PANEL_CONFIG: Record<
  string,
  Record<
    string,
    {
      initiallyOpened: boolean
      props: Record<string, unknown>
    }
  >
> = {
  templates: {
    translations: {
      initiallyOpened: true,
      props: {
        children: '',
        text: '%USERNAME%, %DOCUMENT_ID%, %DOCUMENT_TYPE%, %DOCUMENT_STATE_PREV%, %DOCUMENT_STATE_NEXT%',
        linkText: '',
        linkTo: '',
      },
    },
  },
}

const FormBlock = (props: IFormBlockProps) => {
  const {
    blockKey,
    fields,
    formErrors,
    formSubmit,
    formConfig,
    FormWrapper,
    id,
    initialValues,
    isReadOnly,
    isSecondaryBlock,
    isSubmitOnBlur,
    labelKey,
    labelType,
    optionsData,
    touchedFields,
    type,
    setTouchedFields,
    submitByEnterPressed,
    updateCheckbox,
    updateInput,
    updateSelect,
    validateField,
    expansionPanelProps,
    validationRules,
  } = props
  const { t, i18n } = useTranslation(['forms', 'menu', 'validation', 'table'])

  const isEveryFieldHidden = useMappedState(
    selectIsEveryFieldHidden(type, Array.isArray(fields) ? fields : Object.values(fields), optionsData)
  )

  const withBlockTabs =
    Object.keys(DEFAULT_TABS).includes(blockKey as string) || formConfig?.withTabs?.includes(blockKey as string)

  const InfoPanelConfig = INFO_PANEL_CONFIG[type as string]?.[blockKey as string]

  const withInfo = !!InfoPanelConfig?.props

  const [infoOpened, setInfoOpened] = useState(InfoPanelConfig?.initiallyOpened)

  const onToggleInfo = (event: InputEvent) => {
    event.stopPropagation()
    setInfoOpened((prev) => !prev)
  }

  const isFormWithOneBlockOnly = !blockKey

  const formFieldsProps = {
    fields,
    formErrors,
    formSubmit,
    initialValues,
    isReadOnly,
    isSubmitOnBlur,
    labelKey,
    labelType,
    optionsData,
    submitByEnterPressed,
    type,
    updateCheckbox,
    updateInput,
    updateSelect,
    sidebarFormId: id,
    validateField,
    validationRules,
  }

  const compoundTypeKey = `${type}.${blockKey}`
  const editableTableConfig =
    EDITABLE_TABLE_NAVBAR_FORM_CONFIG[type as string] || EDITABLE_TABLE_NAVBAR_FORM_CONFIG[compoundTypeKey]

  const customBlockValues = undefined
  // (Array.isArray(fields) ? false : (fields as IFieldsProps)?.getBlockValuesByCustomRule?.(formValues, optionsData)) ||
  // (editableTableConfig as IFieldsProps)?.getBlockValuesByCustomRule?.(formValues, optionsData)

  const getIsBlockShown = () => {
    let isShown = true
    if (isReadOnly || ['recipient', 'client'].includes((optionsData?.user as ICurrentUser)?.role as string)) {
      isShown = blockKey !== 'admin'
    }

    if ((blockKey === 'comments' || blockKey === 'activity') && !id) {
      isShown = false
    }

    if (isShown) {
      if (fields && Array.isArray(fields) && fields.some((field) => !!field.getIsHidden) && isEveryFieldHidden) {
        isShown = false
      }
    }

    return isShown
  }

  const isBlockShown =
    (fields as IFieldsProps)?.getBlockValuesByCustomRule ||
    (editableTableConfig as IFieldsProps)?.getBlockValuesByCustomRule
      ? customBlockValues
      : getIsBlockShown()

  const panelProps = {
    isHidden:
      isFormWithOneBlockOnly || blockKey === 'noTitle' || blockKey === 'info' || blockKey === 'statusWithActions',
    ...expansionPanelProps,
  }

  const getFields = () => {
    if (!isBlockShown) {
      return null
    }

    return Array.isArray(fields) ? (
      <FormFields {...formFieldsProps} fields={fields} />
    ) : (
      Object.values(fields as unknown as Record<string, IFieldsProps[]>).map((values, i) => {
        const key = Object.keys(fields as unknown as Record<string, IFieldsProps[]>)[i]

        return Array.isArray(values) ? (
          <div key={i}>
            <Typography type="h4" text={t(key)} margin="0 0 10px 0" />
            <FormFields {...formFieldsProps} fields={values} />
          </div>
        ) : null
      })
    )
  }

  return FormWrapper ? (
    <StyledExpansionPanel
      key={blockKey}
      type="light"
      header={
        <FormBlockPanelHeader
          blockKey={blockKey}
          i18n={i18n}
          onToggleInfo={onToggleInfo}
          t={t}
          type={type}
          withInfo={withInfo}
        />
      }
      size="small"
      initialOpened={
        !COLLAPSED_FORM_BLOCKS.includes(blockKey) ||
        (blockKey === 'admin' &&
          ['operator', 'platform-admin'].includes((optionsData?.user as ICurrentUser)?.role as string))
      }
      id={blockKey}
      className={clsx(
        'singleFormTitle',
        blockKey,
        !!editableTableConfig && 'isEditableTable',
        !isBlockShown && 'hidden'
      )}
      {...panelProps}
    >
      {withInfo && infoOpened && <InfoPanel {...InfoPanelConfig?.props} />}
      {/* @ts-ignore */}
      <FormWrapper
        isBlockShown={isBlockShown}
        isReadOnly={isReadOnly}
        blockKey={blockKey}
        compoundTypeKey={compoundTypeKey}
        customBlockValues={customBlockValues}
        editableTableConfig={editableTableConfig}
        fields={fields}
        formFieldsProps={formFieldsProps}
        id={id}
        initialValues={initialValues}
        // getPanelHeader={getPanelHeader}
        optionsData={optionsData}
        setTouchedFields={setTouchedFields}
        touchedFields={touchedFields}
        withInfo={!isSecondaryBlock}
        updateCheckbox={updateCheckbox}
        updateInput={updateInput}
        updateSelect={updateSelect}
      >
        {withBlockTabs ? (
          <FormBlockTabs formFieldsProps={formFieldsProps} blockKey={blockKey} formConfig={formConfig} type={type} />
        ) : (
          getFields()
        )}
      </FormWrapper>
    </StyledExpansionPanel>
  ) : (
    <FormFields {...formFieldsProps} fields={fields} formConfig={formConfig} />
  )
}

export default FormBlock
