import { useDispatch } from 'react-redux'
import { useParams } from 'react-router'
import { Dispatch, SetStateAction, useContext, useEffect } from 'react'
import { isObjectEmpty, ScreenContext } from '@aidsupply/components'
import { toggleRightPanelExtended } from '../../redux-saga/reducers/common'
import { selectFiltersState, selectIsLeftPanelExtended, selectIsRightPanelExtended } from '../../redux-saga/selectors'
import { useMappedState } from '../../hooks'
import GridLayoutDesktop from './GridLayoutDesktop'
import GridLayoutMobile from './GridLayoutMobile'
import LeftFiltersPanel from './LeftFiltersPanel'

export interface IGridLayout {
  children: React.ReactNode
  rightPanelComponent?: React.ReactNode
  leftPanelComponent?: React.ReactNode
  isRightPanelExtended?: boolean
  handleToggleRightPanel?: Dispatch<SetStateAction<boolean>>
  isLeftPanelExtended?: boolean
  isIconRightPanelWide?: boolean
  className?: string
}

const GridLayout = ({children, rightPanelComponent, isIconRightPanelWide, className}: IGridLayout) => {
  const dispatch = useDispatch()
  const { width, md } = useContext(ScreenContext)
  const params = useParams()
  const { rightPanelId } = params
  const filters = useMappedState(selectFiltersState)

  const showFilterPanel = !isObjectEmpty(filters.facets) || !isObjectEmpty(filters.active)

  const isRightPanelExtended = useMappedState(selectIsRightPanelExtended) as boolean

  const isLeftPanelExtended = useMappedState(selectIsLeftPanelExtended) as boolean  

  const handleToggleRightPanel = () => {
    dispatch(toggleRightPanelExtended(!isRightPanelExtended))
  }

  useEffect(() => {
    dispatch(toggleRightPanelExtended(!!rightPanelId))
  }, [rightPanelId])

  const rightPanelsProps = {
    isRightPanelExtended,
    isIconRightPanelWide,
    handleToggleRightPanel
  }

  const leftPanelsProps = {
    isLeftPanelExtended,
  }

  if (width <= md) {
    return(
      <GridLayoutMobile
        rightPanelComponent={isRightPanelExtended && rightPanelComponent}
        {...rightPanelsProps}
        leftPanelComponent={isLeftPanelExtended && (showFilterPanel && <LeftFiltersPanel />)}
        {...leftPanelsProps}
      >
        {children}
      </GridLayoutMobile>
    )
  }

  return(
    <GridLayoutDesktop
      rightPanelComponent={isRightPanelExtended && rightPanelComponent}
      {...rightPanelsProps}
      leftPanelComponent={isLeftPanelExtended && (showFilterPanel && <LeftFiltersPanel />)
      }
      {...leftPanelsProps}
      className={className}
    >
      {children}
    </GridLayoutDesktop>
  )
};

export default GridLayout
