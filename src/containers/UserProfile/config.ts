export const userProfileConfig: Record<string, unknown>[] = [
  {
    key: 'password',
    label: 'currentPassword',
    variant: 'secondary',
    placeholder: 'password',
    className: 'formItem',
    labelType: 'top',
    type: 'password',
    required: true,
  },
  {
    key: 'new_password',
    label: 'password',
    variant: 'secondary',
    type: 'password',
    labelType: 'top',
    className: 'formItem',
    required: true,
  },
  {
    label: 'fullName',
    key: 'full_name',
    variant: 'secondary',
    labelType: 'top',
    className: 'formItem',
    required: true
  },
  {
    label: 'email',
    key: 'email',
    labelType: 'top',
    placeholder: '<EMAIL>',
    className: 'formItem',
    disabled: true,
  }
]
