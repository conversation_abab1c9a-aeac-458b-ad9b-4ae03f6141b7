import { useTheme } from 'styled-components'
import { useTranslation } from 'react-i18next'
import { Typography, Userpic } from '@aidsupply/components'
import { IMAGEKIT_URL } from '../../../constants'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { StateType } from '../../../components/Form/interfaces'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StyledContentWrapper, StyledStateWrapper } from './styled'

interface IBrandsTableCard {
  id: number
  name: string
  photo_url: string
  website: string
  state: StateType
}

const BrandsTableCard = ({ data, className }: {data: IBrandsTableCard, className?: string}) => {
  const { t } = useTranslation('table')
  const theme = useTheme()
  const { name, photo_url, state, website, id } = data

  return (
    <TableCardStyled key={id} className={className}>
      <>
        <Userpic
          height="46px"
          width="46px"
          imagekitUrl={IMAGEKIT_URL}
          src={photo_url}
          borderColor={theme.color.general.gray1}
          fullName={name}
        />
        <StyledContentWrapper>
          <Typography as={ReactTexty} type="h4">
            {name}
          </Typography>
          <Typography as={ReactTexty} type="body1" color={theme.color.primary.main}>
            {website}
          </Typography>
        </StyledContentWrapper>
        <StyledStateWrapper>
          <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />
        </StyledStateWrapper>
      </>
    </TableCardStyled>
  )
}

export default BrandsTableCard
