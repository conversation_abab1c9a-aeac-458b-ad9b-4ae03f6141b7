import { Outlet } from 'react-router'

import GridLayout from '../../Layout/GridLayout'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import OrdersTableCard from './OrdersTableCard'

const Orders = () => {

  return (
    <GridLayout
      isIconRightPanelWide={true}
      rightPanelComponent={
      <>
        <Outlet context={{}} />
      </>
    }
    >
    <TableBlockInfiniteScroll 
      TableCardContent={OrdersTableCard as unknown as React.JSX.Element}
      tableCardHeight={230}
      iconName='fileCheck'
    />
    </GridLayout>
  )
}

export default Orders
