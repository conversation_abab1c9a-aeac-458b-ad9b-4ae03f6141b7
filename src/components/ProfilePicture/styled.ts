import { Icon } from '@aidsupply/components'
import styled from 'styled-components'

export const StyledProfilePicture = styled.div`
  display: flex;
  padding: 20px 0;
  margin-top: 10px;
  align-items: center;
  gap: 10px;
  justify-content: space-between;
  border-bottom: 1px solid ${({ theme: { color } }) => color.general.gray1};
  figure {
    flex-shrink: 0;
  }
  .body1 {
    flex-grow: 1;
  }
  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px) {
    flex-wrap: wrap;
    justify-content: center;
  }
`
export const StyledIcon = styled(Icon)`
  padding: 10px;
  border: 1px solid ${({ theme }) => theme.color.general.gray2};
  box-shadow: 0px 1px 2px 0px rgba(24, 24, 28, 0.04), 0px 3px 4px -5px rgba(24, 24, 28, 0.03);
`
