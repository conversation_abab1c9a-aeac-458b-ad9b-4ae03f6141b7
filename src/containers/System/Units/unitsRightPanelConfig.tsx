import { IFormConfig } from '../../../components/Form/interfaces'

export const unitsRightPanelConfig = (): IFormConfig => {
  return {
    withTabs: ['translations'],
    validationRules: {
      'translations.en': ['required'],
      code: ['required'],
      coefficient: ['required'],
      magnitude: ['required'],
    },
    fields: {
      noTitle: [{
        key: 'state',
        label: 'state',
        optionsKeys: ['states'],
        component: 'dropdown',
      }],
      general: [
        {
          key: 'code',
          label: 'code',
          required: true,
        },
        {
          key: 'magnitude',
          label: 'magnitude',
          optionsKeys: ['magnitudes'],
          component: 'dropdown',
          required: true,
        },
        {
          key: 'coefficient',
          label: 'Coefficient',
          required: true,
        },
      ],
      translations: [
        {
          key: 'translations',
          label: 'title',
          placeholder: 'Your item title',
          required: true,
        },
      ]
    }
  }
}
