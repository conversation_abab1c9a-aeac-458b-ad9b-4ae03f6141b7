import { ChangeEvent, FormEvent, JSX, ReactNode } from 'react'
import { TLanguages } from '../../locales'
import { QuillOption } from '../../commonTypes'

export type FilesType = 'photos' | 'files' | 'image' | 'document'
export type StateType = 'drafted' | 'posted' | 'deleted'

export type BasicValue = { id?: number | string; [key: string]: unknown } | string | number | boolean | unknown[] | null

export type UpdateSelect = (
  key: string,
  onSelectValueChange?: (value: Record<string, unknown> | string) => void
) => (value?: BasicValue) => void

export type UpdateCheckbox = (checked: boolean, event: InputEvent) => void
export type UpdateInput = (
  key: InputEvent | { target: InputTarget },
  onInputValueChange?: (value: string | number | null) => void
) => void

export type ValidateField = (
  key: string,
  value: string | boolean | Record<string, unknown>,
  formValues: Record<string, unknown>
) => void

export interface IOptionData {
  id: string
  label:
    | {
        en: string
        uk: string
      }
    | string
  iconName?: string
}

type InputEvent = ChangeEvent<HTMLInputElement>
type FormSubmit = (
  event?: FormEvent<HTMLFormElement>,
  customValuesForOnBlur?: {
    [key: string]: unknown
  },
  val?: BasicValue
) => void
export type TranslateFn = (key: string) => string
export type InputTarget = { name: string; value: string | InputEvent | number | { [key: string]: unknown } | null }

export interface IFieldsProps {
  valueKey?: string
  isMulti?: boolean
  noTranslation?: boolean
  component?:
    | 'dropdown'
    | 'checkbox'
    | 'date'
    | 'input'
    | ((props: Record<string, unknown>) => ReactNode | React.JSX.Element)
  type?: string
  key?: string
  label?: string
  placeholder?: string
  defaultValue?: BasicValue
  initialValue?: BasicValue
  required?: boolean
  disabled?: boolean
  isDisabled?: boolean
  error?: string
  variant?: string
  className?: string
  labelType?: string
  width?: string
  hideButtons?: boolean
  labelFontWeight?: number
  minDate?: Date
  validationTranslationKeys?: string[]
  value?: BasicValue
  iconLeftProps?: Record<string, unknown>
  transformValue?: (value?: BasicValue, lng?: string) => BasicValue
  onChange?: (event: boolean, val?: Record<string, unknown>) => void
  onInputValueChange?: (
    value: string | number | null,
    setFormValues?: (value: Record<string, unknown>) => Record<string, unknown>,
    initialValues?: Record<string, unknown>
  ) => void
  onSelectValueChange?: (
    value: Record<string, unknown> | string,
    setValuesChanged?: (prev: Record<string, unknown>) => Record<string, unknown>,
    initialValues?: Record<string, unknown>,
    optionsData?: Record<string, unknown>
  ) => void
  iconRightProps?: { name: string; onClick: (event: ChangeEvent<HTMLInputElement>) => void }
  getBlockValuesByCustomRule?: (
    formValues?: { id?: string | number; [key: string]: unknown },
    optionsData?: Record<string, unknown>
  ) => Record<string, unknown>
  additionToInput?: React.ReactNode
  platformsToShowOn?: []
  // TODO: Check and leave one of readOnly
  isReadOnly?: boolean
  readOnly?: boolean
  isCreatable?: boolean
  labelKey?: string
  autosize?: boolean
  menuIsOpen?: boolean
  isClearable?: boolean
  optionsKeys?: string[]
  options?: IOptionData[]
  min?: number
  max?: number
  rows?: number
  step?: string
  multiline?: boolean
  toolbarOptions?: []
  disabledWhenNotEmpty?: boolean
  getOptionBeforeTextComponent?: () => void
  customGetOptionLabel?:
    | boolean
    | ((option: Record<string, unknown> | IOptionData, t: TranslateFn, lng?: TLanguages) => unknown)
  getOptions?: (optionsData: Record<string, unknown>) => void
  addElementToSelect?: (t: TranslateFn, disabled: boolean, updateSelect?: UpdateSelect) => boolean
  addElementToInput?: (updateInput: UpdateInput, t: TranslateFn, disabled?: boolean) => void
  getOptionsFromFormValues?: (
    formValues: Record<string, unknown>,
    optionsData?: Record<string, unknown>,
    mainFormData?: Record<string, unknown>,
    additionalFormValues?: Record<string, unknown>,
    systemObject?: Record<string, unknown>
  ) => Record<string, unknown> | Record<string, unknown>[]
  validationRules?: Record<string, ValidationRule[]>
  getIsHidden?: (
    formValues?: { id?: string | number; [key: string]: unknown },
    optionsData?: Record<string, unknown>,
    initialValues?: Record<string, unknown>
  ) => boolean
  getDisabled?: (
    formValues?: Record<string, unknown>,
    optionsData?: Record<string, unknown>,
    initialValues?: Record<string, unknown>
  ) => void
}

interface IBaseFormProps {
  fields?: IFieldsProps[] | Record<string, IFieldsProps>
  formErrors?: Record<string, string> | boolean
  formValuesCustom?: { id?: string | number; [key: string]: unknown }
  FormWrapper?: JSX.Element
  initialValues?: Record<string, unknown>
  isReadOnly?: boolean
  isSubmitOnBlur?: boolean
  labelKey?: string
  labelType?: string
  optionsData?: Record<string, unknown>
  submitByEnterPressed?: boolean
  type?: string
  validationRules?: Record<string, ValidationRule[]>
  withActions?: boolean
  headerType?: string
  expansionPanelProps?: Record<string, unknown>
  formSubmit?: FormSubmit
}

export type ValidationRule =
  | string
  | {
      type?: string
      fields?: string[]
      values?: Record<string, unknown> | string
      isPasswordValid?: boolean
      customRuleName?: string
      withSuccess?: boolean
      dataForValidation?: {
        transformValue?: (value: string) => string
        [key: string]: unknown
      }
    }

export interface IFormConfig {
  withTabs?: string[]
  validationRules?: Record<string, ValidationRule[]>
  fields?: IFieldsProps[] | Record<string, IFieldsProps[]> | Record<string, QuillOption>
  createNewOptions?: boolean
  tableRowKey?: string
  optionsFromValuesDependencies?: string[]
  navbarHidden?: boolean
  isDeletionByState?: boolean
  changingBlocksDependency?: string
  changingBlocksDependencyValue?: Record<string, unknown>
  transformAddedResult?: (
    resultObj: Record<string, unknown>,
    additionalFormValues: Record<string, unknown>
  ) => Record<string, unknown>

  [key: string]: unknown
}

interface IFormData extends IFormConfig {}

export interface IFormProps<T = Record<string, unknown>> extends IBaseFormProps {
  id: number
  type: string
  buttonsAreSticky?: boolean
  cancelButtonText?: string
  children?: React.ReactNode
  className?: string
  isAlwaysActiveSubmitButton?: boolean
  formConfig?: IFormConfig
  formData?: IFormData
  FormBlockWrapper?: React.ReactNode
  getFormButtons?: (formValues: Record<string, unknown> | boolean) => React.ReactNode
  initialValuesChanged?: Record<string, unknown>
  initialValuesFromApi?: Record<string, unknown>
  inProgress?: boolean //| null
  isDraft?: boolean
  customType?: string
  withCustomValidationRule?: boolean
  onSubmit: (values: T, e?: { preventDefault: () => void }) => void | boolean
  resetForm?: () => void
  serverError?: string | null | boolean | unknown
  setFormErrors?: (errors: Record<string, string>) => void
  setIsDraft?: (isDraft: boolean) => void
  valuesChanged?: Record<string, unknown>
  setValuesChanged?: (valuesChanged: Record<string, unknown>) => void
  updateInput?: UpdateInput
  updateSelect?: UpdateSelect
}

export interface IFormBlockProps extends IBaseFormProps {
  fields: IFieldsProps[] | Record<string, IFieldsProps>
  blockKey: string
  id: string | number
  isSecondaryBlock?: boolean
  touchedFields?: string[]
  setTouchedFields?: () => void
  // valuesChanged?: Record<string, unknown>
  type: string
  formConfig?: IFormConfig
  updateInput?: UpdateInput
  updateSelect?: UpdateSelect
  updateCheckbox?: UpdateCheckbox
  validateField?: ValidateField
}

export interface IFormFieldsProps extends IBaseFormProps {
  additionalFormValues?: Record<string, unknown>
  flexDirection?: string
  tabKey?: string
  mainFormData?: Record<string, unknown>
  sidebarFormId?: string | number
  updateCheckbox?: UpdateCheckbox
  updateInput?: UpdateInput
  updateSelect?: UpdateSelect
  validateField?: ValidateField
  formConfig?: IFormConfig
}

export interface IFormFieldProps extends Partial<IBaseFormProps> {
  field: IFieldsProps
  formError?: string
  formErrors?: Record<string, string> | boolean
  formFlexDirection?: string
  formSubmit?: FormSubmit
  name: string
  mainFormData?: Record<string, unknown>
  // optionsFromFormValues?: Record<string, unknown> | Record<string, unknown>[]
  sidebarFormId?: string | number
  withTabs?: boolean
  updateCheckbox?: UpdateCheckbox
  updateInput?: UpdateInput
  updateSelect?: UpdateSelect
  validateField?: ValidateField
}

export interface IFormInputProps extends Partial<IBaseFormProps> {
  field: IFieldsProps
  formSubmit?: FormSubmit
  formValues: Record<string, unknown>
  isDisabled?: boolean | void
  isHidden?: boolean | void
  multiline?: boolean
  name: string
  t: TranslateFn
  value: BasicValue
  validateField?: ValidateField
  hideButtons?: boolean
  updateInput?: UpdateInput
}
