import { useState } from 'react'
import { Link as RouterLink, useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { Button, Icon, Typography, UiLink } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import Form from '../../components/Form/Form'
import { StyledAdditionToInputLink } from '../../components/Form/styled'
import { signIn } from '../../redux-saga/reducers/user'
import { INTRO_PAGES_IMG } from '../../constants'
import { useIsPasswordShown } from '../../hooks/useIsPasswordShown'
import { selectUserInProgressAndError } from '../../redux-saga/selectors'
import { useMappedState } from '../../hooks'
import IntroScreenWrapper from '../IntroScreen/IntroScreenWrapper'
import { loginConfig } from './config'

const initialValues: {
  email: string
  password: string
} = {
  email: '',
  password: '',
}

const Login = () => {
  const { t } = useTranslation('signIn')
  const theme = useTheme()

  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { inProgress } = useMappedState(selectUserInProgressAndError)

  const { type, iconRightProps } = useIsPasswordShown()

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const onFormSubmit = () => {
    // @ts-ignore
    dispatch(signIn({ email, password, navigate }));
  }

  const getForgotPassLink = () => (
    <StyledAdditionToInputLink Link={RouterLink} to="/password-recovery">
      {t('forgotPassword')}
    </StyledAdditionToInputLink>
  )

  const getText = () => (
    <div className="inscriptionWrapper">
      <Typography type="body1">{t('dontHaveAccount')}</Typography>
      <Typography type="body1">
        <UiLink Link={RouterLink} to="/signup" className="auth" themeColor="general.dark">
          {t('signUp')}
        </UiLink>
      </Typography>
    </div>
  )

  return (
    <IntroScreenWrapper type="signIn" theme={theme} imageUrl={INTRO_PAGES_IMG} text={t('signIn')} >
      <Form
        type="login"
        initialValues={initialValues}
        formValues={{ email, password }}
        onSubmit={onFormSubmit}
        validationRules={{
          email: ['required', 'email'],
          password: ['required'],
        }}
        fields={[
          {
            ...loginConfig[0],
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => setEmail(event.target.value)
          }, 
          {
            ...loginConfig[1],
            additionToInput: getForgotPassLink(),
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => setPassword(event.target.value),
            type,
            iconRightProps,
          }
        ]}
      >
        <Button
          fullWidth
          variant="primary"
          type="submit"
          disabled={inProgress}
          uppercase={false}
          className="loginSubmit"
        >
          <Icon
            name="statusCompleted"
            size={20}
            stroke={theme.color.general.light}
            fill={theme.color.primary.main}
          />
          &nbsp;{t('signIn')}
        </Button>
      </Form>
      {getText()}
    </IntroScreenWrapper>
  )
}

export default Login
