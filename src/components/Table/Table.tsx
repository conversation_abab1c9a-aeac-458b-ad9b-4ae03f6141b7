import { useState, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import { useParams } from 'react-router'
import clsx from 'clsx'
import { useTheme } from 'styled-components'
import {
  useElementRect,
  Typography,
  Icon,
  Table,
} from '@aidsupply/components'
import { getTableSummary } from '../../utils/tableSummary'
import { IBrandItem } from '../../commonInterfaces'
import { formatNumber } from '../../utils/table'
import useEditableTable from './hooks/useEditableTable'
import { useTableModeAndSize } from './hooks/useTableModeAndSize'
import TableSearch from './TableSearch'
import { StyledContent, StyledLink } from './styled'
import EmptyScreen from '../EmptyScreen/EmptyScreen'

interface ITableComponent {
  additionalFormValues: Record<string, unknown>
  blockKey: string
  cellProps: Record<string, unknown>
  className: string
  columns: Record<string, unknown>
  createNewOptions?: boolean
  data: Record<string, unknown>[]
  editable: boolean
  editableTableConfig: Record<string, unknown>
  editableNavbarFields?: Record<string, unknown>
  formId: number
  gap: string
  optionsData: Record<string, unknown>
  rowKey: string
  tableMode: string
  typeData: { key: string }
  updateTableRows: (value: Record<string, unknown> | string) => void
  validationRules: Record<string, unknown>
  inProgress?: boolean
  isMainTable?: boolean
  navbarHidden?: boolean
  isDeletionByState?: boolean
  isReadOnly?: boolean
  headerHeight?: number
  changingBlocksDependencyValue?: Record<string, unknown>
}

const TableComponent = ({
  additionalFormValues,
  blockKey,
  cellProps,
  changingBlocksDependencyValue,
  className,
  columns,
  createNewOptions,
  data,
  editable,
  editableTableConfig,
  editableNavbarFields,
  formId,
  gap,
  headerHeight,
  inProgress,
  isDeletionByState,
  isMainTable,
  isReadOnly,
  navbarHidden,
  optionsData,
  rowKey,
  tableMode,
  typeData,
  updateTableRows,
  validationRules,
  ...rest
}: ITableComponent) => {
  const dispatch = useDispatch()
  const params = useParams()
  const editedItemId = params?.rightPanelId
  const theme = useTheme()
  const { t } = useTranslation(['table', 'loadingOrNoData'])
  const gridRef = useRef(null)
  const contentRef = useRef(null)
  const { top } = useElementRect(contentRef, 'resize')
  const topHeight = top + parseInt(gap, 10) / 2 + 2 || 0
  const [showAll, setShowAll] = useState(false)
  const initialDataRef = useRef(data)
  const [tableData, setTableData] = useState(data)

  const ROW_HEIGHT = 50
  const SHOW_MORE_HEIGHT = 25

  const maxVisible = 5
  const visibleData = isReadOnly ? (showAll ? tableData : tableData?.slice(0, maxVisible)) : tableData

  const tableDataWithoutDeleted = isDeletionByState
    ? visibleData?.filter((item) => item.state !== 'deleted')
    : visibleData

  const withShowMore = isReadOnly && tableData?.length > maxVisible
  const showMoreValue = tableData?.length - maxVisible

  const tableModeMaxHeight = (visibleData?.length
    ? (visibleData.filter((item) => item.state !== 'deleted').length + 1) * ROW_HEIGHT + (headerHeight || 0)
    : 95)
  
  // @ts-ignore
  const currencySymbol = additionalFormValues?.currency_id?.symbol ||
  // @ts-ignore
  optionsData?.currencies?.[additionalFormValues?.currency_id]?.symbol || ''

  useEffect(() => {
    setTableData(data)
  }, [data])

  const { onTableResize } = useTableModeAndSize({
    type: typeData?.key,
    topHeight,
    gap: Number(gap),
    gridRef,
})

  const { onBlurCellInput } = useEditableTable({
    dispatch,
    isDeletionByState,
    rowKey,
    tableData: visibleData,
    type: typeData.key,
    initialData: initialDataRef?.current,
    // @ts-ignore
    setTableData,
    // @ts-ignore
    updateTableRows,
  })

  if (tableMode === 'table') {
    const summaryData =
      visibleData &&
      getTableSummary(typeData.key, t, visibleData as unknown as IBrandItem[], {
        state: additionalFormValues?.state as string,
        currency: currencySymbol
      })

    const columnsWithAction = [
      ...(Array.isArray(columns) ? columns : [columns]),
      {
        key: 'actions',
        dataKey: 'actions',
        width: 40,
        flexGrow: 0,
        cellRenderer: ({ rowData }: { rowData: IBrandItem }) => (
          <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
            <Icon
              name={rowData.shouldBeDeleted ? 'arrowRotate' : 'trashBin'}
              size={16}
              fill={rowData.shouldBeDeleted ? theme.color.general.gray3 : theme.color.status.error}
              style={{ cursor: 'pointer' }}
              onClick={() => {
                const updatedData = tableData.map((item) => 
                  item.id === rowData.id 
                    ? { ...item, shouldBeDeleted: !rowData.shouldBeDeleted }
                    : item 
                )
                setTableData(updatedData)
              }}
            />
          </div>
        ),
      },
    ]

    const formattedDataValuesReadOnly = () => {
      return visibleData?.map(item => {
        const price = item?.price as number ?? 0
        const formatted = price.toFixed(2)
    
        return {
          ...item,
          price: `${currencySymbol}${formatted}`
        };
      });
    };

    const formattedDataValuesEditable = () => {
      return visibleData?.map(item => {
        const price = item?.price as number ?? 0
        const quantity = item?.quantity as number ?? 0
    
        return {
          ...item,
          price: formatNumber(price),
          quantity: formatNumber(quantity)
        };
      });
    };

    if (data?.length === 0) {
      return <EmptyScreen text={t('noItems')} iconName='noSupplies' width='150px' height='150px' />
    }

    return (
      <>
        {blockKey.endsWith('_items') && data?.length > 0 && (
          <TableSearch
            type={typeData.key}
            isMainTable={false}
            iconLeftProps={{ stroke: theme.color.general.gray3 }}
            placeholder={t('searchEditableTable')}
            setTableData={setTableData}
            data={data}
          />
        )}
        <StyledContent
          ref={contentRef}
          topHeight={topHeight}
          maxHeight={
            !!(summaryData && visibleData?.length)
            ? tableModeMaxHeight + ROW_HEIGHT + (withShowMore ? SHOW_MORE_HEIGHT : 0)
            : tableModeMaxHeight + (withShowMore ? SHOW_MORE_HEIGHT : 0)
          }
          withShowMore={withShowMore as boolean}
          isReadOnly={isReadOnly}
          className={clsx(
            tableMode,
            !!(summaryData && visibleData?.length) && 'withSummary',
            (withShowMore as boolean) && 'withShowMore',
            className
          )}
        > 
          <Table
            onBlurCellInput={onBlurCellInput}
            rowKey={rowKey}
            editable={!isReadOnly && editable}
            isReadOnly={isReadOnly}
            emptyText={t(inProgress ? t('loadingOrNoData:loading') : 'noItems')}
            emptyIconName="noSearchData"
            cellProps={cellProps}
            maxHeight={tableModeMaxHeight}
            topHeight={topHeight}
            headerHeight={headerHeight}
            isEmptyTextOnly={editableTableConfig?.isEmptyTextOnly}
            data={isReadOnly ? formattedDataValuesReadOnly() : formattedDataValuesEditable()}
            columns={isReadOnly ? columns : columnsWithAction}
            onResize={onTableResize}
            rowHeight={ROW_HEIGHT}
            rowClassName={({ rowData }: { rowData: IBrandItem }) => {
              const classes = []
              if (rowData?.shouldBeDeleted) {
                classes.push('deletedRow')
              }
              if (isMainTable && editedItemId && rowData.id?.toString() === editedItemId) {
                classes.push('activeRow')
              }
              return classes.join(' ')
            }}
            summaryData={summaryData}
            {...(tableDataWithoutDeleted?.length === 0 && !blockKey.endsWith('_items')
              ? { containerNotFound: true }
              : {})}
            blockKey={blockKey}
            emptyIconProps={{
              style: { marginBottom: '5px' },
            }}
            {...rest}
          />
        </StyledContent>
        {withShowMore && (
          <StyledLink onClick={() => setShowAll((prev) => !prev)} className="editableTableLink">
            <Typography
              text={showAll ? t('showLess') : t('forms:showMoreWithValue', {value: showMoreValue})}
              color={theme.color.primary.main}
              fontWeight={500}
              lineHeight="16px"
            />
          </StyledLink>
        )}
      </>
    )
  }
}

export default TableComponent
