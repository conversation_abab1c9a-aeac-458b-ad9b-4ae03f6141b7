import * as Sentry from '@sentry/react'
import { Provider } from 'react-redux'
import { createRoot } from 'react-dom/client'
import './i18n'

import App from './App.tsx'
import ThemeProviderWrapper from './providers/ThemeProviderWrapper'
import configureStore from './configureStore'
import GlobalStyle from './globalStyles'
import ScreenContextWrapper from './wrappers/ScreenContextWrapper'
import PopupAlertsComponent from './wrappers/PopupAlertsComponent.tsx'

Sentry.init({
  dsn: process.env.REACT_APP_SENTRY_DSN,
  integrations: [
    Sentry.browserTracingIntegration(),
    Sentry.replayIntegration({
      maskAllText: false,
      blockAllMedia: false,
    }),
  ],
  tracesSampleRate: 1.0,
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  tracePropagationTargets: [/^https:\/\/crm\.aidsupply\.org/],
  // Session Replay
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
})

const root = document.getElementById('root')

const store = configureStore()

createRoot(root as HTMLElement).render(
  <Provider store={store}>
    <ThemeProviderWrapper>
      <ScreenContextWrapper>
        <GlobalStyle />
        <App />
        <PopupAlertsComponent />
      </ScreenContextWrapper>
    </ThemeProviderWrapper>
  </Provider>
)
