import { STATE } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'

export const campaignsRightPanelConfig: IFormConfig = {
  withTabs: ['translations', 'body'],
  validationRules: {
    state: ['required'],
    status: ['required'],
    'translations.en': ['required'],
    'body.en': ['required'],
    sum_goal: ['required'],
    currency_id: ['required'],
    organization_id: ['required'],
    bucket_wayforpay_slug: [
      {
        type: 'requiredIfFieldsEmpty',
        fields: ['bucket_monobank_slug', 'bucket_wayforpay_slug'],
      },
    ],
    bucket_monobank_slug: [
      {
        type: 'requiredIfFieldsEmpty',
        fields: ['bucket_monobank_slug', 'bucket_wayforpay_slug'],
      },
    ],
  },
  fields: {
    general: [
      { ...STATE, required: true },
      {
        key: 'status',
        label: 'status',
        optionsKeys: ['campaignStatuses'],
        component: 'dropdown',
        required: true,
      },
      {
        key: 'organization_id',
        label: 'organization',
        noTranslation: true,
        labelKey: 'name',
        valueKey: 'id',
        optionsKeys: ['organizations'],
        component: 'dropdown',
        isClearable: true,
        required: true,
        getDisabled: () => true
      },
      {
        key: 'deadline_at',
        label: 'deadline',
        component: 'date',
        transformValue: (value) => (value ? new Date(value as string) : ''),
        minDate: new Date(),
      },
      {
        key: 'currency_id',
        label: 'currency',
        labelKey: 'code',
        optionsKeys: ['currencies'],
        noTranslation: true,
        required: true,
        component: 'dropdown',
      },
      {
        key: 'sum_goal',
        label: 'goalAmount',
        type: 'number',
        required: true,
        hideButtons: true,
      },
      {
        key: 'sum_current',
        label: 'currentAmount',
        type: 'number',
        hideButtons: true,
      },
      {
        key: 'bucket_wayforpay_slug',
        label: 'bucketWayforpay',
        getDisabled: (formValues) => !!formValues?.bucket_monobank_slug,
        required: true,
      },
      {
        key: 'bucket_monobank_slug',
        label: 'bucketMonobank',
        getDisabled: (formValues) => !!formValues?.bucket_wayforpay_slug,
        required: true,
      },
    ],
    translations: [
      {
        key: 'translations',
        label: 'title',
        placeholder: 'Your item title',
        required: true,
      },
      {
        key: 'body',
        label: 'texts',
        type: 'richText',
        required: true,
      },
    ],
    photos: [],
  },
}
