import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router'
import { object } from 'dot-object'
import { isObject } from '@aidsupply/components'
import merge from 'deepmerge'
import { StateType } from '../../../components/Form/interfaces'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { useMappedState } from '../../../hooks'
import { ITranslations } from '../../../locales'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { postsRightPanelConfig } from './postsRightPanelConfig'

interface IPostsRightPanel {
  state?: StateType
  translations?: ITranslations
  [key: string]: unknown
}

const PostsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const dataInitial = useMappedState(selectSidebarInitialData)
  
  const isReadOnly = false

  const onSubmit = (formValuesChanged: IPostsRightPanel) => {
    const requestBody: IPostsRightPanel = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key]) && (requestBody[key] as { id: number }).id) {
        requestBody[key] = (requestBody[key] as { id: number }).id
      }
    })

    if (formValuesChanged.translations) {
      requestBody.translations = merge(dataInitial?.translations as ITranslations, requestBody.translations as ITranslations)
      if (formValuesChanged.translations.en) {
        requestBody.name = formValuesChanged.translations.en
      }
    }

    if (requestBody.body) requestBody.body = merge(dataInitial?.body as ITranslations, requestBody.body as ITranslations)

    const dataToSend = {
      id: dataInitial?.id,
      requestBody,
      type: 'posts',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} onSubmit={onSubmit} formData={postsRightPanelConfig} customType="posts" />
}

export default PostsRightPanel
