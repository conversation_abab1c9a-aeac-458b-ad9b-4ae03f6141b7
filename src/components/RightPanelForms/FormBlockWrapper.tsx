import { pick } from 'dot-object'
import { useTranslation } from 'react-i18next'
import { useMappedState } from '../../hooks'
import { selectAllSystemCollections, selectDataTypeObject, selectUserRole } from '../../redux-saga/selectors'
import { ISubmenu } from '../NavMenu/config'
import { getEditableTableColumns } from '../config/columnsEditableTable'
import { EDITABLE_TABLE_NAVBAR_FORM_CONFIG } from '../config/editableTableNavBarForms'
import { IFieldsProps, IFormFieldsProps, UpdateCheckbox, UpdateInput, UpdateSelect } from '../Form/interfaces'
import { TLanguages } from '../../locales'
import EditableTableFormPart from './EditableTableFormPart'
import FilesForm from './FilesForm'
import { IFileDrafted } from '../UploadFiles/Thumbnail'
import Attachments from './Attachments.tsx'

interface FormBlockWrapperProps {
  blockFormValuesChanged: Record<string, unknown>
  blockKey: string
  children: React.ReactNode
  compoundTypeKey: string
  customBlockValues: Record<string, unknown>
  editableTableConfig: Record<string, unknown>
  fields: IFieldsProps[]
  formFieldsProps?: IFormFieldsProps
  // formValues: Record<string, string | number | boolean | unknown[] | null | Record<string, unknown>>
  getPanelHeader: (blockKey: string) => string
  id: number
  initialValues?: Record<string, unknown>
  isReadOnly: boolean
  optionsData: Record<string, unknown>
  touchedFields: Record<string, unknown>
  setTouchedFields: (touchedFields: Record<string, unknown>) => void
  setTextsChosenLng: (textsChosenLng: Record<string, unknown>) => void
  updateCheckbox: UpdateCheckbox
  updateInput: UpdateInput
  updateSelect: UpdateSelect
}

const FormBlockWrapper = ({
  blockFormValuesChanged,
  blockKey,
  children,
  compoundTypeKey,
  customBlockValues,
  editableTableConfig,
  fields,
  formFieldsProps,
  getPanelHeader,
  id,
  initialValues,
  isReadOnly,
  optionsData,
  // TODO: uncomment when setting an corresponding blockKey
  // touchedFields,
  // setTouchedFields,
  setTextsChosenLng,
  // TODO: uncomment when setting an corresponding blockKey
  // updateCheckbox,
  updateInput,
  updateSelect,
}: FormBlockWrapperProps) => {
  const typeData = useMappedState(selectDataTypeObject) as ISubmenu
  const type = typeData?.key || ''
  const system = useMappedState(selectAllSystemCollections)
  const role = useMappedState(selectUserRole)
  const {
    t,
    i18n: { language },
  } = useTranslation('table')

  if (blockKey === 'noExpansionTop') {
    return children
  }

  const editableTableColumns = getEditableTableColumns(
    EDITABLE_TABLE_NAVBAR_FORM_CONFIG[type] ? type : compoundTypeKey,
    language as TLanguages,
    t
  )

  return (
    <>
      {!!editableTableColumns && (
        <EditableTableFormPart
          id={id}
          cellProps={{
            system,
            custom_block_values: customBlockValues,
            lng: language,
            id,
            user: { role },
            is_read_only: isReadOnly,
            updateTableRows: updateSelect.bind(null, blockKey),
          }}
          // @ts-ignore
          isReadOnly={blockKey === 'shipment_items' && formValues.order_id}
          compoundTypeKey={compoundTypeKey}
          optionsData={optionsData}
          language={language}
          // @ts-ignore
          typeData={{ key: EDITABLE_TABLE_NAVBAR_FORM_CONFIG[type] ? type : compoundTypeKey }}
          columns={editableTableColumns}
          // TODO: fixe when setting an EditableTableFormPart
          // initialData={blockKey === 'modifications' ? customBlockValues : initialValues[blockKey]}
          blockKey={blockKey}
          formFieldsProps={formFieldsProps}
          fields={fields}
          getPanelHeader={getPanelHeader}
          updateTableRows={(value: Record<string, unknown> | string) => updateSelect(blockKey)(value)}
          setTextsChosenLng={setTextsChosenLng}
          validationRules={editableTableConfig?.validationRules as Record<string, unknown>}
          t={t}
        />
      )}

      {/* {type === 'users' && blockKey === 'general' && fields.find((field) => field.key === 'organization') && (
        <GenerateNewOrganization type={type} updateSelect={updateSelect} />
      )} */}

      {blockKey === 'photos' || blockKey === 'files' ? (
        <Attachments
          isReadOnly={isReadOnly}
          type={type}
          id={id}
          updateInput={updateInput}
          blockKey={blockKey}
          withTabsHidden={type === 'reports'}
        />
      ) : (
        children
      )}

      {/* // TODO: uncomment when setting an corresponding blockKey */}
      {/* {blockKey === 'stockItemsUpload' && <StockItemsUpload />} */}

      {/* // TODO: uncomment when setting an corresponding blockKey */}
      {/* {type === 'carriers' && blockKey === 'deliveryOptions' && (
        <MultiSelectTable
          attributes={optionsData.attributes}
          customBlockValues={
            optionsData.attributes && [
              Object.values(optionsData.attributes).find((attr) => attr.slug === 'delivery-options'),
            ]
          }
          data={formValues[blockKey]}
          enumerations={optionsData.enumerations}
          headerHeight={0}
          keyToUpdate={blockKey}
          options={
            (optionsData.enumerations &&
              Object.values(optionsData.enumerations).find(
                (enumeration) => enumeration.slug === 'delivery-options'
              )?.options) ||
            []
          }
          type={blockKey}
          updateSelectValue={updateSelect}
          withTextHeader
        />
      )}
      // TODO: uncomment when setting an corresponding blockKey
      {/* {type === 'items' && ['properties', 'characteristics'].includes(blockKey) && (
        <ItemAttributesFormPart
          attributes={optionsData.attributes}
          blockKey={blockKey}
          blocks={getOptionsBasedOnCategory(formValues, optionsData, `general.${blockKey}`, 'attributes')}
          customBlockValues={customBlockValues}
          enumerations={optionsData.enumerations}
          formValues={formValues[blockKey]}
          getPanelHeader={getPanelHeader}
          photos={formValues.photos}
          units={optionsData.units}
          updateInput={updateInput}
          updateSelect={updateSelect}
          updateCheckbox={updateCheckbox}
        />
      )} */}
    </>
  )
}

export default FormBlockWrapper
