import { useContext } from 'react'
import { DefaultTheme } from 'styled-components'
import { useTranslation } from 'react-i18next'
import { ScreenContext, Typography } from '@aidsupply/components'
import PopupAlertsComponent from '../../wrappers/PopupAlertsComponent'
import HeaderIntroScreen from './components/HeaderIntroScreen'
import { StyledFooterText, StyledWindow, StyledWrapper, StyledIntroWrapper } from './styled'

const IntroScreenWrapper = (
  {
    children,
    text,
    type,
    theme,
    imageUrl
  }: {
    children: React.ReactNode
    text: string
    type: string
    theme: DefaultTheme
    imageUrl: string
  }) => {
  const { t } = useTranslation('signIn')

  const { width, lg } = useContext(ScreenContext) || {}
  const isTablet = width && width < lg

  return (
    <StyledWrapper>
      <div className="mainWrapper">
        <PopupAlertsComponent />

        <HeaderIntroScreen theme={theme} />

        <StyledWindow className={type}>
          <div className="contentWrapper">
            <Typography
              type="h2"
              className="text"
              textAlign="left"
              text={t(text || type)}
              margin="0 0 20px"
            />
            {children}
          </div>
        </StyledWindow>
        <StyledFooterText>
          <Typography type="body2" className="footerText">
            AidSupply {new Date().getFullYear()} © {t('footer:allRightsReserved')}
          </Typography>
        </StyledFooterText>
      </div>
      {!isTablet && (
        <StyledIntroWrapper
          className="introWrapper"
          url={imageUrl}
          color={theme.color.general.light}
        ></StyledIntroWrapper>
      )}
    </StyledWrapper>
  )
}

export default IntroScreenWrapper
