export type API_METHODS_TYPE = 'POST' | 'GET' | 'PATCH' | 'DELETE'

export const API_METHODS: Record<string, API_METHODS_TYPE> = {
  create: 'POST',
  read: 'GET',
  update: 'PATCH',
  upsert: 'POST',
  delete: 'DELETE',
}

export const DATA_TYPES_WITH_ITEMS = ['enumerations']

export const CRM_TYPES_WITH_POSTED_STATE: string[] = ['products', 'inquiry_items']

export const CRM_DATA_TYPES_WITH_NO_FACETS: string[] = ['dashboard', 'enumerations', 'products']

export const SEARCH_FIELDS: Record<string, string> = {
  orders: 'external_number|number|total',
  inquiries: 'number|recipient_name',
  invoices: 'number',
  shipments: 'number',
  organizations: 'name|name_en',
  brands: 'name|name_en',
  users: 'full_name|email',
  items: 'name|sku|translations',
  categories: 'name|translations',
  warehouses: 'name|city',
}

// TODO: check what fields are really needed
export const GET_CRM_API_URLS = (param: string): Record<string, string> => {
  return {
    attributes: '/catalogs/attributes',
    banners: '/common/posters',
    brands: '/catalogs/brands',
    carriers: '/common/carriers',
    categories: '/catalogs/categories',
    categoriesAll: '/catalogs/categories',
    contracts: '/documents/contracts',
    campaigns: '/documents/campaigns',
    comments: '/documents/comments',
    country_subdivisions: '/catalogs/country_subdivisions',
    currentUser: '/current_user',
    dashboard: '/reports/dashboard',
    inquiry_items: '/catalogs/categories',
    countries: '/catalogs/countries',
    currencies: '/catalogs/currencies',
    emailConfirm: '/auth/confirm_email',
    enumerations: '/catalogs/enumerations',
    enumerationsItems: `/catalogs/enumerations/${param}/options`,
    faqs: '/knowledgebase/faqs',
    feedbacks: '/common/feedbacks',
    filesUpload: '/files',
    filesUploadProfile: `/users/${param}/profile-image`,
    inquiries: '/documents/inquiries',
    inviteDetails: '/auth/confirm_invite_details',
    inviteSignup: '/auth/confirm_invite',
    invoices: '/documents/invoices',
    invoicesItems: `/documents/invoices/${param}/invoice_items`,
    inventories: '/documents/inventories',
    items: '/catalogs/items',
    notifications: '/notifications',
    orders: '/documents/orders',
    ordersItems: `/documents/orders/${param}/items`, // order_id
    ordersParseFile: '/documents/orders/parse_items',
    organizations: '/organizations',
    organizationsProfileImage: `/organizations/${param}/profile-image`,
    organizationsProfileBanner: `/organizations/${param}/banner-image`,
    pages: '/knowledgebase/pages',
    pagesOpenAPI: '/platform/pages',
    platforms: '/common/platforms',
    platformsOpenAPI: '/platform/platforms',
    posts: '/knowledgebase/posts',
    reports: '/knowledgebase/reports',
    topics: '/knowledgebase/topics',
    products: '/common/search',
    refreshToken: '/auth/refresh-token',
    requestResetPassword: '/auth/request-reset-password',
    resetPassword: '/users/reset-password',
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    shipments: '/documents/shipments',
    shipmentsParseFile: '/documents/shipments/parse_items',
    shipmentsItems: `/documents/shipments/${param}/shipment_items`,
    warehouses: '/warehouses',
    'stock-items': '/stocks',
    versions: '/common/versions',
    units: '/catalogs/units',
    users: '/users',
    usersName: `/users/${param}`,
    userChangeRole: `/users/${param}/user-role`, // user_id
    userInvite: '/auth/invite-user',
    test: '/ws/test',
    inventoriesParseFile: '/documents/inventories/parse_items',
  }
}

export const SYSTEM_DATA_KEYS: Record<string, string[]> = {
  attributes: ['attributes', 'states', 'enumerations', 'units', 'attributeTypes', 'users'],
  brands: ['brands', 'organizations', 'countries', 'categories', 'states', 'users'], // industries
  carriers: ['countries', 'attributes', 'enumerations', 'users'],
  categories: ['categories', 'states', 'attributes', 'enumerations', 'units', 'users'], // industries
  contracts: ['states', 'organizations', 'documents', 'contractTypes'],
  dashboard: [
    'brands',
    'categories',
    'organizations',
    'invoices',
    'items',
    'orders',
    'shipments',
    'warehouses',
    'users',
  ],
  inquiry_items: [
    'states',
    'organizations',
    'units',
    'industries',
    'attributes',
    'enumerations',
    'currencies',
  ],
  enumerations: ['enumerations', 'states', 'users', 'enumerationAddons'],
  faqs: ['organizations', 'states'],
  inquiries: [
    'organizationTypes',
    'states',
    'inquiryStatuses',
    'organizations',
    'categories',
    'units',
    'attributes',
    'enumerations',
    'countries',
    'country_subdivisions',
  ],
  invoices: [
    'states',
    'invoiceStatuses',
    'organizations',
    'contracts',
    'currencies',
    'orders',
    'items',
    'attributes',
    'enumerations',
    'brands',
  ],
  inventories: [
    'states',
    'organizations',
    'warehouses',
    'users',
    'items',
    'inventories',
    'brands',
    'countries',
    'country_subdivisions',
    'inventoryStatuses',
  ],
  items: [
    'attributes',
    'brands',
    'countries',
    'categories',
    'enumerations',
    'items',
    'states',
    'units',
    'users',
  ],
  orders: [
    'brands',
    'categories',
    'organizations',
    'states',
    'orderStatuses',
    'attributes',
    'enumerations',
    'currencies',
    'countries',
    'warehouses',
    'items',
    'inquiries',
    'users',
    'taxTypes',
  ],
  organizations: [
    'states',
    'orgOrUsersStatuses',
    'organizationTypes',
    'countries',
    'country_subdivisions',
    'organizationRoles',
  ], //'currencies', 'users', 'organizations',
  pages: ['states', 'organizations', 'pageTypes'],
  products: [
    'states',
    'categories',
    'brands',
    'attributes',
    'enumerations',
    'currencies',
    'countries',
    'organizations',
    'inquiries',
  ],
  posts: ['states', 'visibilityTypes', 'topics', 'organizations'],
  shipments: [
    'states',
    'shipmentStatuses',
    'organizations',
    'warehouses',
    'currencies',
    'orders',
    'items',
    'attributes',
    'enumerations',
    'brands',
  ],
  warehouses: [
    'currencies',
    'organizations',
    'countries',
    'country_subdivisions',
    'states',
    'warehouseTypes',
    'users',
  ],
  'stock-items': [
    'brands',
    'categories',
    'currencies',
    'items',
    'organizations',
    'states',
    'warehouses',
    'attributes',
    'enumerations',
    'users',
  ],
  units: ['states', 'magnitudes', 'users'],
  users: ['states', 'userRoles', 'organizations', 'orgOrUsersStatuses'], // 'users',
  campaigns: ['states', 'currencies', 'organizations', 'campaignStatuses'],
  'organization-profile': ['organizationTypes', 'countries', 'country_subdivisions'],
  reports: ['states', 'reportTypes', 'months', 'years', 'organizations'],
}
