export const signUpConfig: Record<string, unknown>[] = [
  {
    label: 'fullName',
    key: 'full_name',
    variant: 'secondary',
    labelType: 'top',
    required: true
  },
  {
    label: 'organization',
    key: 'organization_name',
    variant: 'secondary',
    labelType: 'top'
  },
  {
    label: 'email',
    key: 'email',
    labelType: 'top',
    placeholder: '<EMAIL>',
    required: true
  },
  {
    key: 'password',
    label: 'password',
    variant: 'secondary',
    type: 'password',
    labelType: 'top',
    required: true
  },
  {
    key: 'repeat_password',
    label: 'repeatPassword',
    variant: 'secondary',
    type: 'password',
    labelType: 'top',
    required: true
  }
]
