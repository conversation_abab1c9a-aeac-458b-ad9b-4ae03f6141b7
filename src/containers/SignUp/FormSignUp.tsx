import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import { NavigateFunction, useNavigate } from 'react-router'
import { useTheme } from 'styled-components'
import { Button, Icon } from '@aidsupply/components'
import Form from '../../components/Form'
import { signUp } from '../../redux-saga/reducers/user'
import { useMappedState } from '../../hooks'
import { selectUserInProgressAndError } from '../../redux-saga/selectors'
import { useIsPasswordShown } from '../../hooks/useIsPasswordShown'
import CustomPasswordChecklist from '../IntroScreen/components/CustomPasswordChecklist'
import { signUpConfig } from './config'

export interface ICredentials {
  email: string,
  full_name: string,
  password: string,
  repeat_password: string,
  organization_name?: string
}

const FormSignUp = ({ initialValues }: {
  initialValues?: Record<string, unknown>
}) => {
  const theme = useTheme()
  const [password, setPassword] = useState('')
  const [repeat_password, setRepeatPassword] = useState('')
  const [isPasswordValid, setIsPasswordValid] = useState<boolean | undefined>(undefined)
  const [isSubmitPressed, setIsSubmitPressed] = useState(false)

  const { t } = useTranslation(['signIn', 'validation'])
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { inProgress, error } = useMappedState(selectUserInProgressAndError)

  const onFormSubmit = (
    values: ICredentials
  ) => {
    const { repeat_password, ...credentialsToPayload } = values
    const payload: Omit<ICredentials, 'repeat_password'> & { navigate: NavigateFunction } = { ...credentialsToPayload, navigate }
    if (password === repeat_password) {
      dispatch(signUp(payload))
    }
  }

  return (
    <Form
      className="form"
      type="signUp"
      withCustomValidationRule
      initialValues={initialValues}
      serverError={error !== false}
      onSubmit={onFormSubmit as unknown as (values: Record<string, unknown>) => void}
      validationRules={{
        full_name: ['required'],
        email: ['required', 'email'],
        password: ['required'],
        repeat_password: ['required'],
      }}
      fields={[
        signUpConfig[0],
        signUpConfig[1],
        signUpConfig[2],
        {
          ...signUpConfig[3],
          ...useIsPasswordShown(),
          onInputValueChange: (value: string) => setPassword(value),
        },
        {
          ...signUpConfig[4],
          ...useIsPasswordShown(),
          onInputValueChange: (value: string) => setRepeatPassword(value),
          error: isSubmitPressed && password !== repeat_password ? t('validation:passwordsNotMatch') : '',
          className: 'repeatPassword',
        },
      ]}
    >
      <CustomPasswordChecklist
        password={password}
        setIsPasswordValid={setIsPasswordValid}
        className={isSubmitPressed && !isPasswordValid ? 'passError' : ''}
      />
      <Button
        fullWidth
        variant="primary"
        type="submit"
        uppercase={false}
        onClick={() => setIsSubmitPressed(true)}
        disabled={inProgress}
      >
        <Icon
          name="statusCompleted"
          size={20}
          stroke={theme.color.general.light}
          fill={theme.color.primary.main}
        />
        &nbsp;{t('signUp')}
      </Button>
    </Form>
  )
}

export default FormSignUp
