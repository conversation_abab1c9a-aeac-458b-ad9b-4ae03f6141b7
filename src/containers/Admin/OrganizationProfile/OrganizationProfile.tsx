import { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import merge from 'deepmerge'
import { object } from 'dot-object'
import { Navigate, Outlet } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Button, isObject, isObjectEmpty, Typography, useWindowWidth } from '@aidsupply/components'
import { slugify } from 'transliteration'
import GridLayout from '../../Layout/GridLayout'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections, selectSidebarInitialData, selectUpsertInProgress, selectUserOrganizationId, selectUserRole } from '../../../redux-saga/selectors'
import { sidebarFetchById, sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { IDraftFile, uploadAvatar } from '../../../redux-saga/reducers/fileUpload'
import Form from '../../../components/Form'
import UploadFiles from '../../../components/UploadFiles'
import { organizationProfileFormConfig } from './organizationProfileFormConfig'
import { IOrganization } from '../../../commonInterfaces'
import OrganizationAvatar from './OrganizationAvatar'
import OrganizationBanner from './OrganizationBanner'
import { StyledOrganizationProfile } from './styled'

const OrganizationProfile = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation('forms')
  const theme = useTheme()

  const userRole = useMappedState(selectUserRole)
  const myOrganizationInitial = useMappedState(selectSidebarInitialData) as IOrganization
  const organizationId = useMappedState(selectUserOrganizationId) as number
  const system = useMappedState(selectAllSystemCollections)
  const sideBarUpsertInProgress = useMappedState(selectUpsertInProgress)

  const isOrganizationOwner = userRole === 'organization_owner'

  const width = useWindowWidth()

  const userpicSize = width < 1440 ? '85px' : '124px'

  useEffect(() => {
    dispatch(sidebarFetchById({ id: organizationId, type: 'organizations', reload: false}))
  }, [organizationId])

  const onFormSubmit = (valuesChanged: IOrganization) => {
    const requestBody: IOrganization = { ...object(valuesChanged) }
    Object.keys(requestBody).forEach((key) => {
      const value = requestBody[key as keyof IOrganization]
      // @ts-ignore
      if (isObject(value) && 'id' in value) {
        requestBody[key as keyof IOrganization] = value.id
      }
    });

    if ('year_founded' in requestBody && requestBody.year_founded === '') {
      requestBody.year_founded = null
    }

    if ('description' in requestBody && requestBody.description) {
      requestBody.description = merge(myOrganizationInitial?.description as Record<string, unknown>, requestBody.description as Record<string, unknown>)
    }

    if (valuesChanged.name) {
      requestBody.slug = slugify(valuesChanged.name)
    }

    const dataToSend = {
      id: myOrganizationInitial?.id,
      requestBody,
      type: 'organizations',
    }

    dispatch(sideBarUpsert({ ...dataToSend }))
  }

  const onAvatarSet = (newFile: File[] | IDraftFile[]) => {
    if (!isObjectEmpty(newFile)) {
      dispatch(
        uploadAvatar({
          file: newFile[0] as File,
          entityId: myOrganizationInitial?.id as number,
          entityType: 'organizations',
          fileType: 'image',
          fileGroup: 'logos',
        })
      )
    }
  }

  const onBannerSet = (newFiles: File[] | IDraftFile[]) => {
    if (!isObjectEmpty(newFiles)) {
      dispatch(
        uploadAvatar({
          file: newFiles[0] as File,
          entityId: myOrganizationInitial?.id as number,
          entityType: 'organizations',
          fileType: 'image',
          fileGroup: 'banners'
        })
      )
    }
  }

  if (!isOrganizationOwner) {
    return <Navigate replace to="/404" />
  }

  return (
    <GridLayout rightPanelComponent={<Outlet />} className="scrollable">
      <div>
        <UploadFiles
          entityType="organizations"
          entityId={myOrganizationInitial?.id as number}
          fileGroup="banners"
          onFnSet={onBannerSet}
          isSimpleButton
          simpleButtonProps={{
            as: OrganizationBanner,
            src: myOrganizationInitial?.banner_url,
            photoId: myOrganizationInitial?.banner_photo_id,
            entityId: myOrganizationInitial?.id,
          }}
        />
        <StyledOrganizationProfile>
          <div className="infoOrganization">
            <div className="container">
              <UploadFiles
                entityType="organizations"
                entityId={myOrganizationInitial?.id as number}
                fileGroup="logos"
                onFnSet={onAvatarSet}
                isSimpleButton
                simpleButtonProps={{
                  as: OrganizationAvatar,
                  userpicSize,
                  name: myOrganizationInitial?.name as string,
                  src: myOrganizationInitial?.logo_url,
                  photoId: myOrganizationInitial?.logo_photo_id,
                  entityId: myOrganizationInitial?.id,
                }}
              />
              <div className="info">
                <Typography type="h2" margin="0 0 6px 0">
                  {myOrganizationInitial?.name ? myOrganizationInitial?.name.charAt(0)?.toUpperCase() + myOrganizationInitial?.name.slice(1) : ''}
                </Typography>
                <Typography
                  type="body1"
                  lineHeight={'16px'}
                  fontWeight={500}
                  color={theme.color.general.gray3}
                >
                  {myOrganizationInitial?.type ? myOrganizationInitial?.type.charAt(0)?.toUpperCase() + myOrganizationInitial?.type.slice(1) : ''}
                </Typography>
              </div>
            </div>
            <div className="buttonInfo">
              <Button
                variant="bordered"
                text={width < theme.breakpoints.lg ? '' : t('viewProfile')}
                iconName="linkExternal"
                color={'rgba(23, 43, 77, 1)'}
              />
            </div>
          </div>
          <Form
            optionsData={system as Record<string, unknown>}
            initialValues={myOrganizationInitial as Record<string, unknown>}
            formConfig={organizationProfileFormConfig}
            validationRules={organizationProfileFormConfig.validationRules}
            expansionPanelProps={{
              disabled: false,
              withChevronInHeader: true,
            }}
            inProgress={sideBarUpsertInProgress}
            headerType="h4"
            onSubmit={onFormSubmit}
            buttonsAreSticky
            withActions
          />
        </StyledOrganizationProfile>
      </div>
    </GridLayout>
  )
}

export default OrganizationProfile
