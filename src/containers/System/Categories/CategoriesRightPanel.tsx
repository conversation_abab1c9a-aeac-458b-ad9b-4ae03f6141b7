import { useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { object } from 'dot-object'
import { isObject } from '@aidsupply/components'
import { slugify } from 'transliteration'
import merge from 'deepmerge'
import { useMappedState } from '../../../hooks'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { categoriesRightPanelConfig } from './categoriesRightPanelConfig'

const CategoriesRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const categoryInitial = useMappedState(selectSidebarInitialData)

  const isReadOnly = false

  const onFormSubmit = (formValuesChanged: Record<string, unknown>) => {
    const requestBody: Record<string, unknown> = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key] as Record<string, unknown>) && (requestBody[key] as Record<string, unknown>).id) {
        requestBody[key] = (requestBody[key] as Record<string, unknown>).id
      }
    })

    if (requestBody.translations)
      requestBody.translations = merge((categoryInitial?.translations as Record<string, unknown>), requestBody.translations)

    if (formValuesChanged.name) {
      requestBody.slug = slugify(formValuesChanged?.name as string)
    }

    const dataToSend = {
      id: categoryInitial?.id,
      requestBody,
      type: 'categories',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} onSubmit={onFormSubmit} formData={categoriesRightPanelConfig()}/>
}

export default CategoriesRightPanel
