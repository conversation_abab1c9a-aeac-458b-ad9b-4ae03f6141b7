import styled from 'styled-components'

export const StyledUserCardName = styled.div`{
  width: 46px;
  height: 46px;
  background-color: ${({ theme: { color } }) => color.general.gray4};
  border-radius: 50%;
  display: block;
}`

export const StyledContainerWrapper = styled.div`{
  width: calc(100% - 85px);
}`

export const StyledContentWrapper = styled.div`{
  display: flex;
  align-items: center;
  gap: 6px;
}`
