import { useEffect, useState } from 'react'
import { TABLE_MODES_BY_TYPES } from '../../config/table'
import { TABLE_WIDTH_LG, TABLE_WIDTH_SM } from '../../../constants'

export const useTableModeAndSize = ({
  type,
  topHeight,
  gap,
  gridRef,
}: {
  type: string
  topHeight: number
  gap: number
  gridRef: {
    current: { resetAfterColumnIndex: (index: number) => void; resetAfterRowIndex: (index: number) => void } | null
  } | null
}) => {
  const [isDropdownSearch, setIsDropdownSearch] = useState(false)

  const tableModeInitial = TABLE_MODES_BY_TYPES[type]?.[0] || 'table'
  const [tableMode, setTableMode] = useState(tableModeInitial)

  useEffect(() => {
    setTableMode(tableModeInitial)
  }, [type, tableModeInitial])

  // @ts-ignore
  const getMaxTableHeight = (tableHeaderHeight: number, rowHeight?: number) => {
    if (typeof window !== 'undefined' && topHeight) {
      if (tableMode === 'table') {
        return window.innerHeight - topHeight - tableHeaderHeight - gap
      } else if (tableMode === 'cards') {
        return window.innerHeight - topHeight - gap
      }
    }

    return null
  }

  const onListResize = (width: number) => {
    if (width < TABLE_WIDTH_SM) {
      setIsDropdownSearch(true)
    } else {
      setIsDropdownSearch(false)
    }

    if (gridRef && gridRef.current) {
      if (gridRef.current.resetAfterColumnIndex) {
        gridRef.current.resetAfterColumnIndex(0)
      }
      if (gridRef.current.resetAfterRowIndex) {
        gridRef.current.resetAfterRowIndex(0)
      }
    }

    const tableModes = TABLE_MODES_BY_TYPES[type]

    if (tableModes) {
      if (width <= TABLE_WIDTH_LG) {
        if (tableModes.includes('cards')) {
          setTableMode('cards')
        }
      }

      if (width > TABLE_WIDTH_LG) {
        if (tableModes.includes('table')) {
          setTableMode('table')
        }
      }
    }
  }

  const onTableResize = ({ width }: {width: number }) => {
    if (width < TABLE_WIDTH_SM) {
      setIsDropdownSearch(true)
    } else {
      setIsDropdownSearch(false)
    }

    const tableModes = TABLE_MODES_BY_TYPES[type]

    if (tableModes) {
      if (width < TABLE_WIDTH_LG) {
        if (tableModes.includes('cards')) {
          setTableMode('cards')
        }
      }
    }
  }

  return { tableMode, onTableResize, onListResize, getMaxTableHeight, isDropdownSearch }
}
