import dot from 'dot-object'
import { isObjectEmpty } from '@aidsupply/components'
import { EDITABLE_TABLE_NAVBAR_FORM_CONFIG } from '../config/editableTableNavBarForms'
import { DEFAULT_TABS } from './FormBlockTabs'
import <PERSON>Field from './FormField'
import { IFormFieldsProps } from './interfaces'

const FormFields = (props: IFormFieldsProps) => {
  const {
    fields,
    flexDirection = 'column',
    formErrors,
    formSubmit,
    formConfig,
    tabKey,
    mainFormData,
    formValuesCustom,
    initialValues,
    isReadOnly,
    isSubmitOnBlur,
    labelKey,
    labelType,
    optionsData,
    sidebarFormId,
    submitByEnterPressed,
    type,
    updateCheckbox,
    updateInput,
    updateSelect,
    validateField,
    validationRules,
  } = props
  const platformName = 'aidsupply'

  if (!Array.isArray(fields)) {
    return null
  }

  const filteredFields = fields.filter(
    (field) => !field.platformsToShowOn || field.platformsToShowOn.includes(platformName as never)
  )

  return (
    filteredFields &&
    filteredFields.map((field) => {
      const withTabs = (
        ((EDITABLE_TABLE_NAVBAR_FORM_CONFIG as Record<string, { withTabs?: string[] }>)[type as string]?.withTabs) ||
        formConfig?.withTabs ||
        Object.keys(DEFAULT_TABS)
      ) as string[]
      const isTabs = withTabs.includes(field?.key as string)
      const name = isTabs ? `${field.key}.${tabKey}` : field.key

      return (
        <FormField
          formFlexDirection={flexDirection}
          field={field}
          formSubmit={formSubmit}
          formError={
            !isObjectEmpty(formErrors) &&
            ((formErrors as Record<string, string>)?.[name as string] || dot.pick(name as string, formErrors))
          }
          formErrors={field.validationRules && !isObjectEmpty(formErrors) && formErrors}
          formValuesCustom={formValuesCustom}
          initialValues={initialValues}
          isReadOnly={isReadOnly}
          isSubmitOnBlur={isSubmitOnBlur}
          /// withTabs={isTabs}
          key={field.label || field.key}
          labelKey={labelKey}
          labelType={labelType}
          mainFormData={mainFormData}
          name={name as string}
          optionsData={optionsData}
          sidebarFormId={sidebarFormId}
          submitByEnterPressed={submitByEnterPressed}
          type={type}
          updateCheckbox={updateCheckbox}
          updateInput={updateInput}
          updateSelect={updateSelect}
          validateField={validateField}
          validationRules={validationRules}
        />
      )
    })
  )
}

export default FormFields
