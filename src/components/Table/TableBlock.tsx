import queryString from 'query-string'
import { JSX, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import { createSearchParams, useLocation, useNavigate, useParams } from 'react-router'
import { useTheme } from 'styled-components'
import {
  Icon,
  Loader,
  ScreenContext,
  Table,
  Typography,
  usePrevious,
  VirtualizedList,
} from '@aidsupply/components'

import { useMappedState } from '../../hooks'
import { sortObjectToUrlString } from '../../utils/table'
import {
  selectAllSystemCollections,
  selectApiUrlParam,
  selectAreFiltersChosen,
  selectDataByType,
  selectDataTypeObject,
  selectMainDataInitialized,
  selectMainDataLoading,
  selectTableDataCount,
  selectUpsertInProgress,
  selectUserDetails,
} from '../../redux-saga/selectors'
import { dataFetchWithFacets } from '../../redux-saga/reducers/data'
import { ICurrentUser } from '../../redux-saga/reducers/user'
import { TLanguages } from '../../locales'
import { ISubmenu } from '../NavMenu/config'
import { getColumns } from '../config/columns'
import { useTableModeAndSize } from './hooks/useTableModeAndSize'
import { useSorting } from './hooks/useSorting'
import EmptyScreen from '../EmptyScreen/EmptyScreen'
import { RIGHT_PANEL_CREATE_ROUTES } from '../config'
import {
  GAP,
  HEADER_HEIGHT,
  NAVBAR_HEIGHT_LG,
  NAVBAR_HEIGHT_SM,
  SCREEN_WIDTH,
  TABLE_CARD_PADDING,
  TABLE_HEADER_HEIGHT,
  TABLE_ROW_GAP,
  TABLE_ROW_HEIGHT_LG,
  TABLE_ROW_HEIGHT_SM,
} from '../../constants'
import TableTitleBlock from './components/TableTitleBlock'
import NavBar from './NavBar'
import { StyledContent } from './styled'
import { usePrevWithoutEmptyValuesSave } from '../../hooks/usePrevWithoutEmptyValuesSave.ts'

export interface IColumn {
  key: string
  dataKey: string
  sortable?: boolean
  width: number
  flexGrow?: string
  flexShrink?: number
  className?: string
  resizable?: boolean
  getIsHidden?: ({ role }: { role: string }) => boolean
  headerRenderer?: ({ column }: { column: { key: string; title?: string } }) => JSX.Element
  cellRenderer?: ({ cellData, column }: { cellData: string; column: { key: string } }) => JSX.Element
  type?: string
}

export interface ITableBlock {
  tableCardHeight: number
  className?: string
  navBarProps?: Record<string, unknown>
  queryParams?: { key: string; value: number }[]
  rowKey?: string
  tableTitleChildren?: React.ReactNode
  TableCardContent?: React.JSX.Element
  editable?: boolean
  emptyScreenChildren?: React.ReactNode
  isDeletionByState?: boolean
  isMainTable?: boolean
  isReadOnly?: boolean
  isTableWithTitle?: boolean
  isPaginationDisabled?: boolean
  selectable?: boolean
  textsChosenLng?: string
  iconName?: string
}

const TableBlock = ({
  className,
  editable,
  emptyScreenChildren,
  isDeletionByState,
  isMainTable = true,
  isReadOnly,
  isTableWithTitle = true,
  TableCardContent,
  isPaginationDisabled,
  navBarProps = {},
  queryParams,
  rowKey,
  selectable,
  tableCardHeight,
  tableTitleChildren,
  textsChosenLng,
  iconName,
  ...rest
}: ITableBlock) => {
  const dispatch = useDispatch()
  const {
    t,
    i18n: { language },
  } = useTranslation(['table', 'general'])
  const location = useLocation()
  const navigate = useNavigate()
  const theme = useTheme()
  const { width: screenWidth } = useContext(ScreenContext)
  const searchParsed = queryString.parse(location.search)
  const prevSearchValue = usePrevious(location.search) || ''

  const params = useParams()
  const editedItemId = params?.rightPanelId

  const activeRowId = editedItemId
  const prevRowId = usePrevious(activeRowId)

  const gridRef = useRef<{
    resetAfterColumnIndex: (index: number) => void
    resetAfterRowIndex: (index: number) => void
  } | null>(null)
  const contentRef = useRef(null)

  const upsertInProgress = useMappedState(selectUpsertInProgress)
  const prevUpsertInProgress = usePrevious(upsertInProgress)
  const inProgress = useMappedState(selectMainDataLoading)
  const isDataInitialized = useMappedState(selectMainDataInitialized)
  const user = useMappedState(selectUserDetails) as ICurrentUser
  const system = useMappedState(selectAllSystemCollections)
  const typeData = useMappedState(selectDataTypeObject) as ISubmenu
  const type = typeData?.key
  const data = useMappedState(selectDataByType(type))
  const totalItems = useMappedState(selectTableDataCount)
  const areFiltersChosen = useMappedState(selectAreFiltersChosen)
  const withCustomCards = type === 'products' || type === 'inquiry_items'

  const [tableData, setTableData] = useState(data)
  const tableDataWithoutDeleted = isDeletionByState
    ? // TODO: extend the type for items via | or create a separate interface when working with other types
      tableData?.filter((item: Record<string, unknown>) => item.state !== 'deleted')
    : tableData

  useEffect(() => {
    setTableData(data)
  }, [data])

  const selectedApiUrlParam = useMappedState(selectApiUrlParam) as string

  const isEmptyTable =
    isDataInitialized &&
    !areFiltersChosen &&
    !tableDataWithoutDeleted?.length &&
    !searchParsed?.searchValue &&
    inProgress === false

  const gap = GAP
  const tableRowHeight =
    type === 'products' || type === 'inquiry_items' ? TABLE_ROW_HEIGHT_LG : TABLE_ROW_HEIGHT_SM
  const tableHeaderHeight = TABLE_HEADER_HEIGHT
  const tableRowGap = TABLE_ROW_GAP
  const navbarHeight = screenWidth <= SCREEN_WIDTH ? NAVBAR_HEIGHT_LG : NAVBAR_HEIGHT_SM

  const isMobile = screenWidth && screenWidth < theme.breakpoints.lg

  const isPageWithHeader = screenWidth && screenWidth < theme.breakpoints.xl

  const topHeight =
    (isPageWithHeader ? HEADER_HEIGHT : 0) + (isTableWithTitle ? HEADER_HEIGHT : 0) + navbarHeight

  const [gridColCount, setGridColCount] = useState(1)

  const { tableMode, onListResize, onTableResize, getMaxTableHeight, isDropdownSearch } = useTableModeAndSize(
    {
      type,
      topHeight,
      gap,
      gridRef,
    }
  )

  const columnsFromConfig =
    (getColumns(type, language as TLanguages, tableMode, t) as IColumn[]) || ([] as IColumn[])

  const columns = columnsFromConfig?.filter(
    (column) => !column?.getIsHidden || !column?.getIsHidden({ role: user.role || '' })
  )

  useEffect(() => {
    if (tableMode === 'table') {
      setGridColCount(1)
    }
  }, [tableMode])

  const maxTableHeight = getMaxTableHeight(
    tableHeaderHeight,
    tableMode === 'table' ? tableRowHeight : tableCardHeight || tableRowHeight
  )

  const maxTableRowsCount = type
    ? tableMode === 'table'
      ? Math.floor((maxTableHeight as number) / (tableRowHeight + tableRowGap))
      : Math.floor(((maxTableHeight as number) - TABLE_CARD_PADDING) / (tableCardHeight + GAP)) * gridColCount
    : undefined

  const maxListRowsCount = useRef<number | null>(null)

  const { sortState, onColumnSort } = useSorting(type)

  const prevType = usePrevWithoutEmptyValuesSave(type)
  const prevMaxTableRowsCount = usePrevWithoutEmptyValuesSave(maxTableRowsCount)

  useEffect(() => {
    if (
      maxTableRowsCount &&
      (prevType !== type ||
        prevMaxTableRowsCount !== maxTableRowsCount ||
        prevSearchValue !== location.search)
    ) {
      maxListRowsCount.current = maxTableRowsCount

      dispatch(
        dataFetchWithFacets({
          type,
          locationSearch: location.search,
          sortString: sortObjectToUrlString(sortState),
          pageLimit: !isPaginationDisabled && maxTableRowsCount,
          pageOffset: Number(
            !isPaginationDisabled &&
              (!prevSearchValue && searchParsed.searchValue
                ? undefined
                : searchParsed.page && (Number(searchParsed.page) - 1) * maxTableRowsCount)
          ),
          tableMode,
          query: [...(queryParams || [])],
          lng: language as TLanguages,
          apiUrlParam: selectedApiUrlParam,
        })
      )
    }
  }, [type, location.search, maxTableRowsCount, selectedApiUrlParam, queryParams, prevUpsertInProgress])

  useEffect(() => {
    if (maxTableRowsCount && prevUpsertInProgress && !upsertInProgress) {
      dispatch(
        dataFetchWithFacets({
          type,
          locationSearch: location.search,
          sortString: sortObjectToUrlString(sortState),
          pageLimit: !isPaginationDisabled && maxTableRowsCount,
          pageOffset: Number(
            !isPaginationDisabled &&
              (!prevSearchValue && searchParsed.searchValue
                ? undefined
                : searchParsed.page && (Number(searchParsed.page) - 1) * maxTableRowsCount)
          ),
          tableMode,
          query: [...(queryParams || [])],
          lng: language as TLanguages,
          apiUrlParam: selectedApiUrlParam,
        })
      )
    }
  }, [upsertInProgress, prevUpsertInProgress])

  useEffect(() => {
    if (searchParsed.page && !prevSearchValue && searchParsed.searchValue) {
      delete searchParsed.page
      navigate({
        search: createSearchParams(queryString.stringify(searchParsed)).toString(),
      })
    }
  }, [prevSearchValue, searchParsed])

  const onRowClick = (data: { rowKey: string }) => {
    navigate({
      pathname: data.rowKey ? `${data.rowKey}` : type,
      search: createSearchParams(queryString.stringify(searchParsed)).toString().replace(/%2C/g, ','),
    })
  }

  const onSetGridColCount = (width: number) => {
    if (withCustomCards) {
      setGridColCount(width < 500 ? 1 : width < 750 ? 2 : width < 1050 ? 3 : 4)
    } else {
      setGridColCount(width < 650 ? 1 : width < 1050 ? 2 : 3)
    }
  }

  const isDataInProgress =
    !screenWidth ||
    (((!searchParsed.searchValue && !tableDataWithoutDeleted?.length) || !isDataInitialized) && inProgress)

  const cellProps = { system, lng: language as TLanguages, user, is_read_only: isReadOnly }

  const getTableComponent = () => {
    if (isDataInProgress) {
      return <Loader size="60px" top={`calc(50% - 30px)`} left={`calc(50% - 30px)`} />
    }

    if (isEmptyTable) {
      return <EmptyScreen btnLabelType={typeData?.labelSingle}>{emptyScreenChildren}</EmptyScreen>
    }

    switch (tableMode) {
      case 'table': {
        return (
          <Table
            cellProps={cellProps}
            columns={columns}
            data={tableDataWithoutDeleted}
            editable={!isReadOnly && editable}
            isReadOnly={isReadOnly}
            emptyText={t('general:notFound')}
            headerHeight={tableDataWithoutDeleted?.length === 0 ? maxTableHeight : tableHeaderHeight}
            maxHeight={maxTableHeight}
            onColumnSort={onColumnSort}
            onResize={onTableResize}
            onRowClick={onRowClick}
            rowClassName={({ rowData }: { rowData: { status: string; id: number } }) => {
              const classes = []
              if (rowData.status === 'inactive') {
                classes.push('inactive')
              }
              const currentRowId = rowData.id.toString()
              const isHighlightedRow =
                activeRowId && !RIGHT_PANEL_CREATE_ROUTES.includes(activeRowId)
                  ? activeRowId === currentRowId
                  : // @ts-ignore
                    prevRowId?.current === currentRowId

              if (isHighlightedRow) {
                classes.push('activeRow')
              }

              return classes.join(' ')
            }}
            rowHeight={tableRowHeight || 50}
            rowKey={rowKey || 'id'}
            selectable={selectable}
            sortState={sortState}
            textsChosenLng={textsChosenLng}
            {...(tableDataWithoutDeleted?.length === 0 && !type.endsWith('_items')
              ? { containerNotFound: true }
              : {})}
            blockKey={type}
            {...rest}
          />
        )
      }
      case 'cards': {
        return tableDataWithoutDeleted?.length ? (
          <VirtualizedList
            activeRowId={activeRowId}
            cellProps={{ container: { props: { cellProps } } }}
            columns={columns}
            columnCount={gridColCount}
            editedItemId={editedItemId}
            gridRef={gridRef}
            initialPageNumber={1}
            items={tableDataWithoutDeleted}
            itemsTotalCount={totalItems}
            onItemClick={onRowClick}
            onResize={onListResize}
            onSetColumnCount={onSetGridColCount}
            rowHeight={tableCardHeight || (type === 'products' || type === 'inquiry_items' ? 394 : 115)}
            rowKey={rowKey || 'id'}
            selectable={selectable}
            sortState={sortState}
            t={t}
            TableCardContent={TableCardContent}
            type={type}
            variant={tableMode === 'cards' && 'grid'}
            {...rest}
          />
        ) : (
          <>
            <Icon name="notFound" width={'70%'} height={'80%'} wrapperWidth={'80%'} wrapperHeight={'50%'} />
            <Typography margin="20px 0" textAlign="center" type="h2">
              {t('general:notFound')}
            </Typography>
          </>
        )
      }
    }
  }

  return (
    <>
      {isTableWithTitle && (
        <TableTitleBlock titleKey={typeData?.label} withMenuIcon iconName={iconName || type}>
          {!isMobile && tableTitleChildren}
        </TableTitleBlock>
      )}
      {!navBarProps.isHidden && isDataInitialized && (
        <NavBar
          maxTableRowsCount={0}
          isMainTable={isMainTable}
          rowsCount={maxTableRowsCount}
          isDropdownSearch={isDropdownSearch}
          onRowClick={onRowClick}
          tableMode={tableMode}
        />
      )}
      <StyledContent
        ref={contentRef}
        topHeight={topHeight}
        maxHeight={maxTableHeight as number}
        className={
          className || (tableDataWithoutDeleted?.length === 0 && tableMode === 'cards')
            ? 'notFoundSearch'
            : ''
        }
      >
        { getTableComponent()}
      </StyledContent>
    </>
  )
}

export default TableBlock
