import { useDispatch } from 'react-redux'

import { popupAlertShow } from '../redux-saga/reducers/common.ts'

export const useCopyToClipboard = () => {
  const dispatch = useDispatch()

  const copy = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        dispatch(
          popupAlertShow({
            contentKey: 'copied',
            id: Date.now(),
            type: 'success',
            timeout: 6000,
          })
        )
      })
      .catch(() => {
        console.error('Error copying to clipboard:', text)
      })
  }

  return copy
}
