import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { IFileDrafted } from '../../components/UploadFiles/Thumbnail'
import { IComment } from '../../commonInterfaces'

export interface ISideBar {
  initial: Record<string, unknown> | null,
  itemInProgress: null | boolean,
  error: null | string | boolean | unknown,
  upsertError: null | string | unknown,
  upsertInProgress: null | boolean,
}

export const sideBarSlice = createSlice({
  name: 'sideBar',
  initialState:  {
    initial: null,
    itemInProgress: null,
    error: null,
    upsertError: null,
    upsertInProgress: null,
  },
  reducers: {
    sidebarItemSet(state: ISideBar, action: PayloadAction<Record<string, unknown>>) {
      state.initial = action.payload
    },
    sideBarUpsertUser(state: ISideBar, _action: PayloadAction<Record<string, unknown>>) {
      state.upsertInProgress = true
    },
    sideBarUpsert(state: ISideBar, _action: PayloadAction<Record<string, unknown>>) {
      state.upsertInProgress = true
    },
    sideBarError(state: ISideBar, actions: PayloadAction<{ error: string | unknown }>) {
      state.upsertInProgress = false
      state.upsertError = actions.payload.error
    },
    sideBarSuccess(state: ISideBar) {
      state.upsertInProgress = false
      state.upsertError = false
    },
    sideBarUpsertSuccess(state: ISideBar, action: PayloadAction<{item?: Record<string, unknown>}>) {
      state.upsertInProgress = false
      state.error = false
      state.initial = action.payload.item?.photo
      ? { ...action.payload.item, photos: state.initial?.photos }
      : action?.payload.item || state.initial
    },
    sidebarFetchById(state: ISideBar, _action: PayloadAction<{id: string | number, type: string, reload: boolean | null}>) {
      state.itemInProgress = true
    },
    sidebarFetchByIdError(state: ISideBar, action: PayloadAction<{ error: string | unknown }>) {
      state.itemInProgress = false,
      state.error = action.payload.error
    },
    sidebarFetchByIdSuccess(state: ISideBar, action: PayloadAction<{item: Record<string, unknown>}>) {
      state.itemInProgress = false,
      state.error = null,
      state.initial = action.payload.item?.id ? action.payload.item : { ...action.payload.item, id: action.payload.item.id || '' }
    },
    sideBarItemPhotosSet(state: ISideBar, action: PayloadAction<{photos: Record<string, { active: IFileDrafted[], disabled: IFileDrafted[] }>, files: Record<string, unknown>}>) {
      state.initial = {
        ...state.initial,
        photos: action.payload.photos || state.initial?.photos,
        files: action.payload.files || state.initial?.files,
      }
    },
    sidebarItemClone(_state: ISideBar, _action: PayloadAction<{ id: number, type: string }>) {},
    instanceCreate(_state: ISideBar, _action: PayloadAction<{ type: string, requestBody: Record<string, unknown> }>) {},
    instanceCreateError(state: ISideBar, action: PayloadAction<{ error: unknown }>) {
      state.itemInProgress = false,
      state.upsertInProgress = null,
      state.error = action.payload.error
    },
    sidebarPostComment(state: ISideBar, _action: PayloadAction<{ entity_id: number, content: string }>) {
      state.upsertInProgress = true
    },
    sidebarPostCommentSuccess(state: ISideBar, action: PayloadAction<{ comment: IComment }>) {
      state.upsertInProgress = false,
      state.initial = {
        ...state.initial,
        comments: [
          ...(state.initial?.comments as IComment[]),
          action.payload.comment
        ]
      },
      state.error = false
    },
    sidebarPostCommentError(state: ISideBar, action: PayloadAction<{ error: unknown }>) {
      state.upsertInProgress = false,
      state.error = action.payload.error
    }
  }
})

export const {
  sidebarItemSet,
  sideBarUpsertUser,
  sideBarUpsert,
  sideBarError,
  sideBarSuccess,
  sideBarUpsertSuccess,
  sidebarFetchById,
  sidebarFetchByIdError,
  sidebarFetchByIdSuccess,
  sideBarItemPhotosSet,
  sidebarItemClone,
  instanceCreate,
  instanceCreateError,
  sidebarPostComment,
  sidebarPostCommentSuccess,
  sidebarPostCommentError
} = sideBarSlice.actions
export default sideBarSlice.reducer
