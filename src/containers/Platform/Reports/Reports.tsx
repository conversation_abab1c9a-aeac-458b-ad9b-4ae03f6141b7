import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import ReportsTableCard from './ReportsTableCard'

const Reports = () => {
  return (
    <GridLayout
      rightPanelComponent={<Outlet context={{}} />}
    >
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={ReportsTableCard as unknown as React.JSX.Element}
        tableCardHeight={90}
        iconName='fileCheckList'
      />
    </GridLayout>
  )
}

export default Reports
