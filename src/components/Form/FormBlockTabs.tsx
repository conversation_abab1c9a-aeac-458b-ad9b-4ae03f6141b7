import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { pick } from 'dot-object'
import { useTheme } from 'styled-components'
import { FlexRow, Icon, Tabs, Typography } from '@aidsupply/components'
import { SUPPORTED_LNGS } from '../../locales'
import FormFields from './FormFields'
import { IFieldsProps, IFormConfig, IFormFieldsProps } from './interfaces'
import { useSelector } from 'react-redux'
import { selectFormValuesWhole } from '../../redux-saga/selectors.ts'
import { IStore } from '../../configureStore.ts'

export const DEFAULT_TABS = {
  translations: 'en',
  seo: 'en',
  description: 'en',
  body: 'en',
}

interface IFormBlockTabsProps {
  blockKey: string
  defaultTabsValues?: Record<string, string>
  dropdownItems?: Record<string, string>[]
  formFieldsProps?: IFormFieldsProps
  formValuesCustom?: Record<string, unknown>
  getTabScreenCustom?: (tab: string) => React.ReactNode
  isLanguageTabs?: boolean
  onTabChange?: (tab: string) => void
  withTabsFieldsCustom?: string[]
  formConfig?: IFormConfig
  titleProps?: Record<string, unknown>
  type: string
}

const FormBlockTabs = ({
  blockKey,
  defaultTabsValues,
  dropdownItems,
  formConfig,
  formFieldsProps = {},
  formValuesCustom,
  getTabScreenCustom,
  isLanguageTabs,
  onTabChange,
  withTabsFieldsCustom,
  titleProps,
  type,
}: IFormBlockTabsProps) => {
  const { t } = useTranslation(['forms', 'table'])
  const { isReadOnly } = formFieldsProps
  const [tabs, setTabs] = useState<string[]>([])
  const [errorTabIndex, setErrorTabIndex] = useState({ index: -1 })
  const theme = useTheme()

  const fields = formFieldsProps.fields as IFieldsProps[]

  const reduxFromValues = useSelector((state: IStore) => selectFormValuesWhole(state, type))
  const formValues = formValuesCustom || reduxFromValues

  useEffect(() => {
    const inputWithError =
      formFieldsProps.formErrors &&
      Object.keys(formFieldsProps.formErrors).filter((error) => {
        return fields?.filter((field: IFieldsProps) => error.includes(field.key as string))
      })

    if (!inputWithError) {
      return
    }

    let errorId: string | undefined

    inputWithError.find((el) => {
      errorId = tabs.find((tab) => el.includes(`.${tab}`))
      return errorId
    })

    const index = tabs.indexOf(errorId as string)

    setErrorTabIndex({ ...errorTabIndex, index })
  }, [formFieldsProps.formErrors])

  const getTabOptions = () => {
    const withTabsFields = withTabsFieldsCustom || formConfig?.withTabs || Object.keys(DEFAULT_TABS)
    const tabsKeys = fields?.length
      ? withTabsFields.filter((key) => fields.some((field) => field.key === key))
      : withTabsFields

    if (!tabsKeys.length) {
      return null
    }

    const isDefaultTabsValues = (defaultTabsValues || DEFAULT_TABS)[blockKey as keyof typeof DEFAULT_TABS] !== undefined

    let tabs = isDefaultTabsValues
      ? [(defaultTabsValues || DEFAULT_TABS)[blockKey as keyof typeof DEFAULT_TABS]]
      : tabsKeys

    if (isDefaultTabsValues) {
      tabsKeys.forEach((key) => {
        const tabValueObj = pick(key, formValues) || {}

        Object.keys(tabValueObj).forEach((tabKeyFromValues) => {
          if (!tabs.includes(tabKeyFromValues) && tabValueObj[tabKeyFromValues]) {
            tabs = [...tabs, tabKeyFromValues]
          }
        })
      })
    }

    return tabs
  }

  useEffect(() => {
    const tabs = getTabOptions()

    if (tabs) {
      setTabs(tabs)
    }
  }, [formValues?.id])

  const getTabScreen = (tabKey: string) => {
    if (getTabScreenCustom) {
      return getTabScreenCustom(tabKey)
    } else {
      return <FormFields fields={fields} tabKey={tabKey} formValuesCustom={formValues} {...formFieldsProps} />
    }
  }

  const onDropItemClick = (id: string) => {
    setTabs([...tabs, id])
  }

  const getTabTitle = (title: string) => (
    <FlexRow id={title} gap="6px">
      {(isLanguageTabs || ['translations', 'texts', 'seo'].includes(blockKey)) && (
        <Icon width={16} height={12} name={`flag${title.toUpperCase()}`} />
      )}
      <Typography
        text={blockKey !== 'activity' ? title : t(title)}
        textTransform="uppercase"
        style={{
          fontWeight: 600,
          letterSpacing: '0',
        }}
        {...titleProps}
      />
    </FlexRow>
  )

  const getDropdownItems = () => {
    if (dropdownItems) {
      return dropdownItems
    } else {
      return SUPPORTED_LNGS.filter((lng) => !tabs.includes(lng)).map((lng) => ({
        id: lng,
        label: lng.toUpperCase(),
      }))
    }
  }

  return (
    <Tabs
      backgroundColor={theme.color.general.gray2}
      isDisabled={isReadOnly}
      withAddAction
      tabsTitles={tabs.map((tab) => getTabTitle(tab))}
      tabsContents={tabs.map((tab) => getTabScreen(tab))}
      dropdownItems={getDropdownItems()}
      onDropItemClick={onDropItemClick}
      onTabChange={onTabChange}
      typographyType="link"
      errorTabIndex={errorTabIndex}
    />
  )
}

export default FormBlockTabs
