import { TOOLBAR_CONFIG } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'

export const pagesRightPanelConfig: IFormConfig = {
  withTabs: ['translations', 'body'],
  validationRules: {
    'translations.en': ['required'],
    'body.en': ['required'],
    page_type: ['required'],
    organization_id: ['required'],
  },
  fields: {
    noTitle: [{
      key: 'state',
      label: 'state',
      optionsKeys: ['states'],
      component: 'dropdown',
    }],
    general: [
      {
        key: 'organization_id',
        label: 'organization',
        noTranslation: true,
        labelKey: 'name',
        valueKey: 'id',
        optionsKeys: ['organizations'],
        component: 'dropdown',
        isClearable: true,
        required: true,
        getDisabled: () => true
      },
      {
        key: 'page_type',
        label: 'type',
        labelKey: 'label',
        noTranslation: true,
        optionsKeys: ['pageTypes'],
        component: 'dropdown',
        required: true,
        getIsHidden: (formValues?: { id?: string | number, [key: string]: unknown }) => !!formValues?.id,
        onSelectValueChange: (val: Record<string, unknown> | string, setValuesChanged?: (prev: Record<string, unknown>) => Record<string, unknown>) => {
          // @ts-ignore
          setValuesChanged?.((prev) => ({
            ...prev,
            link: (val as Record<string, unknown>)?.label,
          }))
        },
      },
      {
        key: 'link',
        label: 'linkToPage',
        required: true,
        disabled: true,
      },
    ],
    translations: [
      {
        key: 'translations',
        label: 'title',
        placeholder: 'Your item title',
        required: true,
      },
      {
        key: 'body',
        label: 'texts',
        type: 'richText',
        toolbarOptions: TOOLBAR_CONFIG,
        required: true,
      },
    ],
    photos: [],
  },
}
