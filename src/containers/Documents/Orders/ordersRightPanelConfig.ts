import { CURRENCY, INQUIRY_WITH_DATE, RECIPIENT, STATE, SUPPLIER, WAREHOUSE } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'
import { TAXES_TYPE } from '../../../data'

export const ordersRightPanelConfig = (mode: 'create' | 'edit'): IFormConfig => {
  const supplies = {
    ...SUPPLIER,
    onSelectValueChange: (val: string | Record<string, unknown>, setValuesChanged?: (prev: Record<string, unknown>) => Record<string, unknown>) => {
      // @ts-ignore
      setValuesChanged((prev: Record<string, unknown>) => {
        return {
          ...prev,
          supplier_id: val,
          warehouse_id: '',
        }
      })
    },
  }

  const warehouse = {
    ...WAREHOUSE,
    required: true,
    getDisabled: (formValues?: Record<string, unknown>) => !formValues?.supplier_id,
    getOptionsFromFormValues: (formValues: Record<string, unknown>, optionsData?: Record<string, unknown>) => {
      return (
        (optionsData?.warehouses &&
        Object.values(optionsData.warehouses).filter(
          (warehouse) =>
          warehouse.organization_id === (formValues.supplier_id?.
          // @ts-ignore
          id || formValues.supplier_id))
        ) as Record<string, unknown>
      )
    },
  }

  return {
    validationRules: {
      recipient_id: ['required'],
      supplier_id: ['required'],
      warehouse_id: ['required'],
    },
    fields: {
      ...(mode === 'create' && {
      general: [
          { ...supplies },
          { ...warehouse },
          RECIPIENT,
        ],
      }),
      ...(mode === 'edit' && {
        general: [
          {
            ...STATE,
            getIsHidden: (formValues) => !formValues?.id,
            getOptionsFromFormValues: (formValues, optionsData) => {
              const allStates = Object.values(optionsData?.states || {})
    
              if (!formValues.order_items || !Array.isArray(formValues.order_items)) {
                return allStates.filter((state) => state.id !== 'posted')
              }
    
              return allStates
            },
          },
          { ...supplies },
          { ...warehouse },
          {
            key: 'external_number',
            label: 'externalNumber',
            component: 'input',
            getIsHidden: (formValues) => !formValues?.id,
          },
          RECIPIENT,
          {
            ...INQUIRY_WITH_DATE,
            onSelectValueChange: async (val: Record<string, unknown> | string, setValuesChanged?: (prev: Record<string, unknown>) => Record<string, unknown>) => {
              // @ts-ignore
              setValuesChanged((prev: Record<string, unknown>) => ({
                ...prev,
                recipient_id: typeof val === 'object' ? val?.recipient_id ||'' : '',
              }))
            },
            getIsHidden: (formValues) => !formValues?.id,
          },
          {
            ...CURRENCY,
            getIsHidden: (formValues) => !formValues?.id,
          },
          {
            key: 'tax_rate',
            label: 'taxRate',
            component: 'dropdown',
            options: TAXES_TYPE,
            getIsHidden: (formValues) => !formValues?.id,
          },
        ],
        order_items: [],
        activity: [],
        notes: [],
      }),
    }
  }
}
