import { TakeableChannel } from 'redux-saga'
import { all, call, put, select, takeLatest } from 'redux-saga/effects'
import { selectCurrentApiUrlParams, selectCurrentUserId, selectDataType, selectFileUploadState, selectSidebarInitialData, selectUserDetails } from '../selectors'
import { instanceCreate, instanceCreateError, ISideBar, sideBarError, sidebarFetchById, sidebarFetchByIdError, sidebarFetchByIdSuccess, sidebarItemClone, sideBarItemPhotosSet, sidebarItemSet, sidebarPostComment, sidebarPostCommentError, sidebarPostCommentSuccess, sideBarSuccess, sideBarUpsert, sideBarUpsertSuccess, sideBarUpsertUser } from '../reducers/sideBar'
import { NavigateFunction } from 'react-router'
import { getCurrentUserSuccess, ICurrentUser } from '../reducers/user'
import { isObjectEmpty } from '@aidsupply/components'
import { buildRequest, fetchData, showError } from '../../api'
import { DATA_TYPES_WITH_ITEMS } from '../../apiConstants'
import { DATA_TYPE_TO_SINGULAR_FORM, STOCKS_ITEM_DRAFTED_ERROR } from '../../constants'
import { popupAlertShow } from '../reducers/common'
import { updateSystemData } from '../reducers/data'
import { DEFAULT_VALUES_DATA } from '../../data/defaultValues'
import { IResponse } from './auth'
import { fetchFacets } from './dataFromCommonPackage'
import { getUploadedFilesData } from './fileUpload'
import { fileParse, fileParseError, IFileUpload } from '../reducers/fileUpload'
import { IFileDrafted } from '../../components/UploadFiles/Thumbnail'
import { FIELDS_TO_EXCLUDE_WHEN_CLONING } from '../../components/config/forms'
import { IComment } from '../../commonInterfaces'

function* doFetchById( { payload }: {
  payload: {
    id: string | number,
    type: string
  }
}) {
  let apiUrlParam
  const { id, type } = payload
  try {
    if (id) {
      if (id === 'new') {
        yield put(sidebarItemSet({item: DEFAULT_VALUES_DATA}))
      } else {

        // TODO: uncomment when setting up stock-items
        // if (type === 'stock-items') {
        //   const userOrganizationId = yield select(selectUserOrganizationId)
        //   const selectedOrganizationParam = yield select(selectApiUrlParam)

        //   apiUrlParam = selectedOrganizationParam || userOrganizationId
        // }

        const request: Request = yield buildRequest({
          operation: 'read',
          type,
          id,
          apiUrlParam,
        })

        const item:
          { item: Record<string, unknown> }
          | { items: Record<string, unknown>[] }
          | Record<string, unknown>
          = yield fetchData(request)

        yield showError(item as IResponse)

        let resolvedItem: Record<string, unknown> | undefined;

        if ('item' in item && item.item) {
          resolvedItem = item.item as Record<string, unknown>;
        } else if ('items' in item && Array.isArray(item.items) && item.items.length > 0) {
          resolvedItem = item.items[0];
        } else {
          resolvedItem = item;
        }

        if (resolvedItem) {
          yield put(sidebarFetchByIdSuccess({ item: resolvedItem }));
          return resolvedItem;
        }
      }
    }
  } catch (error) {
    yield put(sidebarFetchByIdError({ error }))
  }
}

export function* doUpsert({ payload }: {
  payload: {
    apiUrlParam?: string,
    id?: number,
    requestBody?: Record<string, unknown>,
    type: string,
    operation?: string,
    platform?: Record<string, unknown>,
    withoutPopups?: boolean,
    withErrorPopups?: boolean
  }
}) {
  const { apiUrlParam, id, requestBody, type, operation, withoutPopups, withErrorPopups } = payload

  try {
    const request: Request  = yield buildRequest({
      operation: operation || (id ? 'update' : 'create'),
      type: type === 'users' && !id ? 'userInvite' : type,
      id,
      requestBody,
      apiUrlParam,
    })

    const res: IResponse = yield fetchData(request)

    const withoutErrorPopups = !withErrorPopups && withoutPopups && res.detail !== STOCKS_ITEM_DRAFTED_ERROR

    yield showError(res, withoutErrorPopups)

    yield put(sideBarSuccess())
    if (!withoutPopups) {
      yield put(popupAlertShow({contentKey: 'dataSuccessfullySaved', type: 'success'})
      )
    }
    return res
  } catch (error) {
    console.log(error)
    yield put(sideBarError({ error }))
    yield put(popupAlertShow({contentKey: 'errorInSavingData', type: 'error'}))
  }
}

export function* doFileParse({ payload }: {
  payload: {
    entity_type: string,
    file: File,
    id: number,
    entityTypeId: string,
    is_replace_existing: boolean
  }
}) {
  const sidebarItemData: Record<string, unknown>  = yield select (selectSidebarInitialData)
  const { entity_type, file, id, is_replace_existing, entityTypeId } = payload
  const entityType = entity_type.replace('ParseFile', '')

  try {
    const fd = new FormData()
    fd.append('file', file)
    // @ts-ignore
    fd.append(entityTypeId, id)
    // @ts-ignore
    fd.append('is_replace_existing', is_replace_existing)

    console.log(fd)
    const request: Request = yield buildRequest({
      operation: 'create',
      type: entity_type,
      requestBody: fd,
    })

    const res: IResponse = yield fetchData(request)

    yield showError(res)

    return res
  } catch (error) {
    yield put(fileParseError({
      payload: { error: error as Record<string, unknown> | string, entity_type: DATA_TYPE_TO_SINGULAR_FORM[entityType] || entityType },
    }))
    yield put(sidebarItemSet({ ...sidebarItemData, is_being_processed: false }))
  }
}

function* doCloneSidebarItem({ payload: { id, type } }:
  { payload:
    {
      id: number,
      type: string
    }
  }) {
  let sidebarItemData: ISideBar = yield select(selectSidebarInitialData)
  try {
    if (!sidebarItemData) {
      sidebarItemData = yield doFetchById({ payload: { id, type }})
    }

    const itemToClone = { ...sidebarItemData }
    const itemTransformed = Object.keys(itemToClone).reduce<Record<string, unknown>>((acc, curr) => {
      if (!FIELDS_TO_EXCLUDE_WHEN_CLONING.includes(curr)) {
        return { ...acc, [curr]: (itemToClone as Record<string, unknown>)[curr] }
      }
      return acc
    }, {})
    yield put(sidebarItemSet(itemTransformed))
  } catch (error) {
    yield put(sidebarFetchByIdError({ error }))
  }
}

// TODO: uncomment when setting up type from DATA_TYPES_WITH_ITEMS
// function* upsertCollectionWithItems({ type, id, requestBody }: { type: string, id: number, requestBody: Record<string, unknown> }) {
//   try {
//     let collectionResult
//     let collectionWithItemsResult

//     const itemsKey = type === 'enumerations' ? 'options' : type === 'shipments' ? 'shipment_items' : 'items'
//     const allDataKeys = Object.keys(requestBody)
//     const itemsData = requestBody[itemsKey] as Record<string, unknown>[]

//     const mainData =
//       !(allDataKeys?.length === 1 && itemsData) &&
//       Object.keys(requestBody).reduce((mainData, allDataKey) => {
//         if (allDataKey !== itemsKey) {
//           return { ...mainData, [allDataKey]: requestBody[allDataKey] }
//         }
//         return mainData
//       }, {})

//     if (mainData) {
//       collectionResult = yield doUpsert({
//         payload: {
//           id,
//           type,
//           requestBody: mainData,
//         },
//       })
//     }

//     if (type !== 'invoices' && itemsData) {
//       // @ts-ignore
//       collectionWithItemsResult = yield doUpsert({
//         payload: {
//           apiUrlParam: id || collectionResult.values?.[0]?.id,
//           type: `${type}Items`,
//           // @ts-ignore
//           requestBody: itemsData,
//           withoutPopups: !!mainData,
//         },
//       })
//     }

//     return collectionWithItemsResult || collectionResult
//   } catch (error) {
//     console.log(error)
//   }
// }

function* doUpsertFiles({ id, type, requestBody }: {
  id: string,
  type: string,
  requestBody: Record<string, unknown>
}) {
  // TODO: errors
  const fileUpload: IFileUpload = yield select(selectFileUploadState)
  let filesFormDataObject: {
    photos?: Record<string, { active: IFileDrafted[], disabled: IFileDrafted[] }>
  } = {}

  const filesType = fileUpload.filesType || (requestBody.photos ? 'photos' : 'files')

  if (!isObjectEmpty(fileUpload.files)) {
    filesFormDataObject = yield getUploadedFilesData({ type, requestBody, id, filesType })
  }

  // @ts-ignore
  yield put(sideBarItemPhotosSet({
    // @ts-ignore
    [filesType]: filesFormDataObject?.[filesType] || requestBody[filesType]
  }))

  if (filesFormDataObject || requestBody.filesNewStates) {
    if (requestBody.filesNewStates) {
      yield all(
        Object.entries(requestBody.filesNewStates).map(([fileId, fileState]) => {
          return call(doUpsert, {
            payload: {
              id: +fileId.replace('id-', ''),
              type: 'filesUpload',
              requestBody: { state: fileState },
              withErrorPopups: true,
            },
          })
        })
      )
    }
  }

  delete requestBody.photos
  delete requestBody.files
  delete requestBody.filesNewStates

  // @ts-ignore
  return filesFormDataObject?.primaryPhotoObj
}

export function* doUpsertSidebar({ payload}: {
  payload: {
    id: number | undefined
    type: string
    requestBody: Record<string, unknown>
    navigate: NavigateFunction
    skipNavigate: boolean
    skipFileUpload: boolean
    forceNavigate?: boolean
  },
}) {
  const { id, type, requestBody: requestBodyArg, skipFileUpload, navigate, skipNavigate, forceNavigate } = payload
  let requestBody
  try {
    if (skipFileUpload) {
      requestBody = { ...requestBodyArg }
    } else {
      // @ts-ignore
      const primaryPhotoObj = yield doUpsertFiles({ id, type, requestBody: requestBodyArg })
      requestBody = { ...requestBodyArg, ...(primaryPhotoObj || {}) }
    }

    let result: IResponse

    if (type === 'stock-items') {
      // TODO: add when setting up type 'stock-items
      // yield upsertStockItems({ data: args, navigate })
      return
    } else if (isObjectEmpty(requestBody)) {
      yield put(sideBarUpsertSuccess({}))
      return
    } else if (DATA_TYPES_WITH_ITEMS.includes(type)) {
       // TODO: add when setting up type from DATA_TYPES_WITH_ITEMS
      // result = yield upsertCollectionWithItems({ type, id, requestBody })
    } else {
      result = yield doUpsert({
        payload: { ...payload, requestBody },
      })

      if (result) {
        yield showError(result)

        // TODO: move this code outside if block when rid of todo above
        // const itemToSet = result.value || result?.values?.[0] || result
        let itemToSet: { value: Record<string, unknown> }
        | { values: Record<string, unknown>[] }
        | Record<string, unknown>

        if ('value' in result) {
          itemToSet = result.value as Record<string, unknown>
        } else if ('values' in result && Array.isArray(result.values)) {
          itemToSet = result.values[0]
        } else {
          itemToSet = result
        }

        // TODO: move this code outside if block when rid of todo above
        yield put(sideBarUpsertSuccess({ item: itemToSet }))

        // TODO: move this code outside if block when rid of todo above
        yield put(
          updateSystemData({
            type,
            status: id ? 'update' : 'create',
            itemToSet,
          })
        )

        // TODO: move this code outside if block when rid of todo above
        if ((!id && !skipNavigate) || forceNavigate) {
          navigate({
            pathname: `../${(itemToSet as Record<string, unknown>).id}`,
            search: location.search,
          })
        }

        // TODO: move this code outside if block when rid of todo above
        return itemToSet
      }
    }

    const queryParams: {
      key: string;
      value: string | number;
    }[] = yield select(selectCurrentApiUrlParams)

    yield fetchFacets({
      type,
      queryParams,
    })

  } catch (error) {
    console.log(error)
    yield put(sideBarError({error}))
  }
}

function* doUpsertUser({ payload }: {
  payload: {
    id: number
    type: string
    requestBody: Record<string, unknown>
    navigate: NavigateFunction
  }
}) {
  try {
    const { id, type, requestBody, navigate } = payload

    const currentUser: ICurrentUser = yield select(selectUserDetails)

    const result: IResponse = yield doUpsertSidebar({
      payload: {
        id,
        type,
        requestBody,
        navigate,
        skipNavigate: true,
        skipFileUpload: true,
      }
    })

    // TODO: uncomment when add setUserRole
    // if (requestBody.role) {
    //   yield put(setUserRole(requestBody.role, result.id))
    // }

    if (id === currentUser.id) {
      yield put(getCurrentUserSuccess({
        ...currentUser ,
        ...result,
        profile_pic: result.profile_pic as string || currentUser.profile_pic
      }))
    }

    if (navigate && !id) {
      navigate({
        pathname: `../${result.id}`,
        search: location.search,
      })
    }
  } catch (error) {
    console.log(error)
    yield put(sideBarError({ error }))
  }
}

function* doPostComment({ payload }: {
  payload: {
    entity_id: number
    content: string
  }
}) {
  const { entity_id, content } = payload
  const entity_type: string = yield select(selectDataType)
  const user_id: number = yield select(selectCurrentUserId)

  try {
    const request: Request = yield buildRequest({
      type: 'comments',
      apiMethod: 'POST',
      requestBody: {
        content: content,
        entity_type: DATA_TYPE_TO_SINGULAR_FORM[entity_type],
        entity_id,
        user_id,
      },
    })

    const res: IComment = yield fetchData(request)
  
    yield showError(res as unknown as IResponse)

    yield put(sidebarPostCommentSuccess({comment: res}))
    return res
  } catch (error) {
    console.log(error)
    yield put(sidebarPostCommentError({error}))
  }
}

function* doCreateInstance(payload: {
  payload: {
    requestBody: Record<string, unknown>
    type: string
  }
}) {
  const { requestBody, type } = payload.payload

  try {

    let result: IResponse
    if (DATA_TYPES_WITH_ITEMS.includes(type)) {
      // TODO: uncomment when setting up type from DATA_TYPES_WITH_ITEMS
      // result = yield upsertCollectionWithItems({ requestBody, type, id: 0 })
    } else {
      result = yield doUpsert({ payload: { requestBody, type } })

      if (!result || result.detail) {
        throw new Error(result.detail)
      }
    }
    
    // @ts-ignore
    const itemToSet = result.items?.[0] || result.value || (result.values && result.values[0])

    if (itemToSet?._id) {
      itemToSet.id = itemToSet._id
    }

    if (type === 'organizations') {
      yield put(
        updateSystemData({
          itemToSet,
          type,
          status: 'create',
        })
      )
    }

  } catch (error) {
    console.log(error)
    yield put(instanceCreateError({ error }))
  }
}

export default function* sideBarSaga() {
  yield takeLatest(sidebarFetchById.type as unknown as TakeableChannel<unknown>, doFetchById)
  yield takeLatest(sideBarUpsertUser.type as unknown as TakeableChannel<unknown>, doUpsertUser)
  yield takeLatest(sideBarUpsert.type as unknown as TakeableChannel<unknown>, doUpsertSidebar)
  yield takeLatest(fileParse.type as unknown as TakeableChannel<unknown>, doFileParse)
  yield takeLatest(sidebarItemClone.type as unknown as TakeableChannel<unknown>, doCloneSidebarItem)
  yield takeLatest(instanceCreate.type as unknown as TakeableChannel<unknown>, doCreateInstance)
  yield takeLatest(sidebarPostComment.type as unknown as TakeableChannel<unknown>, doPostComment)
}
