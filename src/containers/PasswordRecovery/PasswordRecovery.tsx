import { useTheme } from 'styled-components'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { Link as RouterLink, useNavigate} from 'react-router'
import { Button, Typography, UiLink } from '@aidsupply/components'
import { INTRO_PAGES_IMG } from '../../constants'
import IntroScreenWrapper from '../IntroScreen/IntroScreenWrapper'
import Form from '../../components/Form/Form'
import { userRequestResetPassword } from '../../redux-saga/reducers/user'

const PasswordRecovery = () => {
  const theme = useTheme()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { t } = useTranslation('signIn')
  const initialValues = { email: '', password: '' }

  const onFormSubmit = (values: { email: string }) => {
    dispatch(userRequestResetPassword({ ...values, navigate }))
  }

  return(
    <IntroScreenWrapper type="passwordRecovery" theme={theme} imageUrl= {INTRO_PAGES_IMG} text={t('passwordRecovery')}>
      <Form
        type="passwordRecovery"
        initialValues={initialValues}
        onSubmit={onFormSubmit as unknown as (values: Record<string, unknown>) => void}
        validationRules={{
          email: ['required', 'email'],
        }}
        fields={
          [{
            label: 'email',
            key: 'email',
            labelType: 'top',
            placeholder: '<EMAIL>',
          }]
        }
      >
        <Button
          fullWidth
          variant="primary"
          type="submit"
          uppercase={false}
          theme={theme}
        >
          {t('recoverPassword')}
        </Button>
      </Form>
      <div className="inscriptionWrapper">
      <Typography type="body1">{t('backTo')}</Typography>
      <Typography type="button1">
      <UiLink Link={RouterLink} to="/signin" className="auth" themeColor="general.dark">
        {t('toSignIn')}
        </UiLink>
      </Typography>
      </div>
    </IntroScreenWrapper>
  )
}

export default PasswordRecovery
