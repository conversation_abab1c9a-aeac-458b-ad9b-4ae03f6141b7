import { createSelector } from '@reduxjs/toolkit'
import { pick, object } from 'dot-object'
import merge from 'deepmerge'

import { isObjectEmpty } from '@aidsupply/components'
import { IStore } from '../configureStore'
import { INotification } from './reducers/common'
import { ISystem } from './reducers/data'
import { ICurrentUser } from './reducers/user'
import { IFieldsProps } from '../components/Form/interfaces.ts'

export const mapStateToProps = (state: IStore) => state

// user
export const selectUserToken = (state: IStore) => state.user.token
export const selectUserState = (state: IStore) => state.user
export const selectCurrentUserId = (state: IStore) => state.user.details?.id
export const selectUserInProgressAndError = (state: IStore) => ({
  inProgress: state.user.inProgress as boolean,
  error: state.user.error as boolean,
})
export const selectUserSystemParams = (state: IStore) => ({
  isAuthenticated: state.user.isAuthenticated,
  isAppInitialized: state.user.isAppInitialized,
  inProgress: state.user.inProgress,
})
export const selectUserDetails = (state: IStore) => ({ ...state.user.details, token: state.user.token })
export const selectUserStatus = (state: IStore) => (state.user.details as ICurrentUser).status
export const selectUserRole = (state: IStore) => (state.user.details as ICurrentUser).role
export const selectUserOrganizationId = (state: IStore) => (state.user.details as ICurrentUser).organization_id

// common
export const selectPopupAlerts = (state: IStore) => state.common.popupAlerts
export const selectNotifications = (state: IStore) => state.common.notifications as INotification[]
export const selectIsNotificationsOpened = (state: IStore) => state.common.isNotificationsOpened
export const selectIsNavMenuExtended = (state: IStore) => state.common.isNavMenuExtended
export const selectIsRightPanelExtended = (state: IStore) => state.common.isRightPanelExtended
export const selectIsLeftPanelExtended = (state: IStore) => state.common.isLeftPanelExtended
export const selectIsRightPanelExtendedWider = (state: IStore) => state.common.isRightPanelExtendedWider

// fileUpload
export const selectFileUploadState = (state: IStore) => state.fileUpload
export const selectIsCurrentFilesInDraft = (state: IStore) => {
  const currentFiles = state.fileUpload.files
  return (
    !state.fileUpload.inProgress &&
    !!Object.values(currentFiles)?.length &&
    Object.values(currentFiles).some((file) => file?.length)
  )
}

// data
export const selectAllSystemCollections = (state: IStore) => state.data.system
export const selectSystemInProgress = (state: IStore) => state.data.systemInProgress
export const selectDataTypeObject = (state: IStore) => state.data.type
export const selectDataType = (state: IStore) => state.data.type.key
export const selectDataByType = (type: string) => (state: IStore) => {
  return Array.isArray(type)
    ? type.reduce((acc, type) => {
        acc[type] = state.data[type]
        return acc
      }, {})
    : state.data[type]
}
export const selectSystemCollections = (types: Array<keyof ISystem>) => (state: IStore) => {
  return Array.isArray(types)
    ? types.reduce((acc: Record<string, Record<string, unknown>>, type) => {
        acc[type] = state.data.system[type] as Record<string, unknown>
        return acc
      }, {})
    : state.data.system[types]
}
export const selectApiUrlParam = (state: IStore) => state.data.apiUrlParamsCurrent
export const selectMainDataInitialized = (state: IStore) => state.data.isDataInitialized
export const selectMainDataLoading = (state: IStore) => state.data.inProgress
export const selectTableDataCount = (state: IStore) => state.data.totalItems
export const selectCurrentApiUrlParams = (state: IStore) => state.data.apiUrlParamsCurrent
export const selectDashboard = (state: IStore) => state.data.dashboard
export const selectRole = (state: IStore) => state.user.details.role

// sideBar
export const selectSidebarInitialData = (state: IStore) => state.sideBar.initial
export const selectSidebarState = (state: IStore) => state.sideBar
export const selectUpsertInProgress = (state: IStore) => state.sideBar.upsertInProgress
export const selectSidebarCommentsData = (state: IStore) => state.sideBar.initial?.comments

// filters
export const selectFiltersState = (state: IStore) => state.filters
export const selectAreFiltersChosen = (state: IStore) => !!state.filters.labels.length

// forms
/* -------------------- Simple (non-memoized) -------------------- */
export const selectFormValuesChangedByType = (type: string) => (state: IStore) => state.forms[type]?.valuesChanged

export const selectFormValuesInitialByType = (type: string) => (state: IStore) => state.forms[type]?.valuesInitial

export const selectFormValueChangedByKey = (type: string, key: string) => (state: IStore) =>
  state.forms[type]?.valuesChanged?.[key]

export const selectFormValueInitialByKey = (type: string, key: string | string[]) => (state: IStore) => {
  const initial = state.forms[type]?.valuesInitial
  return key && initial ? pick(key as string, initial) : undefined
}

export const selectIsEveryFieldHidden =
  (type: string, fields: IFieldsProps[], optionsData?: Record<string, unknown>) => (state: IStore) => {
    const initial = state.forms[type]?.valuesInitial || {}
    const changed = state.forms[type]?.valuesChanged || {}
    return fields.every((field) => field.getIsHidden && field.getIsHidden(initial, optionsData, changed))
  }

/* -------------------- Reselect (memoized) -------------------- */
/** Input selectors that accept params alongside state */
const _selectFormValueChanged = (state: IStore, type: string, key: string) => state.forms[type]?.valuesChanged?.[key]

const _selectFormValueInitial = (state: IStore, type: string, key: string | string[]) => {
  const initial = state.forms[type]?.valuesInitial
  return key && initial ? pick(key as string, initial) : undefined
}

export const selectFormValueByKey = createSelector(
  [_selectFormValueChanged, _selectFormValueInitial],
  (valueChanged, initialValue) => (typeof valueChanged === 'undefined' ? initialValue : valueChanged)
)

// Usage:
// const v = useSelector((s) => selectFormValueByKey(s, type, key));

/** Whole values = initial merged with changed (arrays replaced, not concatenated) */
const _selectFormValuesChanged = (state: IStore, type: string) => state.forms[type]?.valuesChanged

const _selectFormValuesInitial = (state: IStore, type: string) => state.forms[type]?.valuesInitial

export const selectFormValuesWhole = createSelector(
  [_selectFormValuesChanged, _selectFormValuesInitial],
  (valuesChanged = {}, initialValues = {}) => {
    if (!valuesChanged || isObjectEmpty(valuesChanged)) return initialValues

    // dot.object to expand dotted keys if you store them that way
    const changedExpanded = object(valuesChanged)
    return merge(initialValues, changedExpanded, {
      arrayMerge: (_dest, source) => source, // replace arrays
    })
  }
)
