import { put, takeLatest } from 'redux-saga/effects'
import { getNotifications, getNotificationsError, getNotificationsSuccess } from '../reducers/common'
import { TakeableChannel } from 'redux-saga'
import { buildRequest, fetchData } from '../../api'
import { IResponse } from './auth'

function* doGetNotifications({ payload }: {
  payload: {
    message: string,
    organization_id: number,
    user_id: number
  }
}) {
  const { message, organization_id, user_id } = payload
  try {
    const request: Request = yield buildRequest({
      apiMethod: 'POST',
      type: 'test',
      requestBody: { message, organization_id, user_id }
    })

    const notifications: IResponse = yield fetchData(request)
    
    // TODO: add logic
    console.log(notifications)

    yield put(getNotificationsSuccess())
  } catch (error) {
    console.error(error)
    yield put(getNotificationsError())
  }
}

export default function* commonSaga() {
  yield takeLatest(getNotifications.type as unknown as  Takeable<PERSON>hannel<unknown>, doGetNotifications)
}
