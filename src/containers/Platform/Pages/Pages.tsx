import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import PagesTableCard from './PagesTableCard'

const FAQs = () => {
  return (
    <GridLayout
      rightPanelComponent={
        <>
          <Outlet context={{}} />
        </>
      }
    >
      <TableBlockInfiniteScroll
        TableCardContent={PagesTableCard as unknown as React.JSX.Element}
        tableCardHeight={90}
        iconName='fileCheck'
      />
    </GridLayout>
  )
}

export default FAQs
