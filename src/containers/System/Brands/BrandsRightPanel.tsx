import { useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { object } from 'dot-object'
import { isObject } from '@aidsupply/components'
import { slugify } from 'transliteration'
import merge from 'deepmerge'
import { useMappedState } from '../../../hooks'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { brandsRightPanelConfig } from './brandsRightPanelConfig'

const BrandsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const brandInitial = useMappedState(selectSidebarInitialData)

  const isReadOnly = false

  const onFormSubmit = (formValuesChanged: Record<string, unknown>) => {
    const requestBody: Record<string, unknown> = { ...object(formValuesChanged) }

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key] as Record<string, unknown>) && (requestBody[key] as Record<string, unknown>).id) {
        requestBody[key] = (requestBody[key] as Record<string, unknown>).id
      }
    })

    if (requestBody.description)
      requestBody.description = merge(brandInitial?.description as Record<string, unknown>, requestBody.description)

    if (formValuesChanged.name_en || (formValuesChanged.name && !brandInitial?.name_en)) {
      requestBody.slug = slugify(formValuesChanged.name_en as string || formValuesChanged.name as string)
    }

    const dataToSend = {
      id: brandInitial?.id,
      requestBody,
      type: 'brands',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} onSubmit={onFormSubmit} formData={brandsRightPanelConfig()}/>
}

export default BrandsRightPanel
