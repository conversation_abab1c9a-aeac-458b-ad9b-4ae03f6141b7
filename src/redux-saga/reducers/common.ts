import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { buildPopupAlerts } from '../../utils/alerts'

// TODO: rid of hardcode
const notificationData = [{
  created_at: '2025-03-25T09:05:19.482661',
  details: {
    id: 11,
    name: 'bananas',
    message: 'test',
    number: 1
  },
  id: 133,
  is_system: true,
  notification_type: "document_updated",
  organization_id: null,
  status: 'new',
  operation: 'test',
  entity: {
    created_at: 1
  },
  collection: 'test',
  isNew: true
}]

// TODO: review and fix INotification after fetching real data
export interface INotification {
  created_at: string
  details: {
    id: number
    name: string
    message: string
    number: number
  }
  id: number
  is_system: boolean
  notification_type: string // TODO: added types
  organization_id: number | null
  status: string // TODO: added types
  operation: string,
  entity: {
    created_at: number
  },
  collection: string,
  isNew: boolean
}

export interface IPopupAlert {
  id?: number
  type?: 'default' | 'new' | 'success' | 'warning' | 'error';
  contentKey?: string
  contentParams?: Record<string, string>
  content?: string
  iconName?: string
  iconProps?: Record<string, unknown>
  timeout?: number
  stacked?: boolean
  withCloseButton?: boolean
  className?: string
  onCloseCustom?: () => void
}

export interface ICommon {
  popupAlerts: IPopupAlert[]
  notifications: INotification[]
  isNotificationsOpened: boolean
  isNavMenuExtended: boolean
  isRightPanelExtended: boolean
  isRightPanelExtendedWider: boolean
  isLeftPanelExtended: boolean
}

export const commonSlice = createSlice({
  name: 'common',
  initialState: {
    popupAlerts: [],
    notifications: notificationData,
    isNotificationsOpened: false,
    isNavMenuExtended: false,
    isRightPanelExtended: false,
    isRightPanelExtendedWider: false,
    isLeftPanelExtended: false
  },
  reducers: {
    popupAlertShow(state: ICommon, action: PayloadAction<IPopupAlert>) {
      state.popupAlerts = buildPopupAlerts(state.popupAlerts, action.payload as IPopupAlert) as IPopupAlert[]
    },
    popupAlertHide(state: ICommon, action: PayloadAction<{id: number}>) {
      state.popupAlerts = state.popupAlerts.filter((alert) => alert.id !== action.payload.id)
    },
    popupAlertHideAll(state: ICommon) {
      state.popupAlerts = []
    },
    getNotifications(_state: ICommon, _action: PayloadAction<{
      message: string,
      organization_id?: number,
      user_id: number
    }>) {},
    // TODO: add logic
    getNotificationsSuccess(_state: ICommon) {},
    // TODO: add logic
    getNotificationsError(_state: ICommon) {},
    toggleNotifications(state: ICommon, action: PayloadAction<boolean | undefined>) {
      state.isNotificationsOpened = action.payload === undefined ? !state.isNotificationsOpened : action.payload
    },
    clearNotifications(state: ICommon) { 
      state.notifications = []
    },
    toggleNavMenuExtended(state: ICommon, action: PayloadAction<boolean>) {
      state.isNavMenuExtended = action.payload
    },
    toggleRightPanelExtended(state: ICommon, action: PayloadAction<boolean>) {
      state.isRightPanelExtended = action.payload
    },
    toggleRightPanelExtendedWider(state: ICommon, action: PayloadAction<boolean>) {
      state.isRightPanelExtendedWider = action.payload
    },
    toggleLeftPanelExtended(state: ICommon, action: PayloadAction<boolean>) {
      state.isLeftPanelExtended = action.payload
    }
  }
})

export const {
  popupAlertShow,
  popupAlertHide,
  popupAlertHideAll,
  getNotifications,
  getNotificationsSuccess,
  getNotificationsError,
  toggleNotifications,
  clearNotifications,
  toggleNavMenuExtended,
  toggleRightPanelExtended,
  toggleLeftPanelExtended,
  toggleRightPanelExtendedWider
} = commonSlice.actions
export default commonSlice.reducer
