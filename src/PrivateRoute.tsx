import React, { useEffect } from 'react'
import { Navigate, useLocation, useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { initializeApp } from './redux-saga/reducers/user'
import { selectUserState } from './redux-saga/selectors'
import LoadingNavigation from './components/LoadingNavigation'
import { useMappedState } from './hooks'

const PrivateRoute = ({ children }: {
	children: React.ReactNode;
}) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const location = useLocation()

  const user = useMappedState(selectUserState)

  useEffect(() => {
    if (!user.isAppInitialized) {
      dispatch(initializeApp({ navigate, location }))
    }
  }, [user.isAppInitialized])

  if (!user.isAppInitialized || !user.email) {
    return <LoadingNavigation />
  }

  return user.isAuthenticated ? children : <Navigate replace to="/signin" />
}

export default PrivateRoute
