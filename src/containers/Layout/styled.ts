import styled from 'styled-components'
import { Drawer } from '@aidsupply/components'
import { HEADER_HEIGHT, NAV_MENU_WIDTH, TOP_BAR_BORDER_BOTTOM, TOP_BAR_HEIGHT, TOP_BAR_HEIGHT_XL } from '../../constants'

export const StyledMainWrapper = styled.div`
  display: flex;
  overflow: hidden;
  height: calc(100vh - ${HEADER_HEIGHT}px - ${TOP_BAR_BORDER_BOTTOM}px);
  position: relative;
  width: 100%;
  padding: 0 0 0 ${NAV_MENU_WIDTH.desktop.condensed};

  &.extended {
    &::after {
      z-index: 22;
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.25);

      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
        z-index: 9;
      }
    }
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
    display: flex;
    flex-direction: column;
    padding: 0;
    height: calc(100vh - ${TOP_BAR_HEIGHT_XL}px - ${TOP_BAR_BORDER_BOTTOM}px);
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.md}px) {
    height: calc(100vh - ${TOP_BAR_HEIGHT_XL}px - ${TOP_BAR_BORDER_BOTTOM}px);
  }
`

export const StyledContainer = styled.div`{
  position: relative;
  width: 100%;
  height: 100%;

  &.extended {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -${NAV_MENU_WIDTH.desktop.condensed};
      width: calc(100% + ${NAV_MENU_WIDTH.desktop.condensed});
      height: 100%;
      background-color: rgba(0, 0, 0, 0.25);
      z-index: 11;
    }
  }
}`

export const StyledGrid = styled.div`{
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-grow: 1;
  height: 100%;

  &.scrollable {
    overflow: auto;
  }
}`

export const StyledMain = styled.div<{
  rightPanelWidth?: string
}>`
  position: relative;
  flex-grow: 1;
  padding: 0 20px;

  .iconLeft {
    position: absolute;
    top: 22px;
    right: 0;
    z-index: 1;
    cursor: pointer;
    background-color: ${({ theme }) => theme.color.primary.main};
    opacity: 0;
    transform: translateX(0);
    transition: opacity 1s ease, transform 0.3s ease;

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
      right: ${({ rightPanelWidth }) => rightPanelWidth};
    }

    &.open {
      opacity: 1;
      transform: translateX(0);
    }
    
    &.close {
      opacity: 0;
      transform: translateX(200px);
    }

    &:hover {
      opacity: 0.8;
    }
  }
`

export const StyledLeftDrawer = styled(Drawer)`
  border-right: 1px solid ${({ theme }) => theme.color.general.gray1};
  min-width: 200px;
  overflow-x: hidden;
  height: calc(100vh - ${TOP_BAR_HEIGHT}px - ${TOP_BAR_BORDER_BOTTOM}px);

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
    height: calc(100vh - ${TOP_BAR_HEIGHT_XL}px - ${TOP_BAR_BORDER_BOTTOM}px);
  }

  .counterBadge {
    span {
      background-color: ${({ theme: { color } }) => color.general.light};
      color: ${({ theme: { color } }) => color.general.dark};
      border: 1px solid ${({ theme: { color } }) => color.general.gray2};
      border-radius: 4px;
      line-height: 14px;
    }
  }

  .rightPanel {
    padding: 20px;
  }

  .headerContent {
    display: flex;
    align-items: center;
  }

  .titleRow {
    padding: 15px 20px 10px 20px;

    .typography {
      line-height: 32px;
    }
  }
`
