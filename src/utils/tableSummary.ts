import { TFunction } from 'i18next'
import { IBrandItem } from '../commonInterfaces'

const getQuantity = (
  tableData: IBrandItem[],
  props: { state: string; currency: string },
  key: string,
  width: number
) => ({
  value: tableData
     // @ts-ignore
    ?.reduce((acc, curr) => acc + (+curr[key as keyof IBrandItem] || 0), 0)
    .toFixed(2),
  width: width,
  padding: props?.state === "drafted" ? "0px 7px 0 15px" : undefined,
})

const getTotal = (
  tableData: IBrandItem[],
  props: { state: string; currency: string; withLabel?: boolean }
) => {
  const valueSum = tableData?.reduce((acc, curr) => {
    acc += (+curr.quantity || 0) * (+curr.price || 0)
    return acc
  }, 0)
  return {
    value: `${props.currency || ''}${valueSum.toFixed(2)}`,
    width: 75,
    padding: props?.state === 'drafted' ? '0px 7px 0 15px' : undefined,
  }
}

export const getTableSummary = (type: string, t: TFunction, tableData: IBrandItem[], props: { state: string, currency: string }) => {
  const CONFIG = {
    'orders.order_items': {
      title: t('total'),
      // TODO: move width to constants or set dynamic value in styles after fixed GOOD-194
      data: [getQuantity(tableData, props, 'quantity', 100), getTotal(tableData, props)],
    },
    'shipments.shipment_items': {
      title: t('total'),
      // TODO: move width to constants or set dynamic value in styles after fixed GOOD-194
      data: [getQuantity(tableData, props, 'quantity', 50)],
    },
    'inquiries.inquiry_items': {
      title: t('total'),
      // TODO: move width to constants or set dynamic value in styles after fixed GOOD-194
      data: [getQuantity(tableData, props, 'qty_new', 50)],
    },
    dashboard: {
      title: t('summary'),
      data: [getQuantity(tableData, props, 'quantity', 100)],
    },
  }

  return CONFIG[type as keyof typeof CONFIG]
}
