import { useContext } from 'react'
import { useTranslation } from 'react-i18next'

import { Icon, Typography, Badge, ScreenContext } from '@aidsupply/components'

import { StyledNotificationsHeader, StyledNotificationsMenu } from './styled'
import { useMappedState } from '../../hooks'
import { selectNotifications } from '../../redux-saga/selectors'
import NotificationItem from './NotificationItem'

const NotificationsNavbar = () => {
  const screenWidth = useContext(ScreenContext)
  const { width, md } = screenWidth || {}
  const isMobile = width && width < md
  const {
    i18n: { language },
    t,
  } = useTranslation('notifications')

  const notifications = useMappedState(selectNotifications)
  const badgeContent = notifications.length + ' new'

  const getListHeader = () => (
    <StyledNotificationsHeader>
      <Typography text={t('Notifications')} type="body1" />
    </StyledNotificationsHeader>
  )

  const getItems = () => {
    return notifications.map((item) => {
      return {
        ...item,
        name: <NotificationItem item={item} language={language as 'en' | 'uk'} />,
      }
    })
  }

  return (
    <StyledNotificationsMenu
      className="navbarNotifications"
      inverted
      openToRight={!isMobile}
      ListHeader={getListHeader()}
      menuLinkType="main"
      isMenuSelectable={false}
      link={
        <div className="navbarNotificationsLabel">
          <Icon name={notifications.length ? 'notificationOn' : 'notificationOff'} />
          {!isMobile && notifications.length ? (
            <Badge className="navbarNotificationsBadge" badgeContent={badgeContent} />
          ) : (
            ''
          )}
        </div>
      }
      menuAlign={isMobile ? 'right' : 'left'}
      items={getItems()}
    />
  )
}

export default NotificationsNavbar
