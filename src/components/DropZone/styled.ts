import styled, { DefaultTheme } from 'styled-components'

interface IDropZoneProps {
  theme: DefaultTheme;
  isDragAccept: boolean;
  isDragReject: boolean;
  isDragActive: boolean;
}

const getBorder = (props: IDropZoneProps) => {
  const { color } = props.theme
  if (props.isDragAccept) {
    return `1px solid ${color.status.success}`
  }
  if (props.isDragReject) {
    return `1px solid ${color.status.error}`
  }
  if (props.isDragActive) {
    return `1px dashed ${color.status.new}`
  }
  return `1px dashed ${color.general.gray2}`
}

export const StyledDropZone = styled.div<IDropZoneProps>`
  cursor: pointer;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 16px;
  border: ${(props) => getBorder(props)};
  outline: none;
  transition: border-color 0.24s ease-in-out;
  margin-top: 10px;
  width: 100%;
`
export const StyledDropButton = styled.div`
  position: absolute;
  width: 32px;
  height: 26px;
  top: 0;
  left: 0;
`
