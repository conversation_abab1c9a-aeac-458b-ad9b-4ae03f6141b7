import { call, put, select, take, takeLatest } from 'redux-saga/effects'
import { TakeableChannel } from 'redux-saga'
import { NavigateFunction } from 'react-router'
import { ATTRIBUTE_TYPES, CONTRACT_TYPES, ENUMERATION_ADDONS, MAGNITUDES, MONTHS, ORGANI<PERSON>ATION_TYPES, PAGE_TYPES, REPORT_TYPES, TAXES_TYPE, VISIBILITY_TYPES, WAREHOUSE_TYPES, YEARS } from '../../data'
import { CAMPAIGNS_STATUSES_OPTIONS, FRONTEND_TABLE_ACTIONS, INQUIRY_STATUSES_OPTIONS, INVENTORY_STATUSES_OPTIONS, INVOICE_STATUSES_OPTIONS, ORDER_STATUSES_OPTIONS, ORG_OR_USER_STATUSES_OPTIONS, SHIPMENT_STATUSES_OPTIONS, STATES_OPTIONS, STATUSES_OPTIONS } from '../../components/config/table'
import { OR<PERSON>NIZATION_ROLES } from '../../components/config'
import { ISubmenu, USER_ROLES } from '../../components/NavMenu/config'
import { SYSTEM_DATA_KEYS } from '../../apiConstants'
import { dataFetchWithFacets, dataFetchWithFacetsError, dataFetchWithFacetsSuccess, dataSetSystemData, IDataWithFacets, setDataType, updateSystemData } from '../reducers/data'
import { buildRequest, fetchData } from '../../api'
import { mapStateToProps } from '../selectors'
import { mapParamsToUrl, mapUrlToParams } from '../../utils/filter'
import { filtersAdd, filtersClearAll, filtersRemove, filtersSetFacetGroupJustSelected, filtersSetOnFirstLoad } from '../reducers/filters'
import { ICurrentUser } from '../reducers/user'
import { fetchFacets, readSystemData } from './dataFromCommonPackage'
import { TLanguages } from '../../locales'

function* readApiDataWithFacets(payload: {data: IDataWithFacets}) {
  const {
    data: {
      type,
      query,
      facetsToInclude,
      locationSearch,
      filtersFromUrl,
      pageLimit,
      pageOffset,
      lng,
      isLoadDataByInfiniteScroll,
    },
  } = payload
  const {
    user: {
      details: { role },
    }
  } = yield select(mapStateToProps)

  let queryUpdated: {key: string, value: string | number}[] = query?.length ? query : []

  // pagination
  if (pageLimit) {
    queryUpdated.push({ key: 'limit', value: pageLimit })
  }
  if (pageOffset) {
    queryUpdated.push({ key: 'skip', value: pageOffset })
  }

  const filterItems = (items: any) => {
    if (type === 'shipments' && role === 'logistic') {
      return items.filter((item: any) => !item.logist_oid)
    } else if (type === 'inquiries' && role === 'donor') {
      return items.filter((item: any) => item.state === 'posted')
    }

    return items
  }

  let urlParam

  if (type === 'stock-items') {
    // TODO: add implementation
  }

  try {
    let commonQuery

    const {
      data: { isSystemDataLoaded },
    } = yield select(mapStateToProps)

    if (!isSystemDataLoaded && type !== 'dashboard') {
      yield take('data/dataFetchSystemDataSuccess') // Waiting till all system data is loaded
    }

    // First load
    if (filtersFromUrl || locationSearch) {
      const {
        data: { system },
      } = yield select(mapStateToProps)

      const { filters, apiQuery } = mapUrlToParams(
        (locationSearch as string)?.charAt(0) === '?' ? (locationSearch as string)?.slice(1) : locationSearch,
        type,
        system,
        lng
      )

      yield put(filtersSetOnFirstLoad({active: filters}))

      // Join queryParams from arguments and new params from url => common apiQuery
      if (apiQuery?.length) {
        commonQuery = queryUpdated ? queryUpdated.concat(apiQuery) : apiQuery
      }
    }

    if (!commonQuery) {
      commonQuery = queryUpdated || []
    }

    if (type) {
      const requestItems: Request = yield buildRequest({
        operation: 'read',
        type,
        queryParams: commonQuery,
        apiUrlParam: urlParam,
      })
    
      // TODO: extend the type for items via | or create a separate interface when working with other types
      let result: {total_items: number, items: ICurrentUser[]} = yield fetchData(requestItems)

      const { total_items } = result
      const items = type === 'dashboard' ? result : result.items

      if (items) {
        // TODO: extend the type for filteredItems via | or create a separate interface when working with other types
        const filteredItems: ICurrentUser[] = filterItems
          ? yield call(filterItems, items)
          : (type === 'dashboard' && result) || items

        yield put(dataFetchWithFacetsSuccess({data: { items: filteredItems, total_items, type, isLoadDataByInfiniteScroll }}))
      } else {
        yield put(dataFetchWithFacetsError({error: 'noItems', type}))
      }

      yield fetchFacets({
        type,
        queryParams: commonQuery,
        facetsToInclude,
        apiUrlParam: urlParam,
      })
    }
  } catch (error) {
    console.log(error)
    yield put(dataFetchWithFacetsError({error, type}))
  }
}

function* doReadTableData(
  { payload }: { payload: IDataWithFacets }
) {
  const {type, query, locationSearch, apiUrlParam, sortString, pageLimit, pageOffset, lng, isLoadDataByInfiniteScroll } = payload

  try {
    let queryUpdated = query?.length ? query : []

    // TODO: check do we need it when setting up type 'categories' or 'inquiry_items'
    // yield includePlatformIndustriesInQuery(type, queryUpdated)

    yield readApiDataWithFacets({
      data: {
        type,
        query: queryUpdated,
        locationSearch: !FRONTEND_TABLE_ACTIONS[type]?.search ? locationSearch : '',
        apiUrlParam,
        sortString,
        pageLimit,
        pageOffset,
        lng,
        isLoadDataByInfiniteScroll
      },
    })
  } catch (error) {
    yield put(dataFetchWithFacetsError({ error, type }))
  }
}

function* doUpdateSystemData({ payload }: {
    payload: {
      type: string
      status: 'update' | 'create'
      itemToSet: Record<string, unknown>
      updatedData: Record<string, unknown>
    }
  }
) {
  const { type, status, itemToSet, updatedData } = payload
  const { data } = yield select(mapStateToProps)
  if (!data[type] && !data.system[type]) {
    return
  }

  let allItemsToSetInSystem = updatedData

  // sidebar update case
  if (!updatedData && itemToSet) {
    const allCurrentSystemItems = data.system[type] && Object.values(data.system[type])
    // check status names when backend is ready
    if (status === 'create' || status === STATUSES_OPTIONS.new.id) {
      allItemsToSetInSystem = allCurrentSystemItems && [itemToSet, ...allCurrentSystemItems]
    } else if (status === 'update') {
      allItemsToSetInSystem =
        allCurrentSystemItems &&
        // @ts-ignore
        allCurrentSystemItems.map((item) => (itemToSet.id === item.id ? itemToSet : item))
    }
  }

  // bulk update and sidebar update cases (TODO: check stock-items also)
  if (allItemsToSetInSystem) {
    if (data.system[type]) {
      yield put(dataSetSystemData({ key: type, data: allItemsToSetInSystem }))
    }
  }

  // case when edit sidebar is opened (if bulk update)
  // if (sideBar.initial?.id) {
  //   if (updatedData?.length) {
  //     // @ts-ignore
  //     const item = updatedData.find((dataItem) => dataItem.id === sidebar.initial.id)
  //     yield put(sidebarItemSet(item))
  //   }
  // }
}

function* doSetDataType({ payload }: {
  payload: {
    data: ISubmenu
  }
}) {

  const type: string = payload.data?.key

  const constants: Record<string, unknown> = {
    attributeTypes: ATTRIBUTE_TYPES,
    contractTypes: CONTRACT_TYPES,
    campaignStatuses: Object.values(CAMPAIGNS_STATUSES_OPTIONS),
    organizationTypes: ORGANIZATION_TYPES,
    magnitudes: MAGNITUDES,
    pageTypes: PAGE_TYPES,
    states: Object.values(STATES_OPTIONS),
    statuses: Object.values(STATUSES_OPTIONS),
    organizationRoles: Object.values(ORGANIZATION_ROLES),
    orgOrUsersStatuses: Object.values(ORG_OR_USER_STATUSES_OPTIONS),
    inquiryStatuses: Object.values(INQUIRY_STATUSES_OPTIONS),
    invoiceStatuses: Object.values(INVOICE_STATUSES_OPTIONS),
    shipmentStatuses: Object.values(SHIPMENT_STATUSES_OPTIONS),
    inventoryStatuses: Object.values(INVENTORY_STATUSES_OPTIONS),
    warehouseTypes: WAREHOUSE_TYPES,
    userRoles: Object.values(USER_ROLES),
    visibilityTypes: VISIBILITY_TYPES,
    reportTypes: REPORT_TYPES,
    months: MONTHS,
    years: YEARS,
    enumerationAddons: ENUMERATION_ADDONS,
    orderStatuses: Object.values(ORDER_STATUSES_OPTIONS),
    taxTypes: Object.values(TAXES_TYPE),
  }

  yield readSystemData(SYSTEM_DATA_KEYS[type], constants)
}

function* doUpdateFilters({payload}: {
  payload: {
  key: string
  value: string
  lng: TLanguages
  locationSearch: string
  navigate: NavigateFunction
  }
}) {
  const {key, value, lng, locationSearch, navigate} = payload;

  const { data, filters } = yield select(mapStateToProps)
  const type = data?.type?.key

  const { urlQuery } = mapParamsToUrl(type, locationSearch, lng, filters.active, filters.facets)

  // TODO: add when setting up type 'categories' or 'inquiry_items
  // const queryUpdated = [...apiQuery]

  // if (actionType === FILTERS_FILTER_REMOVE && !Object.values(filters.active)?.flat()?.length) {
  //   yield includePlatformIndustriesInQuery(type, queryUpdated)
  // }

  navigate({ search: urlQuery })
  yield put(filtersSetFacetGroupJustSelected({ key, value }))
}

export default function* dataSaga() {
  yield takeLatest(dataFetchWithFacets.type as unknown as TakeableChannel<unknown>,doReadTableData),
  yield takeLatest(setDataType.type as unknown as  TakeableChannel<unknown>, doSetDataType)
  yield takeLatest(updateSystemData.type as unknown as TakeableChannel<unknown>,doUpdateSystemData)
  yield takeLatest(filtersAdd.type as unknown as TakeableChannel<unknown>, doUpdateFilters)
  yield takeLatest(filtersRemove.type as unknown as TakeableChannel<unknown>, doUpdateFilters)
  yield takeLatest(filtersClearAll.type as unknown as TakeableChannel<unknown>, doUpdateFilters)
}
