import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import { useTheme } from 'styled-components'

import { Comment, Typography, Icon, Button } from '@aidsupply/components'
import { Link as RouterLink, useParams } from 'react-router'
import { selectSidebarCommentsData } from '../../redux-saga/selectors'
import { selectSidebarState } from '../../redux-saga/selectors'
import { useMappedState } from '../../hooks'
import { selectSidebarInitialData } from '../../redux-saga/selectors'
import { formatDateToUserTimezone } from '../../utils/dates'
import { StyledLink } from '../Table/styled'
import { StyledComments, StyledInput, StyledContainer } from './styled'
import { sidebarPostComment } from '../../redux-saga/reducers/sideBar'
import { IComment } from '../../commonInterfaces'
import { UpdateInput } from '../Form/interfaces'
import EmptyScreen from '../EmptyScreen/EmptyScreen'

interface ICommentsProps {
  containerStyles?: Record<string, string | Record<string, string>>
  inputStyles?: Record<string, string | number>
  showAll?: boolean
  formValuesChanged?: Record<string, unknown>
  updateInput?: UpdateInput
  formValues?: Record<string, unknown>
  isReadOnly?: boolean
}
const Comments = ({
  containerStyles,
  inputStyles,
  showAll = false,
  formValuesChanged,
  formValues,
  updateInput,
  isReadOnly,
  ...rest
}: ICommentsProps) => {
  const dispatch = useDispatch()
  const theme = useTheme()
  const { t } = useTranslation('forms')
  const commentsData = useMappedState(selectSidebarCommentsData) as IComment[]
  const sidebarInitialData = useMappedState(selectSidebarInitialData)
  const sidebar = useMappedState(selectSidebarState)

  const sidebarUpsertInProgress = sidebar.upsertInProgress

  const [inputValue, setInputValue] = useState('')
  const { rightPanelId: editedItemId } = useParams()

  useEffect(() => {
    if (!formValuesChanged?.comment) {
      setInputValue('')
    }
  }, [formValuesChanged?.comment])

  useEffect(() => {
    if (sidebarUpsertInProgress) {
      setInputValue('')
    }
  }, [sidebarUpsertInProgress])

  const submitComment = () => {
    const commentValue = inputValue
    if (commentValue) {
      dispatch(
        sidebarPostComment({
          entity_id: sidebarInitialData?.id as number,
          content: commentValue,
        })
      )
      setInputValue('')
      if (updateInput) {
        updateInput({ target: { name: 'comment', value: null } })
      }
    }
  }

  const dateFormat = (date: string) => {
    return <>
      {formatDateToUserTimezone(date, 'DD.MM.YYYY')}
      <span className="dateDot">•</span>
      {formatDateToUserTimezone(date, 'H:mm')}
    </>
  }

  if (commentsData?.length === 0 && isReadOnly) {
    return <EmptyScreen text={t('noComments')} iconName='noMail' width='150px' height='150px' />
  }

  const renderComment = (comment: IComment) => {
    return (
      <Comment
        {...comment}
        comment={comment.content}
        dateCreated={comment.created_at}
        dateFormat={(date: string) => dateFormat(date)}
        imageData={comment.user_photo}
        imageProps={{
          width: 20,
          height: 20,
          objectFit: 'cover',
        }}
        key={comment.id}
        name={comment.user_full_name}
      />
    )
  }

  const sortedComments = [...(commentsData || [])].sort(
    (a, b) => b.created_at.localeCompare(a.created_at)
  )
  const newestComment = sortedComments.length > 0 ? sortedComments[0] : null
  const showMoreValue = sortedComments.length - 1

  return (
    <StyledComments containerStyles={containerStyles || {}} {...rest}>
      {showAll ? (
        sortedComments?.map((comment) => renderComment(comment))
      ) : (
        <>
          {newestComment && renderComment(newestComment)}
          {sortedComments.length > 1 && (
            <StyledLink Link={RouterLink} to="./comments" className='showMoreLink'>
              <Typography
                text={t('forms:showMoreWithValue', {value: showMoreValue})}
                color={theme.color.primary.main}
                fontWeight={500}
                lineHeight="16px"

              />
            </StyledLink>
          )}
        </>
      )}
      {!isReadOnly && <StyledContainer inputStyles={inputStyles || {}}>
        <StyledInput
          multiline
          withBorder
          variant="secondary"
          value={inputValue}
          onChange={(value: { currentTarget: { value: string | null } }) => {
            setInputValue(value.currentTarget.value || '')
            if (updateInput) {
              updateInput({ target: { name: 'comment', value: value.currentTarget.value || '' } })
            }
          }}
          onEnter={submitComment}
          maxLength="1000"
          />
          <Button
            className="button"
            variant="primary"
            onClick={submitComment}
            disabled={!inputValue || editedItemId === 'clone' || editedItemId === 'new'}
          >
            <Icon name="send" fill={theme.color.general.light} width={16} height={16} />
          </Button>
        </StyledContainer>
      }
    </StyledComments>
  )
}

export default Comments
