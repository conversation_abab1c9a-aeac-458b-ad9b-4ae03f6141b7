import { useTranslation } from 'react-i18next'
import { Typography } from '@aidsupply/components'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { StateType } from '../../../components/Form/interfaces'
import { ITranslations } from '../../../locales'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StyledStateWrapper } from './styled'

interface ICategoriesTableCard {
  id: number
  name: string
  translations: ITranslations
  state: StateType
}

const CategoriesTableCard = ({ data, className }: {data: ICategoriesTableCard, className?: string}) => {
  const { t } = useTranslation('table')
  const { name, id, translations, state } = data
  
  return (
    <TableCardStyled key={id} direction='column' gap={1} className={className}>
      <>
      <StyledStateWrapper>
        <Typography as={ReactTexty} type="h4">
          {name}
        </Typography>
        <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />
      </StyledStateWrapper>
      <Typography as={ReactTexty} type="button2" fontWeight="400">
        {translations}
      </Typography>
      </>
    </TableCardStyled>
  )
}

export default CategoriesTableCard
