import { useEffect } from 'react'
import { useNavigate } from 'react-router'
import { object } from 'dot-object'
import { useDispatch } from 'react-redux'
import { useMappedState } from '../../../hooks'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sidebarItemSet, sideBarUpsertUser } from '../../../redux-saga/reducers/sideBar'
import { DEFAULT_VALUES_DATA } from '../../../data/defaultValues'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import { usersRightPanelConfig } from './usersRightPanelConfig'

export interface IUsersRightPanel {
  email: string
  full_name: string
  organization_id: Record<string,unknown>
  role: {
    entities: string[],
    id: string,
    label: Record<string,unknown>
    level: number
  }
  id?: number
  state?: string
}

const UsersRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  useEffect(() => {
    dispatch(sidebarItemSet(DEFAULT_VALUES_DATA.users as Record<string, string>))
  }, [])

  const userInitial = useMappedState(selectSidebarInitialData) as IUsersRightPanel | null

  const isReadOnly = false

  const onFormSubmit = (values: Record<string, unknown>) => {
    
    const requestBody: Record<string, unknown> = { ...object(values) }

    Object.keys(requestBody).forEach((key) => {
      if (requestBody[key] && typeof requestBody[key] === 'object' && 'id' in requestBody[key]) {
        requestBody[key] = requestBody[key].id
      }
    })

    if ((values as unknown as IUsersRightPanel).role?.id === userInitial?.role?.id) {
      delete requestBody.role
    }

    if (!userInitial?.id && !requestBody?.state) {
      requestBody.state = 'posted'
    }

    const dataToSend = {
      id: userInitial?.id,
      requestBody,
      type: 'users',
    }

    dispatch(sideBarUpsertUser({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} formData={usersRightPanelConfig}
  onSubmit={onFormSubmit}/>
}

export default UsersRightPanel
