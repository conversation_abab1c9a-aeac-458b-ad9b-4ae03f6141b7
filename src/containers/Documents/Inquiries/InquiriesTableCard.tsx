import { useTranslation } from 'react-i18next'
import { useTheme } from 'styled-components'
import { Icon, Typography, Userpic } from '@aidsupply/components'
import { StyledContainerWrapper, StyledDocumentContainer } from '../../../components/RightPanel/styled'
import { StyledBorderedContainerWrapper, StyledDocumentDate } from '../../../components/RightPanel/styled'
import { IInquiry, IOrganization } from '../../../commonInterfaces'
import { TableCardStyled } from '../../../components/Table/styled'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { STATES_ICONS, STATUSES_ICONS } from '../../../components/config/table'
import { Status } from '../../../commonTypes'
import { StateType } from '../../../components/Form/interfaces'
import ReactTexty from '../../../lib/react-texty'
import { formatDateToUserTimezone } from '../../../utils/dates'
import InquiryNumberWithStatusBar from '../../../components/InquiryNumberWithStatusBar/InquiryNumberWithStatusBar'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections } from '../../../redux-saga/selectors'
import { StyledContentWrapper } from './styled'

const InquiriesTableCard = ({data, initialData, className}: {data: IInquiry, initialData: Record<string, unknown>, className?: string}) => {
  const { id, status, state, open_demands, closed_demands, in_progress_demands, recipient_organization_type } = data || {}
  const { created_at, recipient_id, number } = initialData

  const theme = useTheme()
  const { t } = useTranslation('table')

  const system = useMappedState(selectAllSystemCollections)
  const recipient = system?.organizations?.[recipient_id as number] as IOrganization

  const totalDemands = (open_demands || NaN) + (closed_demands || NaN) + (in_progress_demands || NaN)

  const getPercentageObject = (key: string, totalDemands: number) => {
    return {
      percentage: totalDemands === 0 ? 0 : ((data as Record<string, number>)[key] / totalDemands) * 100,
      tooltipTitle: t(key),
    }
  }

  const percentages = [
    getPercentageObject('open_demands', totalDemands),
    getPercentageObject('closed_demands', totalDemands),
    getPercentageObject('in_progress_demands', totalDemands),
    { percentage: 100, tooltipTitle: null },
  ]
  
  return (
    <TableCardStyled key={id} direction='column' gap={4} className={className}>
      <StyledContentWrapper>
        <StyledContentWrapper>
          {state === 'posted' ?
            <IconWithTooltip text={t(`statuses:${status}`)}  iconName={STATUSES_ICONS[status as Status]} arrowPosition='left'/>
            : <IconWithTooltip text={t(state as StateType)} iconName={STATES_ICONS[state as StateType]} arrowPosition='left'/>
          }
          <Typography as={ReactTexty} color={theme.color.general.dark} fontWeight={600}>
            {number}
          </Typography>
        </StyledContentWrapper>
        <StyledDocumentDate>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'DD.MM.YYYY')}
          </Typography>
          <span>•</span>
          <Typography as={ReactTexty} fontSize={'12px'}>
            {formatDateToUserTimezone(created_at as string, 'HH:mm')}
          </Typography>
        </StyledDocumentDate>
      </StyledContentWrapper>
      <InquiryNumberWithStatusBar percentages={percentages} />
      <StyledBorderedContainerWrapper margin="10px 0 0 0">
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="boxesIn" width={20} height={20} />
            <StyledContentWrapper>
              {
                recipient ?
                <Typography
                  text={recipient?.name}
                  style={{ marginRight: '10px' }}
                /> : ''
              }
              {
                recipient ?
                <Userpic
                  height="25px"
                  width="25px"
                  fullName={recipient?.name}
                  src={recipient?.logo_url}
                /> : ''
              }
            </StyledContentWrapper>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
        <StyledContainerWrapper>
          <StyledDocumentContainer>
            <Icon name="types" width={20} height={20} />
            <Typography as={ReactTexty} type="button1" fontWeight="400">
            {recipient_organization_type}
            </Typography>
          </StyledDocumentContainer>
        </StyledContainerWrapper>
      </StyledBorderedContainerWrapper>
    </TableCardStyled>
  )
}

export default InquiriesTableCard
