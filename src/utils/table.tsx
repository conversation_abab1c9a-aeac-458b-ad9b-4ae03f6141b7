// @ts-ignore
import ReactTexty from 'react-texty'
import { Typography } from '@aidsupply/components'
import { getOptionWithNameColumn } from '../components/config/columns'
import { TableModeType } from '../redux-saga/reducers/data'
import { NameOptionType } from '../commonTypes'
import { TLanguages } from '../locales'

export const sortObjectToUrlString = (sortObject: Record<string, 'asc' | 'desc'> = {}) => {
  return (
    sortObject &&
    Object.entries(sortObject)
      .filter(([, value]) => value)
      .map(([key, value]) => `${key}_${value}`)
      .join(',')
  )
}

export const headerRenderer =
  (t: (key: string) => string, tableMode?: TableModeType) =>
  ({ column }: { column: { title: string; key: string } }) => {
    return tableMode === 'list' ? (
      <ReactTexty>{t(column.title || column.key)}</ReactTexty>
    ) : (
      <Typography as={ReactTexty} type="body2">
        {t(column.key) || t(column.title)}
      </Typography>
    )
}

export const getNameByMode = ({
    t,
    dataKey,
    option,
    key,
    tableMode,
    lng
  }: {
    t: (key: string) => string,
    dataKey: string,
    option: NameOptionType | null,
    key?: string,
    tableMode?: TableModeType,
    lng?: TLanguages
  }) => {
  if (tableMode === 'cards') {
    return {
      key: dataKey,
      dataKey: dataKey,
      sortable: true,
      width: 0,
      flexGrow: 1,
      headerRenderer: headerRenderer(t, tableMode),
    }
  }
  if (tableMode === 'table') {
    const dataKeyToUse: string[] = ['orderNumber', 'invoiceNumber', 'shipmentNumber']
    return getOptionWithNameColumn(
      t,
      tableMode,
      key || dataKey,
      dataKeyToUse.includes(dataKey) ? 'number' : dataKey,
      option,
      lng
    )
  }
}

export const formatNumber = (num: number, minimumFractionDigits: number = 0, maximumFractionDigits: number = 3) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(num)
}
