import dayjs from 'dayjs'
import { RECIPIENT, STATE, SUPPLIER, WAREHOUSE } from '../../../components/config/forms'
import { IFormConfig, IOptionData, TranslateFn } from '../../../components/Form/interfaces'

export const shipmentsRightPanelConfig = (mode: 'create' | 'edit'): IFormConfig => {

  const supplies = {
    ...SUPPLIER,
    onSelectValueChange: (value: string | Record<string, unknown>, setValuesChanged?: (prev: Record<string, unknown>) => Record<string, unknown>) => {
      // @ts-ignore
      setValuesChanged?.((prev: Record<string, unknown>) => {
        return {
          ...prev,
          supplier_id: value,
          warehouse_id: '',
        } as unknown as Record<string, unknown>
      })
    },
  }
  
  return {
    validationRules: {
      warehouse_id: ['required'],
    },
    fields: {
      ...(mode === 'create' && {
      general: [
          {
            key: 'order_id',
            label: 'order',
            optionsKeys: ['orders'],
            noTranslation: true,
            labelKey: 'number',
            component: 'dropdown',
            getOptions: (systemObject: Record<string, unknown>) =>
              systemObject.orders &&
              Object.values(systemObject.orders).filter(
                (order: { state: string }) => order.state === 'posted'
              ),
            customGetOptionLabel: (option: Record<string, unknown> | IOptionData, t: TranslateFn) => {
              // @ts-ignore
              return `№ ${option.number || option.id} ${t('from')} ${
                // @ts-ignore
                option.created_at ? dayjs(option.created_at as string)?.format('DD/MM/YYYY') : ''
              }`
            },
            onSelectValueChange: async (
              value: Record<string, unknown> | string,
              setValuesChanged?: (prev: Record<string, unknown>) => void,
              _initialValues?: Record<string, unknown>,
              optionsData?: Record<string, unknown>
            ) => {
              const token = (optionsData?.user as { token: string })?.token
              const response = await fetch(`${process.env.REACT_APP_API_URL}/documents/orders/${(value as { id: number })?.id}`, {
                headers: { Authorization: `Bearer ${token}` },
              })
              const order = await response.json()

              // @ts-ignore
              setValuesChanged((prev: Record<string, unknown>) => ({
                ...prev,
                shipment_items: order?.order_items,
                recipient_id: (value as { recipient_id: number })?.recipient_id || '',
                supplier_id: (value as { supplier_id: number })?.supplier_id || '',
                warehouse_id: (value as { warehouse_id: number })?.warehouse_id || '',
              } as unknown as Record<string, unknown>))
            },
          },
          {
            ...supplies,
            getDisabled: (formValues) => (formValues?.order_id as { id: number })?.id || formValues?.order_id,
          },
          {
            ...WAREHOUSE,
            required: true,
            getDisabled: (formValues) => (formValues?.order_id as { id: number })?.id || formValues?.order_id,
          },
          RECIPIENT,
        ],
      }),
      ...(mode === 'edit' && {
        general: [
          { ...STATE, getIsHidden: (formValues) => !formValues?.id },
          {
            key: 'order_id',
            label: 'order',
            optionsKeys: ['orders'],
            noTranslation: true,
            labelKey: 'number',
            component: 'dropdown',
            getOptions: (systemObject: Record<string, unknown>) =>
              systemObject.orders &&
              Object.values(systemObject.orders).filter(
                (order: { state: string }) => order.state === 'posted'
              ),
            customGetOptionLabel: (option: Record<string, unknown> | IOptionData, t: TranslateFn) => {
              // @ts-ignore
              return `№ ${option.number || option.id} ${t('from')} ${
                // @ts-ignore
                option.created_at ? dayjs(option.created_at as string)?.format('DD/MM/YYYY') : ''
              }`
            },
            onSelectValueChange: async (
              value: Record<string, unknown> | string,
              setValuesChanged?: (prev: Record<string, unknown>) => void,
              _initialValues?: Record<string, unknown>,
              optionsData?: Record<string, unknown>
            ) => {
              const token = (optionsData?.user as { token: string })?.token
              const response = await fetch(`${process.env.REACT_APP_API_URL}/documents/orders/${(value as { id: number })?.id}`, {
                headers: { Authorization: `Bearer ${token}` },
              })
              const order = await response.json()

              // @ts-ignore
              setValuesChanged((prev: Record<string, unknown>) => ({
                ...prev,
                shipment_items: order?.order_items,
                recipient_id: (value as { recipient_id: number })?.recipient_id || '',
                supplier_id: (value as { supplier_id: number })?.supplier_id || '',
                warehouse_id: (value as { warehouse_id: number })?.warehouse_id || '',
              } as unknown as Record<string, unknown>))
            },
            getDisabled: (_formValues, _optionData, initialValues) => (initialValues?.order_id as { id: number })?.id || initialValues?.order_id
          },
          {
            ...supplies,
            getDisabled: (formValues) => (formValues?.order_id as { id: number })?.id || formValues?.order_id,
          },
          {
            ...WAREHOUSE,
            required: true,
            getDisabled: (formValues) => (formValues?.order_id as { id: number })?.id || formValues?.order_id,
          },
          RECIPIENT,
        ],
        shipment_items: [],
        activity: [],
        notes: [],
      }),
    }
  }
}


