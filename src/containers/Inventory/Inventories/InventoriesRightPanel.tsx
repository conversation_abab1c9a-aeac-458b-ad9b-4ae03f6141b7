import { useDispatch } from 'react-redux'
import { object, pick } from 'dot-object'
import { useNavigate, useParams } from 'react-router'
import { FlexRow, Input, isObject, Select } from '@aidsupply/components'
import { IBrandItem } from '../../../commonInterfaces'
import { IFieldsProps, IFormConfig, StateType, UpdateInput } from '../../../components/Form/interfaces'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sidebarItemSet, sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { inventoriesRightPanelConfig } from './inventoriesRightPanelConfig'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections, selectSidebarInitialData, selectUserDetails } from '../../../redux-saga/selectors'
import { ReactNode, useEffect, useState } from 'react'
import { TLanguages } from '../../../locales'
import { useTranslation } from 'react-i18next'
import { getEditableTableColumns } from '../../../components/config/columnsEditableTable'
import { EDITABLE_TABLE_NAVBAR_FORM_CONFIG } from '../../../components/config/editableTableNavBarForms'
import { fileParse, IDraftFile } from '../../../redux-saga/reducers/fileUpload'
import StatusSelect from '../../../components/RightPanelForms/StatusSelect'
import ActionsDropdown from '../../../components/RightPanelForms/ActionsDropdown'
import ActivityTabs from '../../../components/RightPanelForms/ActivityTabs'
import InfoPanel from '../../../components/InfoPanel'
import UploadFileButtons from '../../Documents/utils/UploadFileButtons'
import UploadFiles from '../../../components/UploadFiles'
import { FILE_TYPES } from '../../../constants'
import EditableTableFormPart from '../../../components/RightPanelForms/EditableTableFormPart'
import InfoBlocks from '../../Documents/utils/InfoBlocks'

export interface IInventories {
  id?: number
  is_being_processed?: boolean
  warehouse_id?: number
  state?: StateType
  inventory_items?: IBrandItem[]
  type?: string
  status?: string
  organization_id?: number
  notes?: string
}

const FormBlockWrapper = ({
  children,
  blockKey,
  id,
  isReadOnly,
  formValues,
  formFieldsProps,
  fields,
  getPanelHeader,
  optionsData,
  customBlockValues,
  updateSelect,
  updateInput,
  formValuesChanged
}: {
  children: ReactNode
  blockKey: string
  id: number
  isReadOnly: boolean
  formValues: IInventories
  formFieldsProps: Record<string, unknown>
  fields: Record<string, unknown>
  getPanelHeader: (blockKey: string) => string
  optionsData: Record<string, unknown>
  customBlockValues: Record<string, unknown>
  updateSelect: (blockKey: string) => void
  setTextsChosenLng: (lng: TLanguages) => void
  updateInput: UpdateInput
  formValuesChanged: IInventories
}) => {
  const dispatch = useDispatch()
  const {
    t,
    i18n: { language },
  } = useTranslation(['table', 'forms', 'general'])
  const params = useParams()
  const { rightPanelId } = params

  const system = useMappedState(selectAllSystemCollections)
  const user = useMappedState(selectUserDetails)

  const [uploadType, setUploadType] = useState<'file' | 'table'>('file')
  const [isUploadFileActive, setIsUploadFileActive] = useState(false)
  const [replaceOption, setReplaceOption] = useState({ id: 'add', label: t('forms:addToExistingItems') })
  
  const editableTableColumns = getEditableTableColumns('inventories.inventory_items', language as TLanguages, t)
  const editableTableConfig = EDITABLE_TABLE_NAVBAR_FORM_CONFIG['inventories.inventory_items'] as unknown as IFormConfig

  useEffect(() => {
    setReplaceOption({ id: 'add', label: t('forms:addToExistingItems') })
  }, [formValues.id])

  useEffect(() => {
    if (rightPanelId === 'clone') {
      setUploadType('table')
    } else {
      setUploadType('file')
      setIsUploadFileActive(false)
    }
  }, [rightPanelId])

  const additionalFormValues = editableTableConfig?.
  optionsFromValuesDependencies?.reduce(
    (acc, curr) => ({ ...acc, [curr]: pick(curr, formValues) }),
    {}
  )

  const onFileDrop = (newFiles: IDraftFile[]) => {
    let is_replace_existing = false
    if (replaceOption.id === 'replace') {
      is_replace_existing = true
    }
    const payload = { entity_type: 'inventoriesParseFile', file: newFiles[0], id: id as number, entityTypeId: 'inventory_id', is_replace_existing: is_replace_existing }
    dispatch(fileParse({ ...payload }))

    dispatch(sidebarItemSet({ ...formValues, is_being_processed: true }))
  }

  const isShowSelect = () => {
    if (uploadType === 'file' &&
      formValues.inventory_items &&
      Array.isArray(formValues.inventory_items) &&
      formValues.inventory_items.length > 0 &&
      formValues.state === 'drafted'
  ) {
    return (
      <Select
        value={replaceOption}
        isDisabled={formValues.is_being_processed}
        onChange={(option: { id: string; label: string }) => setReplaceOption(option)}
        options={[
          { id: 'add', label: t('forms:addToExistingItems') },
          { id: 'replace', label: t('forms:replaceExistingItems') },
        ]}
        withBorder
        margin="20px 0"
        />
      )}
  }

  const infoBlocks = [
    {
      label: t('organization'),
      icon: null,
      id: formValues?.organization_id,
      type: 'userpic',
      key: 'organization',
    },
    {
      label: t('warehouse'),
      icon: 'warehouseAndLocation',
      id: formValues?.warehouse_id,
      type: 'icon',
      key: 'warehouse',
    }
  ]

  return (
      <>
        {blockKey === 'info' && <InfoBlocks infoBlocks={infoBlocks} />}

        {blockKey === 'statusWithActions' && system?.inventoryStatuses && (
          <FlexRow gap="10px" alignItems="center" justifyContent="space-between">
            <StatusSelect
              id={formValues.id as number}
              type="inventories"
              currentStatus={formValues.status as string}
              systemStatuses={system?.inventoryStatuses}
              isDisabled
            />

            {/* @ts-ignore */}
            <ActionsDropdown selectedItem={formValues} type="inventories" />
          </FlexRow>
        )}

        {blockKey === 'activity' && (
          <ActivityTabs
            updateInput={updateInput}
            formValues={formValues as unknown as Record<string, unknown>}
            formValuesChanged={formValuesChanged as unknown as Record<string, unknown>}
            isReadOnly
            withTabsFieldsCustom={['attachments']}
          />
        )}

        {blockKey === 'inventory_items' && !id && rightPanelId !== 'clone' && (
          <InfoPanel text={t('forms:fillGeneralAndSaveBeforeUploadingFile')} />
        )}

        {blockKey === 'inventory_items' && (id || rightPanelId === 'clone') && (
          <>
            {!isReadOnly && rightPanelId !== 'clone' && (
              <UploadFileButtons<IInventories>
                formValues={formValues}
                uploadType={uploadType}
                setUploadType={setUploadType}
                setIsUploadFileActive={setIsUploadFileActive}
                isUploadFileActive={isUploadFileActive}
              />
            )}

            {uploadType === 'file' && isUploadFileActive && formValues.state !== 'posted' && (
              <div style={{margin: '20px 0'}}>
                {isShowSelect()}
              </div>
            )}

            {uploadType === 'file' && isUploadFileActive && formValues.state !== 'posted' && (
              <UploadFiles
                style={{ marginTop: 10, marginBottom: 10 }}
                entityType="inventories"
                entityId={id}
                fileGroup="inventory_items"
                fileTypes={{ 'application/vnd.ms-excel': FILE_TYPES.files['application/vnd.ms-excel'] }}
                filesType="files"
                onFnSet={onFileDrop as unknown as (newFiles: File[] | IDraftFile[]) => void}
                isUploadInProgress={formValues.is_being_processed as boolean}
                maxFiles={1}
                isReadOnly
              />
            )}

            <EditableTableFormPart
              additionalFormValues={additionalFormValues as unknown as Record<string, unknown>}
              id={id}
              cellProps={{
                system,
                custom_block_values: customBlockValues,
                lng: language,
                id,
                user,
                is_read_only: isReadOnly,
                additional_form_values: additionalFormValues,
              }}
              isNavbarHidden={uploadType === 'file'}
              compoundTypeKey="inventories.inventory_items"
              optionsData={optionsData}
              language={language}
              typeData={{ key: 'inventories.inventory_items' }}
              columns={editableTableColumns}
              data={formValues[blockKey] as unknown as Record<string, unknown>[]}
              blockKey={blockKey}
              formFieldsProps={formFieldsProps}
              fields={fields as unknown as IFieldsProps[]}
              getPanelHeader={getPanelHeader}
              updateTableRows={updateSelect.bind(null, blockKey)}
              validationRules={editableTableConfig?.validationRules as unknown as Record<string, unknown>}
              t={t}
            />
          </>
        )}

        {blockKey === 'notes' && (
          <Input
            multiline
            withBorder
            variant="secondary"
            value={formValues.notes || ''}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => updateInput({ target: { name: 'notes', value: event.target.value } })}
            maxLength="1000"
            readOnly={formValues.state === 'deleted'}
          />
        )}
        
        {children}
      </>
  )
}

export const InventoriesRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const params = useParams()
  const { rightPanelId } = params

  const dataInitial = useMappedState(selectSidebarInitialData) as unknown as IInventories

  const onSubmit = (formValuesChanged: IInventories) => {
    const requestBody = { ...object(formValuesChanged) } as IInventories

    Object.keys(requestBody).map((key) => {
      // @ts-ignore
      if (isObject(requestBody[key as keyof IInventories]) && (requestBody[key as keyof IInventories] as {id: number}).id) {
        // @ts-ignore
        requestBody[key as keyof IInventories] = requestBody[key as keyof IInventories]?.id
      }
    })

    if (!formValuesChanged.inventory_items?.length) {
      requestBody.inventory_items = [] as IBrandItem[]
    }

    if (!formValuesChanged?.state) {
      requestBody.state = 'drafted' as StateType
    }

    const dataToSend = {
      id: dataInitial.id,
      requestBody,
      type: 'inventories',
      parentType: 'documents',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm
    initialValues={dataInitial as unknown as Record<string, unknown>}
    formData={dataInitial?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new' ? {
      validationRules: {},
      fields: {
        info: [],
        statusWithActions: [],
        inventory_items: [],
        activity: [],
        notes: [],
      },
    } : inventoriesRightPanelConfig(dataInitial?.id  || rightPanelId === 'clone' ? 'edit' : 'create')}
    onSubmit={onSubmit}
    buttonsAreSticky
    withActions
    isReadOnly={dataInitial?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new'}
    FormBlockWrapper={FormBlockWrapper as unknown as ReactNode}
  />
}
