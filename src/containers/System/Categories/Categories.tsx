import { Outlet } from 'react-router'
import { TableBlockInfiniteScroll } from '../../../components/Table/TableBlockInfiniteScroll'
import GridLayout from '../../Layout/GridLayout'
import CategoriesTableCard from './CategoriesTableCard'

const Categories = () => {
  return (
    <GridLayout
      rightPanelComponent={<Outlet context={{}} />}
    >
      <TableBlockInfiniteScroll
        isMainTable
        TableCardContent={CategoriesTableCard as unknown as React.JSX.Element}
        tableCardHeight={80}
        iconName='flowchart'
      />
    </GridLayout>
  )
}

export default Categories
