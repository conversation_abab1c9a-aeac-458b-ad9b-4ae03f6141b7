import { all, call, put, select, takeLatest } from 'redux-saga/effects'
import { isObjectEmpty } from '@aidsupply/components'
import { TakeableChannel } from 'redux-saga'
import { DATA_TYPE_TO_SINGULAR_FORM } from '../../constants'
import { FilesType } from '../../components/Form/interfaces'
import { buildRequest, fetchData, showError } from '../../api'
import { ISideBar, sideBarUpsert, sideBarUpsertSuccess } from '../reducers/sideBar'
import { popupAlertShow } from '../reducers/common'
import { selectFileUploadState, selectSidebarInitialData, selectSidebarState, selectUserDetails } from '../selectors'
import { deleteAvatar, deleteAvatarError, deleteAvatarSuccess, deletePhoto, deletePhotoError, deletePhotoSuccess, fileUploadFailure, fileUploadSuccess, IFileUpload, IUploadedFile, uploadAvatar, uploadAvatarError, uploadAvatarSuccess } from '../reducers/fileUpload'
import { getCurrentUserSuccess, ICurrentUser } from '../reducers/user'
import { IResponse } from './auth'
import { IOrganization } from '../../commonInterfaces'

export function* getUploadedFilesData({ type, requestBody, id, filesType }:
  {
    type: string,
    requestBody: Record<string, unknown>,
    id: string,
    filesType: FilesType
  }
) {

  const sidebar: ISideBar = yield select(selectSidebarState)
  const fileUpload: IFileUpload = yield select(selectFileUploadState)

  try {
    const initialValues = type === 'enumerations' ? sidebar.initial?.options : sidebar.initial?.[filesType]

    const filesResult: IUploadedFile[] = yield all(
      Object.keys(fileUpload.files)
        .map((fileGroup) => {
          return fileUpload.files[fileGroup].map((file) => {
             // @ts-ignore
            return call(uploadFile, {
              file,
              entityType: type,
              entityId: id,
              fileGroup,
              entitySlug: type === 'enumerations' && (requestBody.slug || sidebar.initial?.slug),
              fileType: filesType === 'files' ? 'document' : 'image',
            })
          })
        })
        .flat()
    )

    const files: Record<string, IUploadedFile[]> = filesResult.reduce((acc, fileObj) => {
      // @ts-ignore
      if (acc[fileObj.fileGroup]) {
        return { ...acc, [fileObj.fileGroup as string]: [...acc[fileObj.fileGroup as string], fileObj] }
      } else {
        return { ...acc, [fileObj.fileGroup as string]: [fileObj] }
      }
    }, {} as Record<string, IUploadedFile[]>)

    const filesFormDataObject =
      files &&
      Object.keys(files).reduce((acc, fileGroup) => {
        const newActiveFromUpload = files[fileGroup] || []
        const newValuesFromFormData = type === 'enumerations' ? requestBody.options : requestBody[filesType]

        return type === 'enumerations'
          ? {
            // @ts-ignore
              options: (newValuesFromFormData || initialValues || []).map((option) => {
                return option.slug === fileGroup && newActiveFromUpload?.[0]
                  ? { ...option, image: newActiveFromUpload?.[0] }
                  : option
              }),
            }
          : {
              [filesType]: {
                // @ts-ignore
                ...(isObjectEmpty(acc[filesType])
                  ? newValuesFromFormData || initialValues || {}
                   // @ts-ignore
                  : acc[filesType]),
                [fileGroup]: {
                   // @ts-ignore
                  ...(newValuesFromFormData?.[fileGroup] || initialValues?.[fileGroup] || {}),
                  active: [
                     // @ts-ignore
                    ...(newValuesFromFormData?.[fileGroup]?.active ||
                       // @ts-ignore
                      initialValues?.[fileGroup]?.active ||
                      []),
                    ...newActiveFromUpload,
                  ],
                },
              },
            }
      }, {})

    yield put(fileUploadSuccess({ files }))

    if (filesType === 'files') {
      return filesFormDataObject
    }

    // for photos, not for files
    if (type === 'organizations' || type === 'users') {
      Object.keys(files).forEach((fileGroup) => {
        const primaryPhotoKey =
          (fileGroup === 'logos' && 'logo_photo_id') || (fileGroup === 'banners' && 'banner_photo_id')
        const primary_photo_id = sidebar.initial?.[primaryPhotoKey as string]

        if (!primary_photo_id) {
          // @ts-ignore
          filesFormDataObject.primaryPhotoObj = {
            // @ts-ignore
            ...filesFormDataObject.primaryPhotoObj,
            // @ts-ignore
            [primaryPhotoKey]: files[fileGroup][0]?.id,
          }
        }
      })
    } else {
      const primary_photo_id = sidebar.initial?.photo_id
      if (!primary_photo_id) {
        const filteredPhotos = filesResult.filter((el) =>
          ['main', 'general', 'logos', 'miniLogos'].includes(el.fileGroup as string)
        )
         // @ts-ignore
        filesFormDataObject.primaryPhotoObj = { photo_id: filteredPhotos[0]?.id || null }
      }
    }

    return filesFormDataObject
  } catch (error) {
    console.error(error)
  }
}

export function* doUploadAvatar({ payload }: {
  payload: {
    file: File & { preview: string }
    entityId: number
    entityType: string
    fileType: FilesType
    fileGroup?: string
  }
}) {
  try {
    const { file, entityId, entityType, fileType, fileGroup } = payload
    if (entityType !== 'users') {
      const result: IResponse = yield uploadFile({ file, entityId, entityType, fileType, fileGroup })
      yield showError(result)

      if (result) {
        const requestBodyKey = fileGroup === 'logos' && 'logo_photo_id' || fileGroup === 'banners' && 'banner_photo_id'
        const dataToSend = {
          id: entityId,
          type: entityType,
          requestBody: {
            [requestBodyKey as string]: result.id,
          },
          skipFileUpload: true,
        }
        yield put(
          sideBarUpsert({ ...dataToSend })
        )

        return result
      }

      return
    }

    if (entityType === 'users') {
      const fd = new FormData()
      fd.append('file', file)

      const request: Request = yield buildRequest({
        requestBody: fd,
        operation: 'create',
        type: 'filesUploadProfile',
        apiUrlParam: entityId
      })

      const result: IResponse = yield fetchData(request)

      yield showError(result)

      if (result.url) {
        const currentUser: IResponse = yield select(selectUserDetails)
        yield put(getCurrentUserSuccess({ ...currentUser, profile_pic: result.url as string }))
        yield put(uploadAvatarSuccess())
      }
    }
    return
  } catch (error) {
    yield put(uploadAvatarError())
    console.error(error)
  }
}

export function* uploadFile({ file, entityId, entityType, fileType, fileGroup = 'noGroup' }: {
    file: File & { preview: string }
    entityId: number
    entityType: string
    fileType: FilesType
    fileGroup?: string
  }) {
  const filePreviewId = file.preview

  try {
    const signedParams: Record<string, unknown> = {
      entity_id: entityId,
      entity_type: DATA_TYPE_TO_SINGULAR_FORM[entityType] || entityType,
      file_type: fileType,
      file_name: file.name,
      mime_type: file.type,
    }

    if (fileGroup && fileGroup !== 'noGroup') {
      signedParams.file_group = fileGroup
    }

    const fd = new FormData()
    fd.append('file_obj', file)
    for (const key in signedParams) {
      fd.append(key, signedParams[key] as File)
    }

    const request: Request = yield buildRequest({
      requestBody: fd,
      operation: 'create',
      type: 'filesUpload',
    })

    const result: IResponse = yield fetchData(request)

    if (result.url) {
      return {
        fileGroup,
        id: result.id,
        file_type: result.file_type,
        url: result.url,
        meta: {
          mime_type: (result.meta as unknown as Record<string, string>).mime_type,
          file_name_original: (result.meta as unknown as Record<string, string>).file_name_original,
          file_size: (result.meta as unknown as Record<string, string>).file_size,
        },
        filePreviewId,
      }
    } else {
      throw new Error('No "result.url" in response')
    }
  } catch (error) {
    console.error(error)
    yield put(fileUploadFailure( { filePreviewId, fileGroup }))
    yield put(popupAlertShow({ contentKey: 'errorInSavingData', type: 'error' }))
  }
}

function* doDeleteAvatar({ payload }: {
  payload: {
  entityType: string,
  entityId: number,
  }
}) {
  const { entityType, entityId } = payload

  try {
    const request: Request = yield buildRequest({
      operation: 'delete',
      type: 'filesUploadProfile',
      apiUrlParam: entityId,
    })

    const result: IResponse = yield fetchData(request)

    yield showError(result)

    if (result && !result.detail && entityType === 'users') {
      const currentUser: ICurrentUser = yield select(selectUserDetails)

      yield put(getCurrentUserSuccess({ ...currentUser, profile_pic: null }))
      yield put(deleteAvatarSuccess())
    }
  } catch (error) {
    yield put(deleteAvatarError())
    console.error(error)
  }
}

function* doDeletePhoto({ payload }: {
  payload: {
    primaryPhotoIdKey: string,
    entityId: number,
    entityType: string,
  }
}) {
  const { primaryPhotoIdKey, entityId, entityType } = payload

  const apiUrlType: Record<string, string> = {
    logo_photo_id: 'organizationsProfileImage',
    banner_photo_id: 'organizationsProfileBanner'
  }

  try {
    const request: Request = yield buildRequest({
      operation: 'delete',
      type: apiUrlType[primaryPhotoIdKey] || 'filesUploadProfile',
      apiUrlParam: entityId,
    })

    const result: IResponse = yield fetchData(request)

    if (result && entityType === 'users') {
      const currentUser: ICurrentUser = yield select(selectUserDetails)

      yield put(getCurrentUserSuccess({ ...currentUser, profile_pic: null }))
    }

    else if (result && entityType === 'organizations') {
      const myOrganizationInitial: IOrganization = yield select(selectSidebarInitialData)

      const initialDataKey: Record<string, string> = {
        logo_photo_id: 'logo_url',
        banner_photo_id: 'banner_url'
      }

      yield put(sideBarUpsertSuccess({
        item: {...myOrganizationInitial, [initialDataKey[primaryPhotoIdKey]]: null
      }}))
    }

    yield put(deletePhotoSuccess())
  } catch (error) {
    console.error(error)
    yield put(deletePhotoError())
  }
}

export default function* fileUploadSaga() {
  yield takeLatest(uploadAvatar.type as unknown as TakeableChannel<unknown>, doUploadAvatar)
  yield takeLatest(deleteAvatar.type as unknown as TakeableChannel<unknown>, doDeleteAvatar)
  yield takeLatest(deletePhoto.type as unknown as TakeableChannel<unknown>, doDeletePhoto)
}
