import { ChangeEvent, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import queryString from 'query-string'
import { useLocation, useNavigate } from 'react-router'
import { Input, Icon, usePrevious, Button } from '@aidsupply/components'
import { mapParamsToUrl } from '../../utils/filter'
import { TLanguages } from '../../locales'
import { IFacets } from '../../redux-saga/reducers/filters'
import { useDebounce } from '../../hooks/useDebounce.ts'

const SEARCH_QUERY_MIN_LENGTH = 2
const MAX_SEARCH_INPUT_LENGTH = 255

interface ITableSearch {
  type: string
  activeFilters?: Record<string, string[]>
  facets?: Record<string, IFacets[]>
  isDropdownSearch?: boolean
  iconRightProps?: Record<string, unknown>
  isMainTable?: boolean
  searchValueState?: string
  setSearchValue?: (val: string) => void
  iconLeftProps?: Record<string, unknown>
  data?: Record<string, unknown>[]
  placeholder?: string
  setTableData?: (data: Record<string, unknown>[]) => void
}

const TableSearch = ({
  type,
  activeFilters,
  facets,
  isDropdownSearch,
  iconRightProps,
  isMainTable,
  searchValueState,
  setSearchValue,
  iconLeftProps,
  placeholder,
  data,
  setTableData = () => {},
}: ITableSearch) => {
  const { i18n } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
  const { search } = location
  const searchParsed = queryString.parse(search)
  const hasCustomSearchState = typeof searchValueState !== 'undefined' && setSearchValue
  const searchValue = hasCustomSearchState ? searchValueState : (searchParsed.searchValue ?? '')

  const [inputValue, setInputValue] = useState(searchValue as string)
  const debouncedInputValue = useDebounce(inputValue, 700)
  const prevInputValue = usePrevious(debouncedInputValue || '') as string

  const [searchOpened, setSearchOpened] = useState(false)

  const prevType = usePrevious(type)
  useEffect(() => {
    if (type !== prevType && searchValue) {
      if (hasCustomSearchState) {
        setSearchValue('')
      } else {
        searchParsed.searchValue = ''
      }
    }
  }, [type, prevType])

  useEffect(() => {
    if (!isMainTable) {
      return
    }

    const trimmedValue = debouncedInputValue.trim()

    if (
      trimmedValue === searchValue ||
      (trimmedValue.length > 0 && trimmedValue.length < SEARCH_QUERY_MIN_LENGTH)
    ) {
      // ignore search
      return
    }

    if (isMainTable) {
      if (trimmedValue.length >= SEARCH_QUERY_MIN_LENGTH) {
        searchParsed.searchValue = encodeURIComponent(trimmedValue)
      } else if (trimmedValue.length === 0) {
        delete searchParsed.searchValue
        if (prevInputValue?.length > inputValue.length) {
          setInputValue('')
        }
      } else {
        console.log('Unmapped search corner case')
      }

      const urlParams = Object.keys(searchParsed).map((key) => `${key}=${searchParsed[key]}`)

      const searchFiltered = `?${urlParams.join('&')}`

      const { urlQuery } = mapParamsToUrl(
        type,
        searchFiltered,
        i18n.language as TLanguages,
        activeFilters,
        facets
      )

      navigate({
        pathname: location.pathname,
        search: urlQuery,
      })
    }
  }, [debouncedInputValue, searchValue, type, isMainTable])

  const onInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    setInputValue(value)
    const trimmedValue = value.trim()

    if (value.length === 0) {
      if (hasCustomSearchState) {
        setSearchValue('')
      }
      if (!isMainTable && data) {
        setTableData(data)
      }
      return
    }

    if (
      trimmedValue === searchValue ||
      (trimmedValue.length > 0 && trimmedValue.length < SEARCH_QUERY_MIN_LENGTH)
    ) {
      return
    }

    if (hasCustomSearchState) {
      setSearchValue(trimmedValue.length >= SEARCH_QUERY_MIN_LENGTH ? trimmedValue : '')
    }

    if (!isMainTable) {
      const lowerCasedSearch = trimmedValue.toLowerCase()
      const filteredData = data?.filter((item) =>
        Object.values(item).some((value) => String(value).toLowerCase().includes(lowerCasedSearch))
      )
      setTableData(filteredData as Record<string, unknown>[])
    }
  }

  const onSearchCleanClick = () => {
    setInputValue('')
    if (hasCustomSearchState) {
      setSearchValue('')
    }
  }

  const withSearchCleanCross = iconRightProps?.name === 'cross'
  const searchCleanIconProps = withSearchCleanCross &&
    inputValue && {
      ...iconRightProps,
      onClick: onSearchCleanClick,
      strokeWidth: 1,
    }

  const getInput = () => (
    <Input
      className="searchInput"
      fullWidth
      iconRightProps={searchCleanIconProps || (!withSearchCleanCross && iconRightProps)}
      iconLeftProps={{
        name: 'search',
        strokeWidth: 1,
        width: 20,
        height: 20,
        ...iconLeftProps,
      }}
      maxLength={MAX_SEARCH_INPUT_LENGTH}
      placeholder={placeholder || ''}
      onChange={onInputChange}
      primaryFocusColor
      value={inputValue}
      withoutValidation
      withBorder
    />
  )

  if (!isDropdownSearch) {
    return getInput()
  }

  return (
    <>
      <Button variant="bordered" onClick={() => setSearchOpened((prev) => !prev)} className="navBar search">
        <Icon name="search" width={20} height={20} strokeWidth={1.5} wrapperWidth={32} wrapperHeight={32} />
      </Button>
      {searchOpened && getInput()}
    </>
  )
}

export default TableSearch
