import { slugify } from 'transliteration'
import { IFormConfig } from '../../../components/Form/interfaces'

export const categoriesRightPanelConfig = (): IFormConfig => {
  return {
    withTabs: ['translations'],
    validationRules: {
      name: [
        'required',
        {
          type: 'keyInListAbsent',
          customRuleName: 'nameAlreadyExists',
          withSuccess: true,
          dataForValidation: {
            listKey: 'categories',
            searchKey: 'slug',
            transformValue: (value) => slugify(value),
          },
        },
      ],
      'translations.en': ['required'],
    },
    fields: {
      noTitle: [{
        key: 'state',
        label: 'state',
        optionsKeys: ['states'],
        component: 'dropdown',
      }],
      general: [
        {
          key: 'name',
          label: 'systemNameInEnglish',
          required: true,
          onInputValueChange: (value, setFormValues, initialValues) => {
            if (!initialValues?.id)
              // @ts-ignore
              setFormValues?.((prev) => {
                return { ...prev, 'translations.en': value }
              })
          },
        },
        {
          key: 'parent_id',
          label: 'parentCategory',
          labelKey: 'name',
          noTranslation: true,
          optionsKeys: ['categories'],
          component: 'dropdown',
          getOptionsFromFormValues: (_formValues, systemObject) => {
            const categoriesOptions = systemObject?.categories ? Object.values(systemObject?.categories) : []
            return categoriesOptions
          },
        },
        {
          key: 'is_used_in_inquiries',
          label: 'isUsedForInquiries',
          component: 'checkbox'
        }
      ],
      translations: [
        {
          key: 'translations',
          label: 'title',
          placeholder: 'Your item title',
          required: true,
        },
      ],
      photos: [],
    },
  }
}
