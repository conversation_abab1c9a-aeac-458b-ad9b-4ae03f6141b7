import { Icon, Tooltip, Typography, Userpic } from '@aidsupply/components'
import { useTheme } from 'styled-components'
import { useTranslation } from 'react-i18next'
import ReactTexty from '../../lib/react-texty'
import { formatDateToUserTimezone } from '../../utils/dates'
import { IRightPanelCard } from './rightPanelCardConfig'
import IconWithTooltip from '../IconWithTooltip/IconWithTooltip'
import { STATES_ICONS, STATUSES_ICONS } from '../config/table'
import { Status } from '../../commonTypes'
import { StateType } from '../Form/interfaces'
import { StyledBorderedContainerWrapper, StyledDateWrapper, StyledPicture, StyledTitle, StyledTitleContainer, StyledTitleWithTooltip, StyledTitleWrapper } from './styled'

const RightPanelCard = ({ data, isRightPanelExtendedWider }: { data: IRightPanelCard, isRightPanelExtendedWider?: boolean }) => {
  const { title, subTitle, profilePic, visited, created, withNoPic, children, state, status, createdDateWithTime } = data
  const theme = useTheme()
  const { t } = useTranslation(['table', 'form', 'statuses'])

  const getIconWithTooltip = () => {
    if (state && ['deleted', 'drafted'].includes(state as string)) {
      return  <IconWithTooltip text={t(state as StateType)} iconName={STATES_ICONS[state as StateType]} arrowPosition='left'/>
    }
    if (status) {
      return <IconWithTooltip text={t(`statuses:${status}`)} iconName={STATUSES_ICONS[status as Status]} arrowPosition='left'/>
    }
  };
  
  return(
    <>
      <StyledTitleContainer
        padding={createdDateWithTime ? '0 40px 0 0' : '0'} isRightPanelExtendedWider={isRightPanelExtendedWider}
      >
        { state || status ?
          <StyledTitleWithTooltip>
            {getIconWithTooltip()}
              <Typography as={ReactTexty} type="h3">
                {title}
              </Typography>
          </StyledTitleWithTooltip> :
          <StyledTitle>
            { withNoPic ? '' :
              profilePic || title ? <Userpic
                height="46px"
                width="46px"
                fullName={title}
                backgroundColor={theme.color.general.gray4}
                color={theme.color.general.light}
                src={profilePic}
              /> : <StyledPicture />
            }
            <StyledTitleWrapper withNoPic={withNoPic}>
              <Typography as={ReactTexty}
                type="h3"
              >
                {title}
              </Typography>
              <Typography as={ReactTexty}
                type="button1"
                fontWeight={400}
              >
                {subTitle}
              </Typography>
            </StyledTitleWrapper>
          </StyledTitle>
        }
        {createdDateWithTime &&
          <>
            {created ?
              <Typography as={ReactTexty}
                type="button1"
                fontWeight={400}
                color={theme.color.general.gray4}
                lineHeight={2.0}
              >
                <ReactTexty>
                  {formatDateToUserTimezone(created,'DD.MM.YYYY')}
                  <span className='dateDot' style={{
                    margin: '0 3px',
                    fontSize: '16px',
                    lineHeight: '21px',
                    color: theme.color.general.gray3
                  }}>•</span>
                  {formatDateToUserTimezone(created,'H:mm')}
                </ReactTexty>
                </Typography> : <div className='noData'>{'-'}</div>
              }
          </>
        }
      </StyledTitleContainer>
      {createdDateWithTime && <Typography as={ReactTexty} type="button1" fontWeight={400} color={theme.color.general.gray4} className='subTitle'>
        {subTitle}
      </Typography>
      }
      {!createdDateWithTime &&
        <StyledDateWrapper>
          <div className='container'>
            <Tooltip
              text={t(`${'created'}`)}
              arrowPosition='left'
            >
              <Icon
                name='calendarAdd'
                width={20}
                height={20}
                strokeWidth={1.4}
              />
            </Tooltip>
            {created ?
            <Typography as={ReactTexty}
              type="button1"
              fontWeight={400}
            >
              <ReactTexty>{formatDateToUserTimezone(created,'DD.MM.YYYY')}</ReactTexty>
              </Typography> : <div className='noData'>{'-'}</div>
            }
          </div>
          <div className='container'>
            <Tooltip
              text={t(`${'lastActivity'}`)}
              arrowPosition='left'
            >
              <Icon
                name='timeEdit'
                width={20}
                height={20}
                strokeWidth={1.4}
              />
            </Tooltip>
            {visited ?
            <Typography as={ReactTexty}
              type="button1"
              fontWeight={400}
            >
              <ReactTexty>{formatDateToUserTimezone(visited,'DD.MM.YYYY')}</ReactTexty>
              </Typography> : <div className='noData'>{'-'}</div>
            }
          </div>
        </StyledDateWrapper>
      }
      { children ? 
      <StyledBorderedContainerWrapper>
        {children}
      </StyledBorderedContainerWrapper>
      : ''}
    </>
  )
}

export default RightPanelCard
