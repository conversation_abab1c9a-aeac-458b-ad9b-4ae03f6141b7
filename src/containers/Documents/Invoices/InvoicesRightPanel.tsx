import { ReactNode, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useNavigate, useParams } from 'react-router'
import { FlexRow, Input, isObject } from '@aidsupply/components'
import { object, pick } from 'dot-object'
import { useTranslation } from 'react-i18next'
import { useMappedState } from '../../../hooks'
import { selectAllSystemCollections, selectSidebarInitialData, selectUserDetails } from '../../../redux-saga/selectors'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { IInvoices } from '../../../commonInterfaces'
import ActivityTabs from '../../../components/RightPanelForms/ActivityTabs'
import { IFieldsProps, IFormConfig, UpdateInput } from '../../../components/Form/interfaces'
import { invoicesRightPanelConfig } from './invoicesRightPanelConfig'
import StatusSelect from '../../../components/RightPanelForms/StatusSelect'
import ActionsDropdown from '../../../components/RightPanelForms/ActionsDropdown'
import EditableTableFormPart from '../../../components/RightPanelForms/EditableTableFormPart'
import { getEditableTableColumns } from '../../../components/config/columnsEditableTable'
import { TLanguages } from '../../../locales'
import { EDITABLE_TABLE_NAVBAR_FORM_CONFIG } from '../../../components/config/editableTableNavBarForms'
import InfoBlocks from '../utils/InfoBlocks'
import UploadFileButtons from '../utils/UploadFileButtons'

const FormBlockWrapper = ({
  id,
  children,
  blockKey,
  isReadOnly,
  formValues,
  updateInput,
  formValuesChanged,
  customBlockValues,
  optionsData,
  fields,
  formFieldsProps,
  getPanelHeader,
  updateSelect
}: {
  id: number
  children: ReactNode
  blockKey: string
  isReadOnly: boolean
  formValues: IInvoices
  updateInput: UpdateInput
  formValuesChanged: IInvoices
  customBlockValues: Record<string, unknown>
  optionsData: Record<string, unknown>
  fields: Record<string, unknown>
  formFieldsProps: Record<string, unknown>
  getPanelHeader: (blockKey: string) => string
  updateSelect: (blockKey: string) => void
}) => {
  const { t, i18n: { language } } = useTranslation(['table', 'forms', 'general'])
  const params = useParams()
  const { rightPanelId } = params

  const system = useMappedState(selectAllSystemCollections)
  const user = useMappedState(selectUserDetails)
  const { order_id } = formValues

  const [uploadType, setUploadType] = useState<'file' | 'table'>('file')

  const editableTableColumns = getEditableTableColumns('invoices.invoice_items', language as TLanguages, t)
  const editableTableConfig = EDITABLE_TABLE_NAVBAR_FORM_CONFIG['invoices.invoice_items'] as unknown as IFormConfig

  const additionalFormValues = editableTableConfig?.
  optionsFromValuesDependencies?.reduce(
    (acc, curr) => ({ ...acc, [curr]: pick(curr, formValues) }),
    {}
  )

  const infoBlocks = [
    {
      label: t('issuer'),
      icon: null,
      id: formValues?.issuer_id,
      type: 'userpic',
    },
    {
      label: t('payer'),
      icon: null,
      id: formValues?.payer_id,
      type: 'userpic',
    },
  ]

  return (
      <>
        {blockKey === 'info' && <InfoBlocks infoBlocks={infoBlocks} />}

        {blockKey === 'statusWithActions' && system?.invoiceStatuses && (
          <FlexRow gap="10px" alignItems="center" justifyContent="space-between">
            <StatusSelect
              id={formValues.id as number}
              type="invoices"
              currentStatus={formValues.status as string}
              systemStatuses={system?.invoiceStatuses}
              isDisabled={formValues.state === 'deleted'}
            />
            <ActionsDropdown selectedItem={formValues} type="invoices" />
          </FlexRow>
        )}

        {blockKey === 'activity' && (
          <ActivityTabs
            updateInput={updateInput}
            formValues={formValues as unknown as Record<string, unknown>}
            formValuesChanged={formValuesChanged as unknown as Record<string, unknown>}
            isReadOnly={formValues.state === 'deleted'}
            withTabsFieldsCustom={['comments', 'attachments']}
          />
        )}
        
        {blockKey === 'notes' && (
          <Input
            multiline
            withBorder
            variant="secondary"
            value={formValues.notes || ''}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => updateInput({ target: { name: 'notes', value: event.target.value } })}
            maxLength="1000"
            readOnly={formValues.state === 'deleted'}
          />
        )}

        {blockKey === 'invoice_items' && (id || rightPanelId === 'clone') && (
          <>
            {!isReadOnly && rightPanelId !== 'clone' && !order_id && (
              <UploadFileButtons<IInvoices>
                formValues={formValues}
                uploadType={uploadType}
                setUploadType={setUploadType}
              />
            )}

            <EditableTableFormPart
              additionalFormValues={additionalFormValues as unknown as Record<string, unknown>}
              id={id}
              cellProps={{
                system,
                custom_block_values: customBlockValues,
                lng: language,
                id,
                user,
                is_read_only: isReadOnly,
                additional_form_values: additionalFormValues,
              }}
              compoundTypeKey="invoices.invoice_items"
              isNavbarHidden={false}
              optionsData={optionsData}
              language={language}
              typeData={{ key: 'invoices.invoice_items' }}
              columns={editableTableColumns}
              data={formValues[blockKey] as unknown as Record<string, unknown>[]}
              blockKey={blockKey}
              formFieldsProps={formFieldsProps}
              fields={fields as unknown as IFieldsProps[]}
              getPanelHeader={getPanelHeader}
              updateTableRows={updateSelect.bind(null, blockKey)}
              validationRules={editableTableConfig.validationRules as unknown as Record<string, unknown>}
              t={t}
            />
          </>
        )}
        
        {children}
      </>
  )
}

const InvoicesRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const params = useParams()
  const { rightPanelId } = params

  const sidebarInitialData = useMappedState(selectSidebarInitialData) as unknown as IInvoices

  const onFormSubmit = (formValuesChanged: IInvoices) => {
    const requestBody = { ...object(formValuesChanged) } as IInvoices

    Object.keys(requestBody).map((key) => {
      if (isObject(requestBody[key as keyof IInvoices]) && (requestBody[key as keyof IInvoices] as {id: number}).id) {
        // @ts-ignores
        requestBody[key as keyof IInvoices] = (requestBody[key as keyof IInvoices])?.id
      }
    })

    requestBody.total =
      formValuesChanged?.subtotal ||
      (sidebarInitialData?.subtotal || 0) + (formValuesChanged?.tax || sidebarInitialData?.tax || 0)

      // TODO: add when added manually functionality
      // if (formValuesChanged.invoice_items?.length) {
      //   requestBody.invoice_items = formValuesChanged.invoice_items.map((item: IBrandItem) => ({
      //     order_item_id: typeof item.order_item_id === 'object' && item.order_item_id? (item.order_item_id as {id: string}).id : item.order_item_id,
      //     // invoice_id: sidebarInitialData.id || undefined,
      //     state: item.state,
      //     item_id: item.item_id,
      //     quantity: item.quantity,
      //     id: item.id,
      //   })) as IBrandItem[]
      // }

    const dataToSend = {
      id: sidebarInitialData?.id,
      requestBody,
      type: 'invoices',
      parentType: 'documents',
    }    

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }
  
  return <EditForm
    initialValues={sidebarInitialData as unknown as Record<string, unknown>}
    onSubmit={onFormSubmit}
    formData={sidebarInitialData?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new' ? {
      validationRules: {},
      fields: {
        info: [],
        statusWithActions: [],
        invoice_items: [],
        activity: [],
        notes: [],
      },
    } : invoicesRightPanelConfig(sidebarInitialData?.id  || rightPanelId === 'clone' ? 'edit' : 'create')}
    FormBlockWrapper={FormBlockWrapper as unknown as ReactNode}
    buttonsAreSticky
    withActions
    isReadOnly={sidebarInitialData?.state !== 'drafted' && rightPanelId !== 'clone' && rightPanelId !== 'new'}
  />
}

export default InvoicesRightPanel
