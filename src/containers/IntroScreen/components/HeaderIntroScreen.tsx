import { useContext } from 'react'
import { DefaultTheme } from 'styled-components'
import { ScreenContext } from '@aidsupply/components'
import Logo from '../../../components/Logo'
import LanguageSelect from '../../../components/LanguageSelect'
import { StyledHeader } from './styled'

const HeaderIntroScreen = ({ theme }: {
  theme: DefaultTheme
}) => {
  const { width, sm, lg, xl } = useContext(ScreenContext) || {}

  const isMobile = width && width < sm
  const isTablet = width && lg < width && width < xl
  const currWidth = !isMobile && !isTablet ? '140px' : '74px'

  return (
    <StyledHeader className="introHeader">
      <Logo theme={theme} variant={isMobile ? 'medium' : 'large'} to="../signin" isExtended />
      <LanguageSelect isIntroScreensSelect theme={theme} minWidth={currWidth} isFullSize={!isMobile && !isTablet} />
    </StyledHeader>
  )
}

export default HeaderIntroScreen
