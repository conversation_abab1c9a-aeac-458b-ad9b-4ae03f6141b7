import { Icon, Tag, Tooltip, Typography } from '@aidsupply/components'
import { useTranslation } from 'react-i18next'
import theme from '../../../theme'
import { TableCardStyled } from '../../../components/Table/styled'
import ReactTexty from '../../../lib/react-texty'
import { getAllItemsString, getFirstChart } from '../../../utils/common'
import { StateType } from '../../../components/Form/interfaces'
import { STATES_ICONS } from '../../../components/config/table'
import IconWithTooltip from '../../../components/IconWithTooltip/IconWithTooltip'
import { StyledBorderedContainerWrapper, StyledContentLocationWrapper, StyledContentWrapper } from './styled'

interface IWarehousesTableCard {
  id: number
  state: StateType
  name: string
  organization_name: string
  type: React.JSX.Element[]
  country_id: string
  city: React.JSX.Element
  // TODO: uncomment and fix when adding sum item from api
  // stocks_count: number
  // currency_id: React.JSX.Element
}

const WarehousesTableCard = (
  { data, initialData, className }: {
    data: IWarehousesTableCard,
    initialData: {[key: string]: unknown, type: string[]},
    className?: string
  }) => {
  const { id, state, name , organization_name, city } = data || {}
  const { type } = initialData
  const { t } = useTranslation('table')

  const allTypesString = getAllItemsString(type, t)

  return (
    <TableCardStyled key={id} direction='column' gap={4} className={className}>
      <StyledContentWrapper>
        <Typography as={ReactTexty} type="h4">
          {name}
        </Typography>
        <IconWithTooltip text={t(state)} iconName={STATES_ICONS[state]} />
      </StyledContentWrapper>
      <StyledContentWrapper>
        <Typography as={ReactTexty} type="button2" fontWeight="400">
          {organization_name}
        </Typography>
        <Tooltip
          textColor={theme.color.general.light}
          text={allTypesString}
          right="5px"
          arrowPosition="right"
          padding="15px 15px"
        >
          {type.map((item) => (
            <Tag key={item} text={getFirstChart(item, t)} />
          ))}
        </Tooltip>
      </StyledContentWrapper>
      <StyledBorderedContainerWrapper>
        <StyledContentLocationWrapper>
          <Icon name="marker" width={20} height={20} />
          <Typography as={ReactTexty} type="body1" fontWeight="400">
            {city}
          </Typography>
        </StyledContentLocationWrapper>
        {/* // TODO: uncomment and fix when adding sum item from api */}
        {/* <StyledContainerWrapper>
          <StyledContainer>
            <Icon name="deliveryBoxes" width={20} height={20} />
            {stocks_count}
          </StyledContainer>
          <div className='verticalLine'></div>
          <StyledContainer>
            <Icon name="currency" width={20} height={20} />
            <Typography as={ReactTexty} type="button1" fontWeight="400">
              {currency_id}
            </Typography>
          </StyledContainer>
        </StyledContainerWrapper> */}
      </StyledBorderedContainerWrapper>
    </TableCardStyled>
  )
}

export default WarehousesTableCard
