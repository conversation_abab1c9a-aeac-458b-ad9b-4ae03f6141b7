import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { PopupAlerts } from '@aidsupply/components'
import { popupAlertHide } from '../redux-saga/reducers/common'
import { selectPopupAlerts } from '../redux-saga/selectors'
import { useMappedState } from '../hooks'

const PopupAlertsComponent = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation('popupAlerts')
  
  const popupAlerts = useMappedState(selectPopupAlerts)

  return <PopupAlerts t={t} popupAlerts={popupAlerts} closeAction={(id: number) => 
    dispatch(popupAlertHide({ id }))}
  />
}

export default PopupAlertsComponent
