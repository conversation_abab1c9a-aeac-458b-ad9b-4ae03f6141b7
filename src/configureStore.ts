import { all } from 'redux-saga/effects'
import { applyMiddleware, combineReducers, legacy_createStore as createStore } from 'redux'
import createSagaMiddleware from 'redux-saga'
import { composeWithDevToolsDevelopmentOnly } from '@redux-devtools/extension'

import authSaga from './redux-saga/sagas/auth'
import initialSaga from './redux-saga/sagas/init'
import fileUploadSaga from './redux-saga/sagas/fileUpload'
import user, { IUser } from './redux-saga/reducers/user'
import fileUpload, { IFileUpload } from './redux-saga/reducers/fileUpload'
import common, { ICommon } from './redux-saga/reducers/common'
import commonSaga from './redux-saga/sagas/common'
import dataSaga from './redux-saga/sagas/data'
import data, { IData } from './redux-saga/reducers/data'
import sideBar, { ISideBar } from './redux-saga/reducers/sideBar'
import sideBarSaga from './redux-saga/sagas/sideBar'
import filters, { IFiltersState } from './redux-saga/reducers/filters'
import forms, { IFormsState } from './redux-saga/reducers/forms.ts'

function* rootSaga() {
  yield all([authSaga(), initialSaga(), fileUploadSaga(), commonSaga(), dataSaga(), sideBarSaga()])
}

const reducers = combineReducers({
  user,
  common,
  fileUpload,
  data,
  sideBar,
  filters,
  forms,
})

export interface IStore {
  user: IUser
  common: ICommon
  fileUpload: IFileUpload
  data: IData
  sideBar: ISideBar
  filters: IFiltersState
  forms: IFormsState
}

export default function configureStore() {
  const sagaMiddleware = createSagaMiddleware()
  const middlewares = [sagaMiddleware]

  const middlewareEnhancer = applyMiddleware(...middlewares)

  const enhancers = [middlewareEnhancer]
  const composedEnhancers = composeWithDevToolsDevelopmentOnly(...enhancers)

  const store = createStore(reducers, undefined, composedEnhancers)

  sagaMiddleware.run(rootSaga)

  return store
}
