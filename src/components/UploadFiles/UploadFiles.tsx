import { useDispatch } from 'react-redux'
import { Accept } from 'react-dropzone'
import { useTranslation } from 'react-i18next'
import { isObjectEmpty, TextPanel } from '@aidsupply/components'
import { FILE_TYPES } from '../../constants'
import { IDraftFile, inputFilesAdd, inputFilesRemove } from '../../redux-saga/reducers/fileUpload'
import { useMappedState } from '../../hooks'
import { selectFileUploadState } from '../../redux-saga/selectors'
import DropZone from '../DropZone'
import { StyledThumbnails } from './styled'
import Thumbnail from './Thumbnail'

export interface IUploadFiles {
  entityType: string,
  entityId?: number,
  fileGroup: string
  maxFiles?: number
  isSimpleButton?: boolean
  withImagePreview?: boolean
  onFnSet?: (newFiles: File[] | IDraftFile[]) => void
  filesType?: 'photos' | 'files'
  fileTypes?: string | Record<string, string[]>
  simpleButtonProps?: Record<string, unknown>
  isUploadInProgress?: boolean
  isMultipleUploadDisabled?: boolean
  isReadOnly?: boolean
  style?: Record<string, unknown>
}

const UploadFiles = ({
  style,
  fileGroup,
  maxFiles,
  isSimpleButton,
  onFnSet,
  withImagePreview = true,
  filesType = 'photos',
  fileTypes,
  simpleButtonProps,
  isUploadInProgress,
  isMultipleUploadDisabled,
  isReadOnly,
}: IUploadFiles) => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { files, filesInProgress, errors } = useMappedState(selectFileUploadState)
  const fileType = fileTypes || (filesType === 'files' ? FILE_TYPES.files : FILE_TYPES.images)

  const fnSet = (newFiles: IDraftFile[]) => {
    dispatch(inputFilesAdd({ newFiles, fileGroup, filesType }))
    if (onFnSet) {
      onFnSet(newFiles)
    }
  }

  const fnDelete = (id: string) => {
    dispatch(inputFilesRemove({ id, fileGroup }))
  }

  return (
    <StyledThumbnails style={style}>
    {!isSimpleButton && withImagePreview && (
      <aside className="thumbnails">
        {files[fileGroup] &&
          files[fileGroup].map((file) => (
            <Thumbnail
              error={(errors[fileGroup] as unknown as string[])?.includes(file.preview)}
              fnDelete={fnDelete}
              key={file.preview}
              // @ts-ignore
              loading={filesInProgress[fileGroup]?.includes(file.preview)}
              src={file.preview}
              disableImagekit
              fileSize={file.size}
              fileName={file.name}
              filesType={filesType}
              isReadOnly={isReadOnly}
            />
          ))}
      </aside>
    )}

    {!isSimpleButton && errors?.[fileGroup] && !isObjectEmpty(errors[fileGroup]) && (
      <TextPanel type="error" content={t('Some files were not uploaded. Please try again.')} />
    )}

    <DropZone
      fileType={fileType as Accept}
      setFiles={fnSet}
      fileGroup={fileGroup}
      currentFiles={files[fileGroup]}
      isSimpleButton={isSimpleButton}
      simpleButtonProps={simpleButtonProps}
      maxFiles={maxFiles}
      isMultipleUploadDisabled={isMultipleUploadDisabled}
      isUploadInProgress={isUploadInProgress}
    />
  </StyledThumbnails>
  )
}

export default UploadFiles
