import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { useTheme } from 'styled-components'
// import { useDispatch } from 'react-redux'
import { pick } from 'dot-object'
import { Icon, Label, Typography } from '@aidsupply/components'
// import { markNotificationAsOld, sidebarFetchById, sidebarItemSet } from '../../redux/actions'
import { STATES_ICONS, STATES_OPTIONS, STATUSES_ICONS, STATUSES_OPTIONS } from '../config/table'
import { MENU_ICONS_BY_TYPES } from '../config'
import { INotification } from '../../redux-saga/reducers/common'
import { useTranslation } from 'react-i18next'
import { DATA_TYPE_TO_SINGULAR_FORM } from '../../constants'
// import { selectDataType, selectSystemCollections } from '../../redux/selectors'
import { StyledNotificationItem } from './styled'

dayjs.extend(relativeTime)

const ICONS: Record<string, string> = {
  ...STATUSES_ICONS,
  ...STATES_ICONS,
  // update: 'reload',
  // create: STATUSES_ICONS.new,
}

const INFO = (language: string): Record<string, string> => ({
  brands: 'name',
  carriers: 'name',
  orders: 'id',
  'stock-items': '',
  warehouses: 'name',
  items: 'model',
  categories: `label.${language}`,
  users: 'email', //general.firstName,general.lastName
  enumerations: `translations.${language}`,
  units: `translations.${language}`,
  attributes: `translations.${language}`,
  inquiries: 'id',
  invoices: 'id',
  contracts: 'id',
  organizations: 'name',
  shipments: 'id',
})

const NotificationItem = (
  { 
    item,
    language
  }: {
    item: INotification
    language: 'en' | 'uk'
  }
) => {
  // const dispatch = useDispatch()
  const theme = useTheme()
  const { t } = useTranslation('notifications')

  // const type = useMappedState(selectDataType)
  // const users = useMappedState(selectSystemCollections('users'))

  const { operation, entity, collection, isNew } = item
  const date = dayjs(entity.created_at).locale(language).fromNow()

  // const user = users && Object.values(users).find((user) => user.username === entity.user)

  const getText = () => {
    const statusOrStateObj = STATUSES_OPTIONS[operation] || STATES_OPTIONS[operation]
  
    const label = statusOrStateObj?.label;
    const statusText =
          label && typeof label === 'object'
            ? label[language]
            : t(`${operation}d`);
  
    return t(collection, { status: statusText.toLowerCase() })
  }

  const getIconWrapperColor = () => {
    if (operation === 'create' || operation === 'new') {
      return theme.color.status.new
    } else if (operation === 'update') {
      return theme.color.primary.main
    } else if (operation === 'deleted' || operation === 'canceled') {
      return theme.color.status.error
    } else if (operation === 'drafted') {
      return theme.color.general.gray2
    } else {
      return theme.color.status.success
    }
  }

  const getIcon = () => {
    if (operation === 'create' || operation === 'update' || operation === 'new') {
      return MENU_ICONS_BY_TYPES[collection]
    } else {
      return ICONS[collection]
    }
  }

  const getInfo = () => {
    const key = INFO(language)[collection]
    const text = key && pick(key, entity)

    return text && collection === 'orders' ? `#${text}` : text
  }

  // const onItemClick = () => {
  //   if (type !== collection) {
  //     dispatch(sidebarFetchById(entity.id, collection))
  //   } else {
  //     dispatch(sidebarItemSet(entity))
  //   }
  //   dispatch(markNotificationAsOld(entity.id))
  // }

  return (
    <StyledNotificationItem
      className={isNew ? 'new' : undefined}
      // noStyles
      // Link={Link}
      // onClick={onItemClick}
      // to={`${
      //   Object.values(SUBMENU)
      //     .flat()
      //     .find((menuItem) => menuItem.key === collection)?.route
      // }/${entity.id}`}
    >
      <Icon
        name={getIcon()}
        fill={theme.color.general.light}
        shade={
          (operation === 'create' || operation === 'update' || operation === 'new') && getIconWrapperColor()
        }
        wrapperColor={getIconWrapperColor()}
        wrapperHeight={37}
        wrapperWidth={37}
        width={22}
        height={22}
        borderRadius="50%"
      />
      <div className="content">
        <Typography type="body2" component="div" className="firstRow">
          {getText()}
        </Typography>
        {getInfo() && (
          <div className="info row">
            <Typography type="body1">{getInfo()}</Typography>
          </div>
        )}
        <Typography collection="body2">{t(`table:${DATA_TYPE_TO_SINGULAR_FORM[collection]}`)}</Typography>
        <div className="labels row">
          {/* {user && (
            <Label
              collection="smaller"
              text={`${user.general.firstName} ${user.general.lastName}`}
              color={color.general.gray5}
            />
          )} */}
          <Label collection="smaller" text={date} color={theme.color.general.gray5} />
        </div>
      </div>
    </StyledNotificationItem>
  )
}

export default NotificationItem
