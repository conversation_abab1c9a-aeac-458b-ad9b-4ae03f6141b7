import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import dot from 'dot-object'
import merge from 'deepmerge'
import { isObjectEmpty } from '@aidsupply/components'
import { DEFAULT_VALUES_DATA } from '../../data/defaultValues'
import { signOut } from './user.ts'

export type ValueMap = Record<string, unknown>

export type AddToFormValuesChangedPayload = {
  type: string
  valuesChanged?: ValueMap
}

export type RemoveFromFormValuesChangedPayload = {
  type: string
  path: string
}

export type AddToFormValuesChangedEditableTablePayload = {
  type: string
  blockKey: string
  valueChanged: unknown
  rowIndex?: number
}

export type SetFormValuesChangedPayload = {
  type: string
  valuesChanged: ValueMap
}

export type SetFormValuesInitialPayload = {
  type: string
  valuesInitial: ValueMap
}

export type SetFormValuesChangedOnChangingBlockPayload = {
  type: string
  changingBlocksDependencyValue?: unknown
}

export type ResetFormValuesChangedPayload = {
  type: string
  initialValuesChanged?: ValueMap
}

export interface FormState {
  valuesChanged?: Record<string, any>
  valuesInitial?: Record<string, any>
}

export type IFormsState = Record<string, FormState>

const initialState: IFormsState = {}

const formsSlice = createSlice({
  name: 'forms',
  initialState,
  reducers: {
    addToFormValuesChanged(state, action: PayloadAction<{ type: string; valuesChanged?: Record<string, any> }>) {
      const { type, valuesChanged } = action.payload
      if (!valuesChanged || isObjectEmpty(valuesChanged)) return

      if (!state[type]) {
        state[type] = { valuesChanged: { ...valuesChanged } }
        return
      }

      const allValuesChanged = state[type].valuesChanged || {}
      const newValuesChanged = Object.keys(valuesChanged).reduce(
        (acc, currKey) => {
          acc[currKey] = currKey in valuesChanged ? (valuesChanged as any)[currKey] : (allValuesChanged as any)[currKey]
          return acc
        },
        { ...allValuesChanged } as Record<string, any>
      )

      state[type].valuesChanged = newValuesChanged
    },

    removeFromFormValuesChanged(state, action: PayloadAction<{ type: string; path: string }>) {
      const { type, path } = action.payload
      if (!state[type]) state[type] = {}
      if (!state[type].valuesChanged) state[type].valuesChanged = {}
      state[type].valuesChanged![path] = undefined // mirrors original behavior
    },

    addToFormValuesChangedEditableTable(
      state,
      action: PayloadAction<{ type: string; blockKey: string; valueChanged: any; rowIndex?: number }>
    ) {
      const { type, blockKey, valueChanged, rowIndex } = action.payload

      if (!state[type]) {
        state[type] = { valuesChanged: { [blockKey]: [valueChanged] } }
        return
      }

      const oldValuesChanged = state[type].valuesChanged || {}
      const initialValue = state[type].valuesInitial?.[blockKey] as any[] | undefined
      const oldValueChanged = (oldValuesChanged as any)[blockKey] as any[] | undefined

      const base = (oldValueChanged || initialValue) ?? []
      const updated = (base.length ? base : [valueChanged]).map((row: any, index: number) =>
        (row && valueChanged && row.id !== undefined && row.id === valueChanged.id) || index === rowIndex
          ? { ...row, ...valueChanged }
          : row
      )

      state[type].valuesChanged = {
        ...oldValuesChanged,
        [blockKey]: updated,
      }
    },

    setFormValuesChanged(state, action: PayloadAction<{ type: string; valuesChanged: Record<string, any> }>) {
      const { type, valuesChanged } = action.payload
      if (!state[type]) state[type] = {}
      state[type].valuesChanged = { ...(state[type].valuesChanged || {}), ...valuesChanged }
    },

    setFormValuesInitial(state, action: PayloadAction<{ type: string; valuesInitial: Record<string, any> }>) {
      const { type, valuesInitial } = action.payload
      if (!state[type]) state[type] = {}
      state[type].valuesInitial = valuesInitial
    },

    setFormValuesChangedOnChangingBlock(
      state,
      action: PayloadAction<{
        type: string
        changingBlocksDependencyValue?: any
      }>
    ) {
      const { type, changingBlocksDependencyValue } = action.payload

      const defaultCommonValuesDotted = dot.dot(DEFAULT_VALUES_DATA[type] ?? {})
      const formValuesChanged = state[type]?.valuesChanged || {}
      const changingValue = changingBlocksDependencyValue?.id ?? changingBlocksDependencyValue

      const currentCommonValuesChanged = Object.keys(defaultCommonValuesDotted).reduce(
        (acc: Record<string, any>, curr) => {
          if (typeof (formValuesChanged as any)[curr] !== 'undefined') {
            acc[curr] = (formValuesChanged as any)[curr]
          }
          return acc
        },
        {}
      )

      const newValues =
        changingValue != null
          ? merge<Record<string, any>>(currentCommonValuesChanged, dot.dot(DEFAULT_VALUES_DATA[changingValue] ?? {}))
          : currentCommonValuesChanged

      if (!state[type]) state[type] = {}
      state[type].valuesChanged = newValues
    },

    resetFormValuesChanged(state, action: PayloadAction<{ type: string; initialValuesChanged?: Record<string, any> }>) {
      const { type, initialValuesChanged } = action.payload
      if (!state[type]) state[type] = {}
      state[type].valuesChanged = initialValuesChanged ?? (initialState as any)[type]
    },
  },

  // Keep listening for external sign-out action (from legacy/other slice)
  extraReducers: (builder) => {
    builder.addCase(signOut, () => initialState)
  },
})

export const {
  addToFormValuesChanged,
  removeFromFormValuesChanged,
  addToFormValuesChangedEditableTable,
  setFormValuesChanged,
  setFormValuesInitial,
  setFormValuesChangedOnChangingBlock,
  resetFormValuesChanged,
} = formsSlice.actions

export default formsSlice.reducer
