import styled from 'styled-components'
import { NAV_MENU_WIDTH, TOP_BAR_BORDER_BOTTOM, TOP_BAR_HEIGHT,TOP_BAR_HEIGHT_XL } from '../../constants'

export const StyledNavMenu = styled.nav`{
  background: ${({ theme }) => theme.color.midnight.lighter};
  position: absolute;
  top: 0;
  left: 0;
  transition: width 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  border-right: 1px solid ${({ theme }) => theme.color.midnight.light};
  display: flex;
  padding: 10px 0;
  flex-shrink: 0;
  flex-direction: column;
  height: 100vh;
  z-index: 20;

  hr {
    display: block;
  }

  .label {
    color: ${({ theme }) => theme.color.general.gray3};
    pointer-events: none;
    height: 21px;
    display: block;
  }

  .topWrapper {
    padding: 0 10px 40px;
  }

  &.extended {
    width: ${NAV_MENU_WIDTH.desktop.extended};

    hr {
      display: none;
    }

    &.tabletView {
      opacity: 1;
      pointer-events: auto;
      overflow-y: auto;
      transform: translateY(0%);
      transition: transform 0.4s ease-in-out;
      overflow-y: auto;
      position: absolute;
      top: ${TOP_BAR_HEIGHT} + ${TOP_BAR_BORDER_BOTTOM};
      bottom: 0;
      left: 0;
      width: 300px;
      border-radius: 0 0 5px 0;
      box-shadow: 0 15px 25px ${({ theme }) => `${theme.color.general.light}14`};

      @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
        top: ${TOP_BAR_HEIGHT_XL} + ${TOP_BAR_BORDER_BOTTOM};
      }

      &.mobileView {
        width: 100%;
        height: calc(100vh - ${TOP_BAR_HEIGHT_XL}px - ${TOP_BAR_BORDER_BOTTOM}px);
      }
    }
  }

  &.condensed {
    width: ${NAV_MENU_WIDTH.desktop.condensed};

    &.tabletView {
      transform: translateY(-100%);
      transition: transform 0.4s ease-in-out, opacity 0.3s ease;
      opacity: 0;
      pointer-events: none;
      width: 0;
    }
  }
}`

export const StyledMenuItems = styled.div<{
  margin?: string | null
  textColor?: string
}>`
  display: flex;
  flex-direction: column;
  margin: ${({ margin }) => margin};

  a {
    width: 100%;
    color: ${({ textColor }) => textColor};
  }
`

export const StyledMenuItem = styled.div`
  padding: 6px 10px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  position: relative;

  &.condensedMenu {

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
      .icon {
        opacity: 0;
      }
    }
  }

  &.active.extendedMenu,
  &.active.condensedMenu {
    background-color: ${({ theme }) => theme.color.status.new};
    border-radius: 6px;

    .typography {
      color: ${({ theme }) => theme.color.general.light};
    }
  }

  &.active.condensedMenu {
    @media only screen and (max-width: ${({ theme }) => theme.breakpoints.xl}px) {
      background-color: transparent;
    }
  }

  .typography {
    margin-left: 6px;
    color: ${({ theme }) => theme.color.general.dark};
  }

  .condensedItem {
    opacity: 0;
    transform: translateX(-100%);

    &.typography {
      margin-left: 0;
      display: none;
    }
  }

  .extendedItem {
    transform: translateX(0%);
    opacity: 1;
    color: ${({ theme }) => theme.color.general.dark};

    &:hover,
    &:focus {
      transition: none;
    }
  }

  .icon {
    margin: 0;
  }

  a,
  .typography {
    color: ${({ theme }) => theme.color.general.dark};
  }
`

export const StyledLine = styled.hr`
  color: ${({ theme }) => theme.color.general.light};
  background-color: ${({ theme }) => theme.color.general.gray2};
  border-width: 0;
  height: 1px;
  margin: 10px 0;
`