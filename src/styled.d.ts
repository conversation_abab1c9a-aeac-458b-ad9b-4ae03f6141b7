import 'styled-components'

export interface IGrid {
  columns: { count: number; gapWidth: string }
  rightPanelCols: number
  leftPanelCols: number
  rightPanelColsWide: number
}

export interface IFontSizeTheme {
  value: string
  lineHeight: string
  name: string
}

export interface ITypography {
  color?: string
  'line-height'?: string
  'font-size'?: string
  'font-weight'?: number
  'font-family'?: string
  name?: string
}

export interface IButtonTheme extends ITypography {
  'background-color'?: string
  'border-radius'?: string
  border?: string
  padding?: string
}

export interface IInputTheme extends ITypography {
  'background-color'?: string;
  'border-radius'?: string;
  padding?: string;
  'box-shadow'?: string;
}

declare module 'styled-components' {
  export interface DefaultTheme {
    logoSrc: string,
    platform: string,
    title: string,
    breakpoints: {
      xxl: number
      xl: number
      lg: number
      md: number
      sm: number
      xs: number
      xxs: number
    }
    gutter: {
      container: {
        side: {
          md: number
          base: number
          xl: number
          lg: number
          sm: number
          xs: number
          xxs: number
        }
      }
    }
    grid: {
      xxl: IGrid
      xl: IGrid
      lg: IGrid
      md: IGrid
      sm: IGrid
      xs: IGrid
      xxs: IGrid
    }
    color: {
      primary: {
        lightest: string
        lighter: string
        light: string
        main: string
        dark: string
        darker: string
        darkest: string
      }
      secondary: {
        lightest: string
        lighter: string
        light: string
        main: string
        dark: string
        darker: string
        darkest: string
      }
      general: {
        dark: string
        light: string
        gray1: string
        gray2: string
        gray3: string
        gray4: string
        gray5: string
        gray6: string
        gray7: string
      }
      status: {
        success: string
        warning: string
        error: string
        new: string
      }
      midnight: {
        light: string
        lighter: string
      }
    }
    font: {
      family: {
        primary: string
        primaryCondensed: string
        secondary: string
        secondaryCondensed: string
      }
      size: {
        h1: IFontSizeTheme
        h2: IFontSizeTheme
        h3: IFontSizeTheme
        h4: IFontSizeTheme
        body1: IFontSizeTheme
        body2: IFontSizeTheme
        button1: IFontSizeTheme
        button2: IFontSizeTheme
        link: IFontSizeTheme
      }
      weight: {
        light: number
        regular: number
        medium: number
        bold: number
        black: number
      }
    }
    size: {
      border: {
        radius: {
          main: string
          smaller: string
          bigger: string
        }
      }
      height: {
        header: {
          mobile: string
        }
        footer: {
          desktop: string
          mobile: string
        }
      }
    }
    components: {
      header: {
        standard: {
          regular: {
            large: {
              color: string
              'background-color': string
              name: string
            }
            small: {
              color: string
              'background-color': string
              name: string
            }
          }
        }
      }
      button: {
        standard: {
          primary: {
            large: IButtonTheme
            small: IButtonTheme
          }
          secondary: {
            large: IButtonTheme
            small: IButtonTheme
          }
          bordered: {
            large: IButtonTheme
            small: IButtonTheme
          }
          disabled: {
            large: IButtonTheme
            small: IButtonTheme
          }
          success: {
            large: IButtonTheme
            small: IButtonTheme
          }
        }
      }
      typography: {
        h1: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        h2: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        h3: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        h4: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        caption1: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        caption2: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        caption3: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        body1: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        body2: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        link: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        button1: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        button2: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
        input: {
          black: {
            large: ITypography
            medium: ITypography
            small: ITypography
          }
        }
      }
      link: {
        standard: {
          main: {
            large: ITypography
          }
        }
      }
      input: {
        primary: {
          black: {
            large: IInputTheme
            small: IInputTheme
          }
        }
        secondary: {
          black: {
            large: IInputTheme
            small: IInputTheme
          }
        }
        transparent: {
          black: {
            large: IInputTheme
            small: IInputTheme
          }
          white: {
            large: IInputTheme
            small: IInputTheme
          }
        }
      }
      homeTextPanelText: {
        standard: {
          large: ITypography
          small: ITypography
        },
      },
      select: {
        primary: {
          black: {
            large: IInputTheme
          }
        }
        secondary: {
          black: {
            large: IInputTheme
          }
        }
      },
      badge: {
        standard: {
          primary: {
            large: IInputTheme
          }
        }
      },
      intro: {
        standard: {
          maintenance: {
            large: {
              color: string
              name: string
            },
            medium: {
              color: string
              name: string
            },
            small: {
              color: string
              'line-height': string
              'font-size': string
              'font-weight': number
              'font-family': string
              name: string
            },
          },
          home: {
            large: {
              color: string
              name: string
            },
            medium: {
              color: string
              name: string
            },
            small: {
              color: string
              name: string
            },
          },
        },
      },
      tag: {
        standard: {
          gray: {
            large: IInputTheme
          },
          primary: {
            large: IInputTheme
          },
        },
      },
      label: {
        standard: {
          uppercase: {
            large: ITypography
            small: ITypography
          },
          bigger: {
            large: ITypography
            small: ITypography
          },
          smaller: {
            large: ITypography
            small: ITypography
          },
        },
      },
      popupAlert: {
        standard: {
          default: {
            large: IInputTheme
          },
        },
      },
      miniLogoWrapper: {
        standard: {
          main: {
            large: {
              'background-color': string
              'border-radius': string
              name: string
            },
          },
        },
      },
    }
    appName: string
    name: string
  }
}
