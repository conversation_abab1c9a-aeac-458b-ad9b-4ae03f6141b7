import { Typography, Tag } from '@aidsupply/components'
import { getNameByMode, headerRenderer } from '../../../utils/table'
import { TLanguages } from '../../../locales'
import { TableModeType } from '../../../redux-saga/reducers/data'
import ReactTexty from '../../../lib/react-texty'
import { getDate } from '../../../components/config/columns'
import { REPORT_TYPES } from '../../../data'

export const reportsColumnsConfig = (
  lng: TLanguages,
  t: (key: string) => string,
  tableMode?: TableModeType,
) => [
  getNameByMode({
    t,
    dataKey: 'translations',
    option: 'state',
    key: 'title',
    tableMode: tableMode || 'table',
    lng
  }),
  {
    key: 'period',
    dataKey: 'period',
    title: t('period'),
    sortable: true,
    width: 0,
    flexGrow: 1,
    cellRenderer: ({ rowData }: { rowData: { period: string } }) => {
      const reportType = REPORT_TYPES.find((type) => type.id === rowData.period);
      const label = typeof reportType?.label === 'string' ? reportType?.label : reportType?.label[lng];
      return <Tag text={label} />;
    },
    headerRenderer: headerRenderer(t, tableMode),
  },
  {
    key: 'year',
    dataKey: 'year',
    title: t('year'),
    sortable: true,
    width: 0,
    flexGrow: 1,
    cellRenderer: ({ rowData }: { rowData: { year: string } }) => <Typography as={ReactTexty}>{rowData.year}</Typography>,
    headerRenderer: headerRenderer(t, tableMode),
  },
  {
    key: 'month',
    dataKey: 'month',
    title: t('month'),
    sortable: true,
    width: 0,
    flexGrow: 1,
    cellRenderer: ({ rowData }: { rowData: { month: string } }) => rowData.month ? <Typography as={ReactTexty}>{typeof rowData.month === 'string' ? rowData.month.charAt(0).toUpperCase() + rowData.month.slice(1) : rowData.month}</Typography> : null,
    headerRenderer: headerRenderer(t, tableMode),
  },
  getDate(t, tableMode),
  {
    key: 'organization',
    sortable: true,
    dataKey: 'organization_id',
    labelKey: 'organization',
    optionsKeys: ['organizations'],
    noTranslation: true,
    width: 0,
    flexGrow: 1,
    cellRenderer: ({ rowData, container }: any) => {
      const orgs = container?.props?.cellProps?.system?.organizations || {}
      const org = orgs[rowData.organization_id] || Object.values(orgs).find((o: any) => o.id === rowData.organization_id)
      const orgName = org?.name || 'N/A'
      return (
        <Typography as={ReactTexty}>{orgName}</Typography>
      )
    },
    headerRenderer: headerRenderer(t, tableMode),
  }
]
