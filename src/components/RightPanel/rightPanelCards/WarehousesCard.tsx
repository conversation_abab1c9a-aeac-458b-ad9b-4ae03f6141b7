import { Icon, Tag, Typography } from '@aidsupply/components'
import { StyledContainerWrapper } from '../styled'
import ReactTexty from '../../../lib/react-texty'
import AddressTableCell from '../../Table/components/AddressTableCell'

// TODO: remove component if it will not be used in the final version
const WarehousesCard = ({t, data}: {
  t: (key: string, ns?: {ns: string}) => string,
  data?: Record<string, unknown>
}) => {
  const onlyFlag = true
  return (
    <>
      <StyledContainerWrapper>
        <div className="container">
          <Icon name="marker" width={20} height={20} />
          <Typography as={ReactTexty}
            type="button1"
            fontWeight={400}
          >
            {`${t('location')}:`}
          </Typography>
        </div>
        <div className="container">
        {data && <AddressTableCell rowData={data} onlyFlag={onlyFlag}/>}
          <Typography as={ReactTexty}
            type="button1"
            fontWeight={400}
          >
            {data?.city}
          </Typography>
        </div>
      </StyledContainerWrapper>
      <StyledContainerWrapper>
        <div className="container">
          <Icon name="warehouse" width={20} height={20} />
          <Typography as={ReactTexty}
            type="button1"
            fontWeight={400}
          >
            
            {`${t('warehouses', { ns: 'menu' })}:`}
          </Typography>
        </div>
        <div className="container">
          {data &&
            (data?.type as string[])?.map((item: string) => (
              <Tag key={item} text={t(`${item}`, { ns: 'table' })} />
            ))
          }
        </div>
      </StyledContainerWrapper>
    </>
  )
}

export default WarehousesCard
