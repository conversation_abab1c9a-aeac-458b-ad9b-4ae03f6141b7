import { useNavigate } from 'react-router'
import { useDispatch } from 'react-redux'
import { object } from 'dot-object'
import { isObject } from '@aidsupply/components'
import merge from 'deepmerge'
import EditForm from '../../../components/RightPanelForms/EditForm'
import { useMappedState } from '../../../hooks'
import { sideBarUpsert } from '../../../redux-saga/reducers/sideBar'
import { selectSidebarInitialData } from '../../../redux-saga/selectors'
import { StateType } from '../../../components/Form/interfaces'
import { ITranslations } from '../../../locales'
import { reportsRightPanelConfig } from './reportsRightPanelConfig'

interface IReportsRightPanel {
  state?: StateType
  period?: string
  year?: string
  month?: {id: number, label: {uk: string, en: string}}
  organization_id?: Record<string, unknown>
  translations?: ITranslations
  [key: string]: unknown
}

const ReportsRightPanel = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const itemInitial = useMappedState(selectSidebarInitialData)

  const isReadOnly = false

  const onSubmit = (formValuesChanged: IReportsRightPanel) => {
    const requestBody: IReportsRightPanel = { ...object(formValuesChanged) }
    Object.keys(requestBody).forEach((key) => {
      const value = requestBody[key] as Record<string, unknown>
      if (isObject(value) && 'id' in value) {
        requestBody[key] = (value as { id: number }).id
      }
    })

    if (requestBody?.translations) {
      requestBody.translations = merge((itemInitial?.translations as ITranslations), requestBody?.translations)
      requestBody.name = requestBody.translations.en
    }

    const dataToSend = {
      id: itemInitial?.id,
      requestBody,
      type: 'reports',
    }

    dispatch(sideBarUpsert({ ...dataToSend, navigate }))
  }

  return <EditForm isReadOnly={isReadOnly} onSubmit={onSubmit} formData={reportsRightPanelConfig} customType="reports" />
}

export default ReportsRightPanel
