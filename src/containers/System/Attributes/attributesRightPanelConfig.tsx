import { slugify } from 'transliteration'
import { IFormConfig } from '../../../components/Form/interfaces'

export const attributesRightPanelConfig = (): IFormConfig => {
  return {
    withTabs: ['translations'],
    validationRules: {
      name: [
        'required',
        {
          type: 'keyInListAbsent',
          customRuleName: 'nameAlreadyExists',
          withSuccess: true,
          dataForValidation: {
            listKey: 'attributes',
            searchKey: 'slug',
            transformValue: (value) => slugify(value),
          },
        },
      ],
      attribute_type: ['required'],
      enumeration_id: ['required'],
      unit_id: ['required'],
      'translations.en': ['required'],
    },
    changingBlocksDependency: 'attribute_type',
    changingBlocks: {
      enum: {
        general: [
          {
            key: 'enumeration_id',
            label: 'enumeration',
            labelKey: 'translations',
            valueKey: 'id',
            noTranslation: true,
            required: true,
            component: 'dropdown',
            getOptions: (optionsData: Record<string, unknown>) => {
              return optionsData.enumerations
                ? Object.values(optionsData.enumerations).map((enumeration) => ({
                    id: enumeration.id,
                    translations: enumeration.translations,
                  }))
                : []
            },
          },
        ],
      },
      numeric: {
        general: [
          {
            key: 'unit_id',
            label: 'unit',
            valueKey: 'id',
            labelKey: 'translations',
            noTranslation: true,
            required: true,
            component: 'dropdown',
            getOptions: (optionsData: Record<string, unknown>) => {
              return optionsData.units
                ? Object.values(optionsData.units).map((unit) => ({
                    id: unit?.id,
                    translations: unit.translations,
                  }))
                : []
            },
          },
        ],
      },
      bool: { general: [] },
    },
    fields: {
      noTitle: [{
        key: 'state',
        label: 'state',
        optionsKeys: ['states'],
        component: 'dropdown',
      }],
      general: [
        {
          key: 'is_characteristic',
          label: 'isCharacteristic',
          component: 'checkbox',
        },
        {
          key: 'is_photo_group',
          label: 'isPhotoGroup',
          component: 'checkbox',
        },
        {
          key: 'name',
          label: 'systemNameInEnglish',
          required: true,
          disabledWhenNotEmpty: true,
          onInputValueChange: (value, setFormValues, initialValues) => {
            if (!initialValues?.id)
              // @ts-ignore
              setFormValues?.((prev) => {
                return { ...prev, 'translations.en': value }
              })
          },
        },
        {
          key: 'attribute_type',
          label: 'type',
          required: true,
          optionsKeys: ['attributeTypes'],
          component: 'dropdown',
        },
      ],
      translations: [
        {
          key: 'translations',
          label: 'title',
          placeholder: 'Your item title',
          required: true,
        },
      ]
    },
  }
}
