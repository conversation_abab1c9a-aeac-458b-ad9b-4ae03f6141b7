import { IFormConfig } from '../../../components/Form/interfaces'
import { ORGANIZATION_TYPES } from '../../../data'

export const organizationsRightPanelConfig = (mode: 'create' | 'edit'): IFormConfig => {
  return {
  withTabs: ['description'],
  validationRules: {
      name: ['required'],
      type: ['required'],
      phone: ['phone'],
      email: ['email'],
      country_id: ['required'],
      year_founded: ['year'],
  },
  fields: {
    ...(mode === 'edit' && {
      noTitle: [
        {
          key: 'status',
          label: 'status',
          optionsKeys: ['orgOrUsersStatuses'],
          component: 'dropdown',
        },
        {
          key: 'roles',
          label: 'roles',
          component: 'dropdown',
          optionsKeys: ['organizationRoles'],
          isMulti: true,
        },
      ],
      // TODO: create another ticket to add this field
      // supervised_organizations: [],
    }),
    general: [
      {
        key: 'name',
        label: 'name',
        className: 'firstInOrder',
        required: true,
      },
      {
        key: 'name_en',
        label: 'nameInEnglish',
      },
      {
        key: 'type',
        label: 'type',
        component: 'dropdown',
        options: ORGANIZATION_TYPES,
        required: true,
      },
      {
        key: 'year_founded',
        label: 'yearFounded',
        type: 'number',
        hideButtons: true,
      },
      {
        key: 'reg_number',
        label: 'registrationNumber',
      },
      {
        key: 'website',
        label: 'website',
        placeholder: 'https://domain.com',
      },
      {
        key: 'email',
        label: 'email',
        placeholder: '<EMAIL>',
      },
      {
        key: 'video_url',
        label: 'introductionVideo',
      },
    ],
    address: [
      {
        key: 'country_id',
        label: 'country',
        component: 'dropdown',
        optionsKeys: ['countries'],
        labelKey: 'translations',
        required: true,
      },
      {
        key: 'region_id',
        label: 'region',
        component: 'dropdown',
        optionsKeys: ['country_subdivisions'],
        labelKey: 'translations',
        isClearable: true,
      },
      {
        key: 'city',
        label: 'city',
      },
      {
        key: 'address_line',
        label: 'addressLine',
      },
    ]
    },
  }
}
