import { IOptionData } from '../components/Form/interfaces'
import { getMonths, getYears } from '../utils/dates'


export const DEFAULT_TABLE_SORTING: Record<string, {[key: string]: 'asc' | 'desc'}> = {
  warehouses: { name: 'asc' },
}

export const ATTRIBUTE_TYPES: IOptionData[] = [
  { id: 'numeric', label: { en: 'Number', uk: 'Число' } },
  { id: 'enum', label: { en: 'Enumeration', uk: 'Перелік' } },
  { id: 'bool', label: { en: 'Boolean', uk: 'Булеве значення' } },
]

export const CONTRACT_TYPES: IOptionData[] = [
  { id: 'payment', label: { en: 'Payment', uk: 'Оплата' } },
  { id: 'shipment', label: { en: 'Shipment', uk: 'Відвантаження' } },
]

export const ORGANIZATION_TYPES: IOptionData[] = [
  {
    id: 'private',
    label: { en: 'Private', uk: 'Приватний' },
  },
  {
    id: 'hospital',
    label: { en: 'Hospital', uk: 'Лікарня' },
  },
  {
    id: 'military',
    label: { en: 'Military', uk: 'Військовий' },
  },
  {
    id: 'business',
    label: { en: 'Business', uk: 'Бізнес' },
  },
  {
    id: 'governmental',
    label: { en: 'Governmental', uk: 'Державний' },
  },
  {
    id: 'fund',
    label: { en: 'Fund', uk: 'Фонд' },
  },
  {
    id: 'ngo',
    label: { en: 'NGO', uk: 'НДО' },
  },
]

export const MAGNITUDES: IOptionData[] = [
  { id: 'amperage', label: { en: 'Amperage', uk: 'Сила струму' } },
  { id: 'density', label: { en: 'Density', uk: 'Щільність' } },
  { id: 'force', label: { en: 'Force', uk: 'Сила' } },
  { id: 'frequency', label: { en: 'Frequency', uk: 'Частота' } },
  { id: 'length', label: { en: 'Length', uk: 'Довжина' } },
  { id: 'mass', label: { en: 'Mass', uk: 'Маса' } },
  { id: 'options', label: { en: 'Options', uk: 'Опції' } },
  { id: 'power', label: { en: 'Power', uk: 'Потужність' } },
  { id: 'pressure', label: { en: 'Pressure', uk: 'Тиск' } },
  { id: 'resistance', label: { en: 'Resistance', uk: 'Опір' } },
  { id: 'speed', label: { en: 'Speed', uk: 'Швидкість' } },
  { id: 'square', label: { en: 'Square', uk: 'Площа' } },
  { id: 'temperature', label: { en: 'Temperature', uk: 'Температура' } },
  { id: 'time', label: { en: 'Time', uk: 'Час' } },
  { id: 'voltage', label: { en: 'Voltage', uk: 'Напруга' } },
  { id: 'volume', label: { en: 'Volume', uk: "Об'єм" } },
]

export const PAGE_TYPES: IOptionData[] = [
  { id: 'cookies', label: '/cookies-policy' },
  { id: 'privacy', label: '/privacy-policy' },
  { id: 'terms', label: '/terms-and-conditions' },
]

export const WAREHOUSE_TYPES: IOptionData[] = [
  { id: 'retail', label: { en: 'Retail', uk: 'Роздрібний' } },
  { id: 'wholesale', label: { en: 'Wholesale', uk: 'Гуртовий' } },
]

export const VISIBILITY_TYPES: IOptionData[] = [
  { id: 'team', label: { en: 'Organization', uk: 'Організація' } },
  { id: 'community', label: { en: 'Community', uk: 'Спільнота' } },
  { id: 'public', label: { en: 'Public', uk: 'Загальний' } },
]

export const REPORT_TYPES: IOptionData[] = [
  { id: 'monthly', label: { en: 'Monthly', uk: 'Місячний' } },
  { id: 'annual', label: { en: 'Annual', uk: 'Річний' } },
]

export const ENUMERATION_ADDONS: IOptionData[] = [
  {
    id: 'none',
    label: {
      en: 'None',
      uk: 'Немає',
    },
  },
  {
    id: 'icon',
    label: {
      en: 'Icon',
      uk: 'Іконка',
    },
  },
  {
    id: 'image',
    label: {
      en: 'Image',
      uk: 'Зображення',
    },
  },
  {
    id: 'hex_and_image',
    label: {
      en: 'Hex or Image',
      uk: 'Колір чи зображення',
    },
  },
]

export const TAXES_TYPE: IOptionData[] = [
  {
    id: 'none',
    label: {
      en: 'None',
      uk: 'Відсутній',
    },
  },
  {
    id: 'vat_7',
    label: {
      en: 'VAT 7%',
      uk: 'ПДВ 7%',
    },
  },
  {
    id: 'vat_14',
    label: {
      en: 'VAT 14%',
      uk: 'ПДВ 14%',
    },
  },
  {
    id: 'vat_20',
    label: {
      en: 'VAT 20%',
      uk: 'ПДВ 20%',
    },
  },
]

export const STOCK_LEVELS: IOptionData[] = [
  // TODO: add stock levels, check id after BE will be updated
  {
    id: 'set_stock_level',
    label: { en: 'Set stock level', uk: 'Встановити рівень запасів' }
  },
]

export const MONTHS = getMonths()

export const YEARS = getYears(2000)

export const FORM_TABLE_OPTIONS = [
  {
    id: 'toEdit',
    label: {
      en: 'Edit',
      uk: 'Редагувати',
    },
    iconName: 'draft',
  },
  {
    id: 'toDelete',
    label: { en: 'Delete', uk: 'Видалити' },
    iconName: 'trashBin2',
  },
]
