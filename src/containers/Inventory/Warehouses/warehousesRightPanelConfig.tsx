import { CURRENCY } from '../../../components/config/forms'
import { IFormConfig } from '../../../components/Form/interfaces'
import { ICurrentUser } from '../../../redux-saga/reducers/user'

export const warehousesRightPanelConfig: IFormConfig = {
  withTabs: [],
  validationRules: {
    name: ['required'],
    type: ['required'],
    currency_id: ['required'],
    country_id: ['required'],
    organization_id: ['required'],
  },
  fields: {
    noTitle: [{
      key: 'state',
      label: 'state',
      optionsKeys: ['states'],
      component: 'dropdown',
    }],
    general: [
      {
        key: 'name',
        label: 'name',
        required: true,
      },
      {
        key: 'type',
        label: 'types',
        isMulti: true,
        labelKey: 'label',
        required: true,
        optionsKeys: ['warehouseTypes'],
        component: 'dropdown',
      },
      {
        ...CURRENCY,
      },
      {
        key: 'organization_id',
        label: 'organization',
        noTranslation: true,
        labelKey: 'name',
        valueKey: 'id',
        optionsKeys: ['organizations'],
        component: 'dropdown',
        isClearable: true,
        required: true,
        getDisabled: (_formValues: Record<string, unknown>, optionsData: Record<string, unknown>) => {
          const fieldsEditableRoles = ['administrator', 'system']
          const role = (optionsData.user as ICurrentUser)?.role as string
          return !fieldsEditableRoles.includes(role)
        },
      },
    ],
    address: [
      {
        key: 'country_id',
        label: 'country',
        component: 'dropdown',
        optionsKeys: ['countries'],
        labelKey: 'translations',
        required: true,
      },
      {
        key: 'region_id',
        label: 'region',
        component: 'dropdown',
        optionsKeys: ['country_subdivisions'],
        labelKey: 'translations',
      },
      {
        key: 'city',
        label: 'city',
      },
    ],
  },
}
